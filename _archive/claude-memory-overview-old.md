# Sub-Agent Management System

This project uses a self-managing sub-agent system located in `acumen-fap/ai/claude/agents/`. The naming convention is - singular "agent" for individual configs, plural "agents" for system-wide files.

## Core Files

- **`agents-index.md`** - Living registry of all available agents organized by domain
- **`agents-management-instructions.md`** - How to add, update, and remove agents
- **`agents-creation-guidelines.md`** - Standards and templates for creating quality agents
- **`claude-memory-overview.md`** - This overview file for CLAUDE.md integration

## Agent Configurations

Individual agent specifications in `agent-specifications/`:

- **`agent-manager-config.md`** - Creates and maintains agents and the index
- **`agent-settings-manager.md`** - Modifies Claude Code configuration files
- **`agent-markdown-specialist.md`** - Enhanced documentation formatting and living docs

## Usage

Use `/agents` to access the management interface or invoke agents explicitly: `"agent-name: task description"`.

Key agents for Acumen FAP infrastructure:

- `agent-manager` - Agent lifecycle management and index updates
- `monorepo-architect` - PNPM workspace design and dependency management
- `docs-manager` - Technical documentation and API specifications
- `pear-specialist` - P2P application development using Holepunch ecosystem

All agents follow functional programming principles with ES6+ patterns, immutable data structures, and modern async/await syntax. The `agent-manager` can create new agents, update existing ones, and maintain the index. The `settings-manager` handles Claude Code configuration changes.
