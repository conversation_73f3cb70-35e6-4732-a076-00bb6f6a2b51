# GitHub Pages Setup for FAP Demo

This guide shows how to publish the `github-demo` app to GitHub Pages for public demonstration.

## 🎯 Strategy

Since GitHub Pages serves static files and our packages are in `node_modules`, we need to create a self-contained version that includes the package assets directly.

## 📁 Directory Structure

```
docs/
├── index.html          # Main demo page
├── css/               # Copied from @acumen-desktop packages
│   ├── fap-theme.css
│   ├── fap-utils.css
│   ├── fap-chat.css
│   ├── fap-message.css
│   └── fap-tooltip.css
├── js/                # Copied from @acumen-desktop packages
│   ├── fap-chat-*.js
│   └── fap-tooltip.js
└── github-demo.js     # Demo app logic
```

## 🔧 Setup Steps

### 1. Enable GitHub Pages
1. Go to repository Settings
2. Scroll to "Pages" section
3. Source: "Deploy from a branch"
4. Branch: "main"
5. Folder: "/docs"

### 2. Copy Package Assets
Since GitHub Pages can't access `node_modules`, we copy the published package assets to `docs/` for static serving.

### 3. Update Paths
Update HTML to reference the copied assets instead of `node_modules` paths.

## 🌐 Result

The demo will be available at:
`https://acumen-desktop.github.io/fap_monorepo_july_2025/`

This creates a public showcase of:
- Published packages working in production
- GitHub Packages integration
- Professional component library demonstration
- "Complexity-free" web development philosophy

## 🔄 Updating

When packages are updated:
1. Re-copy assets from `node_modules/@acumen-desktop/*`
2. Commit and push changes
3. GitHub Pages automatically rebuilds

---

*This completes the full GitHub ecosystem test: private packages + public demo!*
