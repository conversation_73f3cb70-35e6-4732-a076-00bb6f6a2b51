// FapChat Input Module
// Enhanced content-editable input component

(function() {
  'use strict';

  // Initialize namespace
  window.fap = window.fap || {};
  window.fap.chat = window.fap.chat || {};

  let currentInput = null;
  let submitCallback = null;

  // Input functions
  const create = (inputElement, options = {}) => {
    if (!inputElement) return null;

    // Make the chat-input element itself contenteditable
    inputElement.contentEditable = true;
    inputElement.setAttribute('role', 'textbox');
    inputElement.setAttribute('aria-multiline', 'true');
    
    if (options.placeholder) {
      inputElement.setAttribute('placeholder', options.placeholder);
    }

    currentInput = inputElement;

    // Set up event handlers
    setupEventHandlers(inputElement);
    
    // Initial resize
    autoResize(inputElement);
    
    return inputElement;
  };

  const setupEventHandlers = (inputArea) => {
    // Handle keyboard events with proper key detection
    inputArea.addEventListener('keydown', (event) => {
      if (event.key === 'Enter') {
        if (event.shiftKey) {
          // Shift+Enter: insert new line (allow default behavior)
          return;
        } else {
          // Enter alone: submit message
          event.preventDefault();
          handleSubmit();
        }
      }
    });

    // Handle input changes for auto-resize
    inputArea.addEventListener('input', () => {
      autoResize(inputArea);
    });

    // Handle paste events
    inputArea.addEventListener('paste', (event) => {
      event.preventDefault();
      
      // Try to get formatted text first, fallback to plain text
      const htmlData = event.clipboardData.getData('text/html');
      const textData = event.clipboardData.getData('text/plain');
      
      if (htmlData) {
        // Clean up HTML and insert
        const cleanHtml = htmlData
          .replace(/<script[^>]*>.*?<\/script>/gi, '') // Remove scripts
          .replace(/<style[^>]*>.*?<\/style>/gi, '')   // Remove styles
          .replace(/on\w+="[^"]*"/gi, '');             // Remove event handlers
        
        document.execCommand('insertHTML', false, cleanHtml);
      } else {
        // Fallback to plain text with line break conversion
        const textWithBreaks = textData.replace(/\n/g, '<br>');
        document.execCommand('insertHTML', false, textWithBreaks);
      }
      
      autoResize(inputArea);
    });

    // Handle focus/blur for placeholder
    inputArea.addEventListener('focus', () => {
      if (inputArea.textContent.trim() === '') {
        inputArea.classList.add('focused');
      }
    });

    inputArea.addEventListener('blur', () => {
      inputArea.classList.remove('focused');
    });
  };

  const handleSubmit = () => {
    const content = getValue();
    if (content.trim() && submitCallback) {
      submitCallback(content);
      clear();
    }
  };

  const autoResize = (inputArea) => {
    // Reset height to calculate new height
    const minHeight = 60;
    const maxHeight = 200;
    
    // Temporarily set height to auto to get scroll height
    inputArea.style.height = minHeight + 'px';
    const scrollHeight = inputArea.scrollHeight;
    
    // Set new height within bounds
    const newHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));
    inputArea.style.height = newHeight + 'px';
    
    // Show scrollbar if content exceeds max height
    inputArea.style.overflowY = scrollHeight > maxHeight ? 'auto' : 'hidden';
  };

  const getValue = () => {
    if (!currentInput) return '';
    
    // Get HTML content to preserve formatting
    const htmlContent = currentInput.innerHTML;
    
    // Convert div tags to line breaks for better text representation
    const textWithBreaks = htmlContent
      .replace(/<div>/gi, '\n')
      .replace(/<\/div>/gi, '')
      .replace(/<br\s*\/?>/gi, '\n')
      .replace(/<[^>]*>/g, '') // Remove other HTML tags
      .trim();
    
    return textWithBreaks || '';
  };

  const setValue = (value) => {
    if (currentInput) {
      currentInput.textContent = value;
      autoResize(currentInput);
    }
  };

  const clear = () => {
    if (currentInput) {
      currentInput.textContent = '';
      autoResize(currentInput);
    }
  };

  const focus = () => {
    if (currentInput) {
      currentInput.focus();
    }
  };

  const onSubmit = (callback) => {
    submitCallback = callback;
  };

  // Export public interface
  window.fap.chat.input = {
    create,
    getValue,
    setValue,
    clear,
    focus,
    onSubmit
  };

})();