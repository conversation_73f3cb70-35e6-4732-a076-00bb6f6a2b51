// FapChat Plugin System
// Extensibility infrastructure for the FapChat ecosystem

(function () {
  'use strict';

  // Initialize namespace
  window.fap = window.fap || {};
  window.fap.chat = window.fap.chat || {};

  // Plugin registry and management
  const plugins = {
    registry: new Map(),
    hooks: new Map(),

    // Register a plugin
    register: (name, plugin) => {
      if (plugins.registry.has(name)) {
        console.warn(`Plugin "${name}" is already registered`);
        return false;
      }

      // Validate plugin structure
      if (!plugin || typeof plugin !== 'object') {
        console.error(`Plugin "${name}" must be an object`);
        return false;
      }

      // Set plugin metadata
      plugin._name = name;
      plugin._registered = Date.now();

      // Initialize plugin if it has an init method
      try {
        if (typeof plugin.init === 'function') {
          plugin.init();
        }

        // Register plugin events/hooks
        if (plugin.hooks && typeof plugin.hooks === 'object') {
          Object.keys(plugin.hooks).forEach(hookName => {
            plugins.addHook(hookName, plugin.hooks[hookName], plugin._name);
          });
        }

        plugins.registry.set(name, plugin);
        plugins.emit('plugin:registered', { name, plugin });

        console.log(`Plugin "${name}" registered successfully`);
        return true;

      } catch (error) {
        console.error(`Failed to initialize plugin "${name}":`, error);
        return false;
      }
    },

    // Unregister a plugin
    unregister: (name) => {
      const plugin = plugins.registry.get(name);
      if (!plugin) {
        console.warn(`Plugin "${name}" is not registered`);
        return false;
      }

      try {
        // Remove plugin hooks
        plugins.hooks.forEach((hookList, hookName) => {
          plugins.hooks.set(hookName, hookList.filter(hook => hook.plugin !== name));
        });

        // Call plugin destroy method if it exists
        if (typeof plugin.destroy === 'function') {
          plugin.destroy();
        }

        plugins.registry.delete(name);
        plugins.emit('plugin:unregistered', { name, plugin });

        console.log(`Plugin "${name}" unregistered successfully`);
        return true;

      } catch (error) {
        console.error(`Failed to unregister plugin "${name}":`, error);
        return false;
      }
    },

    // Get a registered plugin
    get: (name) => {
      return plugins.registry.get(name);
    },

    // List all registered plugins
    list: () => {
      return Array.from(plugins.registry.keys());
    },

    // Check if plugin is registered
    has: (name) => {
      return plugins.registry.has(name);
    },

    // Add a hook for a specific event
    addHook: (hookName, callback, pluginName = 'anonymous', priority = 10) => {
      if (!plugins.hooks.has(hookName)) {
        plugins.hooks.set(hookName, []);
      }

      const hookList = plugins.hooks.get(hookName);
      hookList.push({
        callback,
        plugin: pluginName,
        priority,
        added: Date.now()
      });

      // Sort by priority (lower numbers = higher priority)
      hookList.sort((a, b) => a.priority - b.priority);

      return true;
    },

    // Remove a hook
    removeHook: (hookName, callback) => {
      if (!plugins.hooks.has(hookName)) return false;

      const hookList = plugins.hooks.get(hookName);
      const index = hookList.findIndex(hook => hook.callback === callback);

      if (index > -1) {
        hookList.splice(index, 1);
        return true;
      }

      return false;
    },

    // Emit an event to all registered hooks
    emit: (hookName, data = {}) => {
      if (!plugins.hooks.has(hookName)) return data;

      const hookList = plugins.hooks.get(hookName);
      let result = data;

      for (const hook of hookList) {
        try {
          const hookResult = hook.callback(result, hookName);
          // If hook returns a value, use it as the new data
          if (hookResult !== undefined) {
            result = hookResult;
          }
        } catch (error) {
          console.error(`Hook error in plugin "${hook.plugin}" for event "${hookName}":`, error);
        }
      }

      return result;
    },

    // Apply filters (hooks that modify data)
    applyFilters: (filterName, value, ...args) => {
      if (!plugins.hooks.has(filterName)) return value;

      const hookList = plugins.hooks.get(filterName);
      let result = value;

      for (const hook of hookList) {
        try {
          const filterResult = hook.callback(result, ...args);
          if (filterResult !== undefined) {
            result = filterResult;
          }
        } catch (error) {
          console.error(`Filter error in plugin "${hook.plugin}" for filter "${filterName}":`, error);
        }
      }

      return result;
    },

    // Execute actions (hooks that don't modify data)
    doAction: (actionName, ...args) => {
      if (!plugins.hooks.has(actionName)) return;

      const hookList = plugins.hooks.get(actionName);

      for (const hook of hookList) {
        try {
          hook.callback(...args);
        } catch (error) {
          console.error(`Action error in plugin "${hook.plugin}" for action "${actionName}":`, error);
        }
      }
    }
  };

  // Export plugin system
  window.fap.chat.plugins = plugins;

  // Core hooks that plugins can use
  const coreHooks = [
    'terminal:init',
    'terminal:destroy',
    'message:before-send',
    'message:after-send',
    'message:before-add',
    'message:after-add',
    'input:before-submit',
    'input:after-submit',
    'input:before-clear',
    'input:after-clear',
    'display:before-render',
    'display:after-render',
    'display:before-scroll',
    'display:after-scroll'
  ];

  // Initialize core hook points
  coreHooks.forEach(hookName => {
    plugins.hooks.set(hookName, []);
  });

  // Plugin development helper
  window.fap.chat.createPlugin = (name, config = {}) => {
    return {
      name,
      version: config.version || '1.0.0',
      description: config.description || '',
      author: config.author || '',

      init() {
        console.log(`Initializing plugin: ${name}`);
        if (config.init) config.init.call(this);
      },

      destroy() {
        console.log(`Destroying plugin: ${name}`);
        if (config.destroy) config.destroy.call(this);
      },

      hooks: config.hooks || {},

      // Helper methods for plugins
      addHook: (hookName, callback, priority) => {
        return plugins.addHook(hookName, callback, name, priority);
      },

      removeHook: (hookName, callback) => {
        return plugins.removeHook(hookName, callback);
      },

      emit: (hookName, data) => {
        return plugins.emit(hookName, data);
      }
    };
  };

})();