// FapChat Storage System
// IndexedDB wrapper with search capabilities for message persistence

(function() {
  'use strict';

  // Initialize namespace
  window.fap = window.fap || {};
  window.fap.chat = window.fap.chat || {};

  // Storage system with multiple adapters
  const storage = {
    adapters: new Map(),
    currentAdapter: 'memory',
    
    // Register a storage adapter
    registerAdapter: (name, adapter) => {
      storage.adapters.set(name, adapter);
      console.log(`Storage adapter "${name}" registered`);
    },
    
    // Set active adapter
    setAdapter: async (name) => {
      if (!storage.adapters.has(name)) {
        throw new Error(`Storage adapter "${name}" not found`);
      }
      
      const adapter = storage.adapters.get(name);
      
      // Initialize adapter if needed
      if (adapter.init && !adapter._initialized) {
        await adapter.init();
        adapter._initialized = true;
      }
      
      storage.currentAdapter = name;
      console.log(`Storage adapter set to: ${name}`);
    },
    
    // Get current adapter
    getAdapter: () => {
      return storage.adapters.get(storage.currentAdapter);
    },
    
    // Storage operations
    save: async (key, data) => {
      const adapter = storage.getAdapter();
      return adapter.save(key, data);
    },
    
    load: async (key) => {
      const adapter = storage.getAdapter();
      return adapter.load(key);
    },
    
    delete: async (key) => {
      const adapter = storage.getAdapter();
      return adapter.delete(key);
    },
    
    search: async (query, options = {}) => {
      const adapter = storage.getAdapter();
      if (adapter.search) {
        return adapter.search(query, options);
      }
      throw new Error('Current storage adapter does not support search');
    },
    
    list: async (options = {}) => {
      const adapter = storage.getAdapter();
      if (adapter.list) {
        return adapter.list(options);
      }
      throw new Error('Current storage adapter does not support listing');
    }
  };

  // Memory adapter (default, for testing)
  const memoryAdapter = {
    data: new Map(),
    
    save: async (key, data) => {
      memoryAdapter.data.set(key, {
        ...data,
        _saved: Date.now(),
        _key: key
      });
      return true;
    },
    
    load: async (key) => {
      return memoryAdapter.data.get(key) || null;
    },
    
    delete: async (key) => {
      return memoryAdapter.data.delete(key);
    },
    
    search: async (query, options = {}) => {
      const results = [];
      const searchTerm = query.toLowerCase();
      
      memoryAdapter.data.forEach((item, key) => {
        if (item.content && item.content.toLowerCase().includes(searchTerm)) {
          results.push(item);
        }
      });
      
      return results.slice(0, options.limit || 100);
    },
    
    list: async (options = {}) => {
      const items = Array.from(memoryAdapter.data.values());
      return items.slice(0, options.limit || 100);
    }
  };

  // IndexedDB adapter (persistent storage with search)
  const indexedDBAdapter = {
    dbName: 'FapChatDB',
    dbVersion: 1,
    storeName: 'messages',
    db: null,
    
    init: async () => {
      return new Promise((resolve, reject) => {
        const request = indexedDB.open(indexedDBAdapter.dbName, indexedDBAdapter.dbVersion);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
          indexedDBAdapter.db = request.result;
          resolve();
        };
        
        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          
          // Create messages store
          if (!db.objectStoreNames.contains(indexedDBAdapter.storeName)) {
            const store = db.createObjectStore(indexedDBAdapter.storeName, { keyPath: 'id' });
            
            // Create indexes for search
            store.createIndex('content', 'content', { unique: false });
            store.createIndex('type', 'type', { unique: false });
            store.createIndex('timestamp', 'timestamp', { unique: false });
            store.createIndex('saved', '_saved', { unique: false });
          }
        };
      });
    },
    
    save: async (key, data) => {
      return new Promise((resolve, reject) => {
        const transaction = indexedDBAdapter.db.transaction([indexedDBAdapter.storeName], 'readwrite');
        const store = transaction.objectStore(indexedDBAdapter.storeName);
        
        const saveData = {
          id: key,
          ...data,
          _saved: Date.now(),
          _key: key
        };
        
        const request = store.put(saveData);
        request.onsuccess = () => resolve(true);
        request.onerror = () => reject(request.error);
      });
    },
    
    load: async (key) => {
      return new Promise((resolve, reject) => {
        const transaction = indexedDBAdapter.db.transaction([indexedDBAdapter.storeName], 'readonly');
        const store = transaction.objectStore(indexedDBAdapter.storeName);
        
        const request = store.get(key);
        request.onsuccess = () => resolve(request.result || null);
        request.onerror = () => reject(request.error);
      });
    },
    
    delete: async (key) => {
      return new Promise((resolve, reject) => {
        const transaction = indexedDBAdapter.db.transaction([indexedDBAdapter.storeName], 'readwrite');
        const store = transaction.objectStore(indexedDBAdapter.storeName);
        
        const request = store.delete(key);
        request.onsuccess = () => resolve(true);
        request.onerror = () => reject(request.error);
      });
    },
    
    search: async (query, options = {}) => {
      return new Promise((resolve, reject) => {
        const transaction = indexedDBAdapter.db.transaction([indexedDBAdapter.storeName], 'readonly');
        const store = transaction.objectStore(indexedDBAdapter.storeName);
        const index = store.index('content');
        
        const results = [];
        const searchTerm = query.toLowerCase();
        
        const request = index.openCursor();
        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (cursor) {
            const item = cursor.value;
            if (item.content && item.content.toLowerCase().includes(searchTerm)) {
              results.push(item);
              if (results.length >= (options.limit || 100)) {
                resolve(results);
                return;
              }
            }
            cursor.continue();
          } else {
            resolve(results);
          }
        };
        request.onerror = () => reject(request.error);
      });
    },
    
    list: async (options = {}) => {
      return new Promise((resolve, reject) => {
        const transaction = indexedDBAdapter.db.transaction([indexedDBAdapter.storeName], 'readonly');
        const store = transaction.objectStore(indexedDBAdapter.storeName);
        const index = store.index('saved');
        
        const results = [];
        const request = index.openCursor(null, 'prev'); // Most recent first
        
        request.onsuccess = (event) => {
          const cursor = event.target.result;
          if (cursor && results.length < (options.limit || 100)) {
            results.push(cursor.value);
            cursor.continue();
          } else {
            resolve(results);
          }
        };
        request.onerror = () => reject(request.error);
      });
    }
  };

  // Register adapters
  storage.registerAdapter('memory', memoryAdapter);
  storage.registerAdapter('indexeddb', indexedDBAdapter);

  // Try to use IndexedDB by default, fallback to memory
  (async () => {
    try {
      if (typeof indexedDB !== 'undefined') {
        await storage.setAdapter('indexeddb');
      } else {
        console.warn('IndexedDB not available, using memory storage');
      }
    } catch (error) {
      console.warn('Failed to initialize IndexedDB, using memory storage:', error);
    }
  })();

  // Export storage system
  window.fap.chat.storage = storage;

  // Helper functions for message operations
  window.fap.chat.messages = {
    // Save a message
    save: async (message) => {
      const messageId = message.id || `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const saveData = {
        ...message,
        id: messageId,
        _saved: Date.now()
      };
      
      await storage.save(messageId, saveData);
      
      // Emit save event for plugins
      if (window.fap.chat.plugins) {
        window.fap.chat.plugins.doAction('message:saved', saveData);
      }
      
      return messageId;
    },
    
    // Delete a message
    delete: async (messageId) => {
      const result = await storage.delete(messageId);
      
      // Emit delete event for plugins
      if (window.fap.chat.plugins) {
        window.fap.chat.plugins.doAction('message:deleted', { id: messageId });
      }
      
      return result;
    },
    
    // Search messages
    search: async (query, options = {}) => {
      return storage.search(query, options);
    },
    
    // List saved messages
    list: async (options = {}) => {
      return storage.list(options);
    },
    
    // Load a specific message
    load: async (messageId) => {
      return storage.load(messageId);
    }
  };

})();