// FapChat API Module
// Main integration interface that composes all modules

(function() {
  'use strict';

  // Initialize namespace
  window.fap = window.fap || {};
  window.fap.chat = window.fap.chat || {};

  // Terminal instance factory
  const create = (parentElement, options = {}) => {
    if (!parentElement) {
      throw new Error('Parent element is required');
    }

    // Apply plugin filters to options
    const filteredOptions = window.fap.chat.plugins ? 
      window.fap.chat.plugins.applyFilters('terminal:options', options) : options;

    // Initialize state
    const state = window.fap.chat.state.create();
    const eventBus = window.fap.chat.events.createEventBus();
    
    // Get display and input elements
    const displayElement = parentElement.querySelector('chat-display');
    const inputElement = parentElement.querySelector('chat-input');
    
    if (!displayElement || !inputElement) {
      throw new Error('chat-display and chat-input elements are required');
    }

    // Emit terminal init hook
    if (window.fap.chat.plugins) {
      window.fap.chat.plugins.doAction('terminal:init', { parentElement, options: filteredOptions });
    }

    // Initialize input component (now the chat-input element itself)
    const inputComponent = window.fap.chat.input.create(inputElement, filteredOptions);
    
    // Set up message handling
    window.fap.chat.input.onSubmit((content) => {
      // Apply input filters
      const filteredContent = window.fap.chat.plugins ? 
        window.fap.chat.plugins.applyFilters('input:before-submit', content) : content;
      
      if (filteredContent && filteredContent.trim()) {
        eventBus.emit('message', { content: filteredContent, type: 'user' });
        
        // Emit input submitted action
        if (window.fap.chat.plugins) {
          window.fap.chat.plugins.doAction('input:after-submit', filteredContent);
        }
      }
    });

    // Handle message events
    eventBus.on('message', (message) => {
      add(message.content, message.type);
    });

    // Terminal instance methods
    const send = (content) => {
      if (content && content.trim()) {
        eventBus.emit('message', { content, type: 'user' });
      }
    };

    const add = (content, type = 'system') => {
      // Apply message filters
      const messageData = { 
        content, 
        type,
        id: `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        timestamp: new Date().toISOString(),
        isSaved: false
      };
      const filteredMessage = window.fap.chat.plugins ? 
        window.fap.chat.plugins.applyFilters('message:before-add', messageData) : messageData;
      
      const newState = window.fap.chat.state.addItem(state, filteredMessage);
      Object.assign(state, newState);
      
      window.fap.chat.display.addItem(displayElement, filteredMessage, filteredMessage.type);
      
      // Emit message added action
      if (window.fap.chat.plugins) {
        window.fap.chat.plugins.doAction('message:after-add', filteredMessage);
      }
    };

    const clear = () => {
      // Emit before clear action
      if (window.fap.chat.plugins) {
        window.fap.chat.plugins.doAction('input:before-clear');
      }
      
      const newState = window.fap.chat.state.clear(state);
      Object.assign(state, newState);
      
      window.fap.chat.display.clear(displayElement);
      window.fap.chat.input.clear();
      
      // Emit after clear action
      if (window.fap.chat.plugins) {
        window.fap.chat.plugins.doAction('input:after-clear');
      }
    };

    const focus = () => {
      window.fap.chat.input.focus();
    };

    const onMessage = (callback) => {
      eventBus.on('message', callback);
    };

    const destroy = () => {
      // Emit terminal destroy action
      if (window.fap.chat.plugins) {
        window.fap.chat.plugins.doAction('terminal:destroy', { parentElement, state });
      }
      
      // Cleanup
      if (parentElement) {
        parentElement.innerHTML = '';
      }
    };

    // Return terminal instance
    return {
      send,
      add,
      clear,
      focus,
      onMessage,
      destroy
    };
  };

  const destroy = (instance) => {
    if (instance && instance.destroy) {
      instance.destroy();
    }
  };

  // Export public interface
  window.fap.chat.Terminal = {
    create,
    destroy
  };

})();