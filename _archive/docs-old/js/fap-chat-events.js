// FapChat Event Handling Module
// Generic event utilities for interactive components

(function() {
  'use strict';

  // Initialize namespace
  window.fap = window.fap || {};
  window.fap.chat = window.fap.chat || {};

  // Event binding functions
  const bindKeyboard = (element, handlers) => {
    const keyHandler = (event) => {
      try {
        const key = event.key;
        const combo = [
          event.ctrlKey && 'ctrl',
          event.shiftKey && 'shift',
          event.altKey && 'alt',
          key.toLowerCase()
        ].filter(Boolean).join('+');

        if (handlers[combo]) {
          handlers[combo](event);
        } else if (handlers[key]) {
          handlers[key](event);
        }
      } catch (error) {
        console.warn('Keyboard event handler error:', error);
      }
    };

    element.addEventListener('keydown', keyHandler);
    return () => element.removeEventListener('keydown', keyHandler);
  };

  const bindMouse = (element, handlers) => {
    const mouseHandlers = {};
    
    Object.keys(handlers).forEach(event => {
      mouseHandlers[event] = (e) => {
        try {
          handlers[event](e);
        } catch (error) {
          console.warn(`Mouse ${event} handler error:`, error);
        }
      };
      element.addEventListener(event, mouseHandlers[event]);
    });

    return () => {
      Object.keys(mouseHandlers).forEach(event => {
        element.removeEventListener(event, mouseHandlers[event]);
      });
    };
  };

  const bindScroll = (element, handlers) => {
    const scrollHandler = (event) => {
      try {
        if (handlers.scroll) handlers.scroll(event);
      } catch (error) {
        console.warn('Scroll handler error:', error);
      }
    };

    element.addEventListener('scroll', scrollHandler);
    return () => element.removeEventListener('scroll', scrollHandler);
  };

  // Simple event bus for internal communication
  const createEventBus = () => {
    const listeners = {};

    return {
      on: (event, callback) => {
        if (!listeners[event]) listeners[event] = [];
        listeners[event].push(callback);
      },
      emit: (event, data) => {
        if (listeners[event]) {
          listeners[event].forEach(callback => {
            try {
              callback(data);
            } catch (error) {
              console.warn(`Event bus error for ${event}:`, error);
            }
          });
        }
      },
      off: (event, callback) => {
        if (listeners[event]) {
          listeners[event] = listeners[event].filter(cb => cb !== callback);
        }
      }
    };
  };

  // Export public interface
  window.fap.chat.events = {
    bindKeyboard,
    bindMouse,
    bindScroll,
    createEventBus
  };

})();