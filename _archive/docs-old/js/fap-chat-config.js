// FapChat Configuration System
// Manages settings and configuration for core and plugins

(function() {
  'use strict';

  // Initialize namespace
  window.fap = window.fap || {};
  window.fap.chat = window.fap.chat || {};

  // Configuration schema and management
  const config = {
    // Default configuration schema
    schema: {
      core: {
        maxMessages: { type: 'number', default: 1000, min: 10, max: 10000 },
        autoScroll: { type: 'boolean', default: true },
        placeholder: { type: 'string', default: 'Type your message...' },
        debug: { type: 'boolean', default: false }
      },
      display: {
        collapseThreshold: { 
          type: 'object', 
          default: { lines: 10, chars: 800 },
          validate: (value) => value && typeof value.lines === 'number' && typeof value.chars === 'number'
        },
        showTimestamps: { type: 'boolean', default: false },
        theme: { type: 'string', default: 'dark', options: ['dark', 'light', 'auto'] },
        animateScroll: { type: 'boolean', default: true }
      },
      input: {
        maxHeight: { type: 'string', default: '50%' },
        minHeight: { type: 'string', default: '60px' },
        preserveFormatting: { type: 'boolean', default: true },
        enableShortcuts: { type: 'boolean', default: true }
      },
      plugins: { type: 'object', default: {} }
    },
    
    // Current configuration values
    values: {},
    
    // Initialize with defaults
    init() {
      this.values = this.getDefaults();
      return this;
    },
    
    // Get default values from schema
    getDefaults() {
      const defaults = {};
      
      Object.keys(this.schema).forEach(section => {
        defaults[section] = {};
        Object.keys(this.schema[section]).forEach(key => {
          defaults[section][key] = this.schema[section][key].default;
        });
      });
      
      return defaults;
    },
    
    // Set a configuration value
    set(path, value) {
      const pathParts = path.split('.');
      if (pathParts.length !== 2) {
        throw new Error('Configuration path must be in format "section.key"');
      }
      
      const [section, key] = pathParts;
      
      // Validate section exists
      if (!this.schema[section]) {
        throw new Error(`Unknown configuration section: ${section}`);
      }
      
      // Validate key exists
      if (!this.schema[section][key]) {
        throw new Error(`Unknown configuration key: ${section}.${key}`);
      }
      
      // Validate value type and constraints
      const schemaItem = this.schema[section][key];
      if (!this.validateValue(value, schemaItem)) {
        throw new Error(`Invalid value for ${path}: ${value}`);
      }
      
      // Set the value
      if (!this.values[section]) {
        this.values[section] = {};
      }
      
      const oldValue = this.values[section][key];
      this.values[section][key] = value;
      
      // Emit change event for plugins
      if (window.fap.chat.plugins) {
        window.fap.chat.plugins.doAction('config:changed', {
          path,
          oldValue,
          newValue: value
        });
      }
      
      return this;
    },
    
    // Get a configuration value
    get(path, defaultValue = undefined) {
      const pathParts = path.split('.');
      if (pathParts.length !== 2) {
        return defaultValue;
      }
      
      const [section, key] = pathParts;
      
      if (this.values[section] && this.values[section][key] !== undefined) {
        return this.values[section][key];
      }
      
      // Return schema default if available
      if (this.schema[section] && this.schema[section][key]) {
        return this.schema[section][key].default;
      }
      
      return defaultValue;
    },
    
    // Get entire section
    getSection(section) {
      return { ...this.values[section] } || {};
    },
    
    // Validate a value against schema
    validateValue(value, schemaItem) {
      // Type validation
      if (schemaItem.type === 'number' && typeof value !== 'number') {
        return false;
      }
      if (schemaItem.type === 'string' && typeof value !== 'string') {
        return false;
      }
      if (schemaItem.type === 'boolean' && typeof value !== 'boolean') {
        return false;
      }
      if (schemaItem.type === 'object' && (typeof value !== 'object' || value === null)) {
        return false;
      }
      
      // Range validation for numbers
      if (schemaItem.type === 'number') {
        if (schemaItem.min !== undefined && value < schemaItem.min) {
          return false;
        }
        if (schemaItem.max !== undefined && value > schemaItem.max) {
          return false;
        }
      }
      
      // Options validation
      if (schemaItem.options && !schemaItem.options.includes(value)) {
        return false;
      }
      
      // Custom validation function
      if (schemaItem.validate && !schemaItem.validate(value)) {
        return false;
      }
      
      return true;
    },
    
    // Extend schema (for plugins)
    extend(section, schema) {
      if (!this.schema[section]) {
        this.schema[section] = {};
        this.values[section] = {};
      }
      
      Object.keys(schema).forEach(key => {
        this.schema[section][key] = schema[key];
        // Set default value if not already set
        if (this.values[section][key] === undefined) {
          this.values[section][key] = schema[key].default;
        }
      });
      
      return this;
    },
    
    // Reset to defaults
    reset(section = null) {
      if (section) {
        if (this.schema[section]) {
          this.values[section] = {};
          Object.keys(this.schema[section]).forEach(key => {
            this.values[section][key] = this.schema[section][key].default;
          });
        }
      } else {
        this.values = this.getDefaults();
      }
      
      return this;
    },
    
    // Export configuration
    export() {
      return JSON.parse(JSON.stringify(this.values));
    },
    
    // Import configuration
    import(configData) {
      Object.keys(configData).forEach(section => {
        if (this.schema[section]) {
          Object.keys(configData[section]).forEach(key => {
            try {
              this.set(`${section}.${key}`, configData[section][key]);
            } catch (error) {
              console.warn(`Failed to import config ${section}.${key}:`, error.message);
            }
          });
        }
      });
      
      return this;
    }
  };

  // Initialize configuration
  config.init();

  // Export configuration system
  window.fap.chat.config = config;

  // Helper function for plugins to register their config
  window.fap.chat.registerPluginConfig = (pluginName, schema) => {
    return config.extend(`plugin_${pluginName}`, schema);
  };

})();