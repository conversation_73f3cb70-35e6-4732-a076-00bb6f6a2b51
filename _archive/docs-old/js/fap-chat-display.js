// FapChat Display Module
// Generic content display component for messages and sequential content

(function () {
  'use strict';

  // Initialize namespace
  window.fap = window.fap || {};
  window.fap.chat = window.fap.chat || {};

  // Display functions
  const render = (parentElement, items) => {
    if (!parentElement) return;

    // Clear existing content
    parentElement.innerHTML = '';

    // Render each item
    items.forEach(item => {
      const messageElement = renderItem(item, item.type);
      parentElement.appendChild(messageElement);
    });

    scrollToLatest(parentElement);
  };

  const renderItem = (item, type = 'system') => {
    const messageElement = document.createElement('message-item');
    messageElement.setAttribute('type', type);

    // Ensure consistent message ID
    if (!item.id) {
      item.id = generateMessageId();
    }
    messageElement.setAttribute('data-message-id', item.id);

    // Mark as saved if it has a saved state
    if (item.isSaved) {
      messageElement.setAttribute('data-saved', 'true');
    }

    // Create bubble structure
    const bubbleElement = document.createElement('message-bubble');
    const contentElement = document.createElement('message-content');

    const formattedContent = formatItem(item.content, item.metadata, type);

    // Check if content should be collapsible (more than 10 lines or 800 characters)
    const shouldCollapse = shouldContentCollapse(item.content);

    if (shouldCollapse) {
      const { summary, collapsed } = createCollapsibleContent(formattedContent);
      contentElement.innerHTML = summary;
      bubbleElement.appendChild(contentElement);
      bubbleElement.appendChild(collapsed);
    } else {
      contentElement.innerHTML = formattedContent;
      bubbleElement.appendChild(contentElement);
    }

    // Add message toolbar
    const toolbarElement = createMessageToolbar(item, type);
    bubbleElement.appendChild(toolbarElement);

    messageElement.appendChild(bubbleElement);
    return messageElement;
  };

  // Generate unique message ID
  const generateMessageId = () => {
    return `msg_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  };

  // Create message toolbar buttons
  const createMessageToolbar = (item, type) => {
    const toolbarElement = document.createElement('message-toolbar');

    // Copy button
    const copyButton = document.createElement('button');
    copyButton.innerHTML = '📋';
    copyButton.setAttribute('data-tooltip', 'Copy message to clipboard');
    copyButton.addEventListener('click', async (e) => {
      e.stopPropagation();
      await handleCopyMessage(item, e.target);
    });

    // Save button (toggle save/unsave)
    const saveButton = document.createElement('button');
    if (item.isSaved) {
      saveButton.innerHTML = '✅';
      saveButton.setAttribute('data-tooltip', 'Click to unsave message');
      saveButton.setAttribute('data-tooltip-theme', 'success');
      saveButton.addEventListener('click', async (e) => {
        e.stopPropagation();
        await handleUnsaveMessage(item, e.target);
      });
    } else {
      saveButton.innerHTML = '💾';
      saveButton.setAttribute('data-tooltip', 'Save message');
      saveButton.addEventListener('click', async (e) => {
        e.stopPropagation();
        await handleSaveMessage(item, type, e.target);
      });
    }

    // Delete button
    const deleteButton = document.createElement('button');
    deleteButton.innerHTML = '🗑️';
    deleteButton.setAttribute('data-tooltip', 'Delete message');
    deleteButton.setAttribute('data-tooltip-theme', 'error');
    deleteButton.addEventListener('click', (e) => {
      e.stopPropagation();
      handleDeleteMessage(item, e.target);
    });

    toolbarElement.appendChild(copyButton);
    toolbarElement.appendChild(saveButton);
    toolbarElement.appendChild(deleteButton);

    return toolbarElement;
  };

  // Handle copy message to clipboard
  const handleCopyMessage = async (item, buttonElement) => {
    try {
      // Get the message content (plain text, not HTML)
      const content = item.content || '';

      // Emit plugin hook for copy action
      const copyData = { ...item, copiedAt: new Date().toISOString() };
      if (window.fap.chat.plugins) {
        const filteredContent = window.fap.chat.plugins.applyFilters('message:before-copy', content, copyData);

        // Use modern clipboard API if available
        if (navigator.clipboard && navigator.clipboard.writeText) {
          await navigator.clipboard.writeText(filteredContent);
        } else {
          // Fallback for older browsers
          const textArea = document.createElement('textarea');
          textArea.value = filteredContent;
          textArea.style.position = 'fixed';
          textArea.style.opacity = '0';
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
        }

        // Visual feedback
        const originalText = buttonElement.innerHTML;
        buttonElement.innerHTML = '✅';
        setTimeout(() => {
          buttonElement.innerHTML = originalText;
        }, 1000);

        // Emit after copy action
        window.fap.chat.plugins.doAction('message:after-copy', copyData);

        console.log('Message copied to clipboard');
      }
    } catch (error) {
      console.error('Failed to copy message:', error);

      // Visual feedback for error
      const originalText = buttonElement.innerHTML;
      buttonElement.innerHTML = '❌';
      setTimeout(() => {
        buttonElement.innerHTML = originalText;
      }, 1000);

      // Show user-friendly error
      alert('Failed to copy message to clipboard. Please try selecting and copying manually.');
    }
  };

  // Handle save message
  const handleSaveMessage = async (item, type, buttonElement) => {
    // Prevent duplicate saves
    if (item.isSaved) {
      console.log('Message already saved, skipping');
      return;
    }

    try {
      // Disable button during save to prevent multiple clicks
      buttonElement.disabled = true;
      buttonElement.innerHTML = '⏳';

      // Prepare save data with consistent ID
      const saveData = {
        ...item,
        type,
        savedAt: new Date().toISOString(),
        id: item.id // Use existing ID, don't generate new one
      };

      if (window.fap.chat.plugins) {
        const filteredData = window.fap.chat.plugins.applyFilters('message:before-save', saveData);

        // Use storage system if available
        if (window.fap.chat.messages) {
          const messageId = await window.fap.chat.messages.save(filteredData);
          console.log(`Message saved with ID: ${messageId}`);

          // Mark item as saved
          item.isSaved = true;
          item.savedId = messageId;

          // Update the message element to show saved state
          const messageElement = document.querySelector(`message-item[data-message-id="${item.id}"]`);
          if (messageElement) {
            messageElement.setAttribute('data-saved', 'true');

            // Update save button to show saved state (toggle to unsave)
            buttonElement.innerHTML = '✅';
            buttonElement.setAttribute('data-tooltip', 'Click to unsave message');
            buttonElement.setAttribute('data-tooltip-theme', 'success');
            buttonElement.disabled = false;

            // Replace click handler with unsave handler
            const newButton = buttonElement.cloneNode(true);
            newButton.addEventListener('click', async (e) => {
              e.stopPropagation();
              await handleUnsaveMessage(item, e.target);
            });
            buttonElement.replaceWith(newButton);
          }

          // Emit after save action
          if (window.fap.chat.plugins) {
            window.fap.chat.plugins.doAction('message:after-save', { ...filteredData, savedId: messageId });
          }

        } else {
          // Fallback: emit action for plugins to handle
          window.fap.chat.plugins.doAction('message:save-requested', filteredData);
        }
      }
    } catch (error) {
      console.error('Failed to save message:', error);

      // Reset button on error
      buttonElement.disabled = false;
      buttonElement.innerHTML = '💾';
      buttonElement.setAttribute('data-tooltip', 'Save message');
      buttonElement.removeAttribute('data-tooltip-theme');

      alert('Failed to save message. Please try again.');
    }
  };

  // Handle unsave message
  const handleUnsaveMessage = async (item, buttonElement) => {
    if (!item.isSaved || !item.savedId) {
      console.log('Message not saved, nothing to unsave');
      return;
    }

    try {
      // Disable button during unsave
      buttonElement.disabled = true;
      buttonElement.innerHTML = '⏳';

      // Emit plugin hook for unsave action
      if (window.fap.chat.plugins) {
        window.fap.chat.plugins.doAction('message:before-unsave', item);
      }

      // Use storage system if available
      if (window.fap.chat.messages) {
        await window.fap.chat.messages.delete(item.savedId);
        console.log(`Message unsaved (deleted ID: ${item.savedId})`);

        // Mark item as not saved
        item.isSaved = false;
        delete item.savedId;

        // Update the message element to remove saved state
        const messageElement = document.querySelector(`message-item[data-message-id="${item.id}"]`);
        if (messageElement) {
          messageElement.removeAttribute('data-saved');

          // Update button back to save state
          buttonElement.innerHTML = '💾';
          buttonElement.setAttribute('data-tooltip', 'Save message');
          buttonElement.removeAttribute('data-tooltip-theme');
          buttonElement.disabled = false;

          // Replace click handler with save handler
          const newButton = buttonElement.cloneNode(true);
          newButton.addEventListener('click', async (e) => {
            e.stopPropagation();
            await handleSaveMessage(item, item.type, e.target);
          });
          buttonElement.replaceWith(newButton);
        }

        // Emit after unsave action
        if (window.fap.chat.plugins) {
          window.fap.chat.plugins.doAction('message:after-unsave', item);
        }

      } else {
        // Fallback: emit action for plugins to handle
        if (window.fap.chat.plugins) {
          window.fap.chat.plugins.doAction('message:unsave-requested', item);
        }
      }
    } catch (error) {
      console.error('Failed to unsave message:', error);

      // Reset button on error
      buttonElement.disabled = false;
      buttonElement.innerHTML = '✅';
      buttonElement.setAttribute('data-tooltip', 'Click to unsave message');
      buttonElement.setAttribute('data-tooltip-theme', 'success');

      alert('Failed to unsave message. Please try again.');
    }
  };

  // Handle delete message
  const handleDeleteMessage = (item, buttonElement) => {
    // Confirm deletion
    if (!confirm('Delete this message?')) {
      return;
    }

    // Find and remove the message element
    const messageElement = buttonElement.closest('message-item');
    if (messageElement) {
      // Emit plugin hook for delete action
      if (window.fap.chat.plugins) {
        const deleteData = { ...item, deletedAt: new Date().toISOString() };
        window.fap.chat.plugins.doAction('message:before-delete', deleteData);
      }

      // Remove from DOM
      messageElement.remove();

      // Emit after delete action
      if (window.fap.chat.plugins) {
        window.fap.chat.plugins.doAction('message:after-delete', item);
      }

      console.log('Message deleted');
    }
  };

  const formatItem = (content, metadata = {}, type = 'system') => {
    if (!content) return '';

    // Convert line breaks to HTML breaks for display
    return content
      .replace(/\n/g, '<br>')
      .replace(/\r/g, '');
  };

  const shouldContentCollapse = (content) => {
    if (!content) return false;

    const lineCount = (content.match(/\n/g) || []).length + 1;
    const charCount = content.length;

    // Collapse if more than 10 lines or 800 characters
    // Try alternate values
    return lineCount > 5 || charCount > 400;
  };

  const createCollapsibleContent = (formattedContent) => {
    // Create preview (first 3 lines or 200 characters)
    const lines = formattedContent.split('<br>');
    const previewLines = lines.slice(0, 3);
    const preview = previewLines.join('<br>');

    const isLongContent = lines.length > 3 || formattedContent.length > 200;
    const summaryText = isLongContent ?
      `${preview}${lines.length > 3 ? '<br>...' : ''}` :
      formattedContent;

    // Create details/summary structure
    const detailsElement = document.createElement('details');
    const summaryElement = document.createElement('summary');
    const collapsedElement = document.createElement('expandable-content');

    summaryElement.textContent = `Show ${lines.length > 3 ? 'full content' : 'more'} (${lines.length} lines)`;
    collapsedElement.innerHTML = formattedContent;

    detailsElement.appendChild(summaryElement);
    detailsElement.appendChild(collapsedElement);

    return {
      summary: summaryText,
      collapsed: detailsElement
    };
  };

  const addItem = (parentElement, item, type = 'system') => {
    if (!parentElement) return;

    const messageElement = renderItem(item, type);
    parentElement.appendChild(messageElement);
    scrollToLatest(parentElement);
  };

  const clear = (parentElement) => {
    if (parentElement) {
      parentElement.innerHTML = '';
    }
  };

  const scrollToLatest = (parentElement) => {
    if (parentElement) {
      parentElement.scrollTop = parentElement.scrollHeight;
    }
  };

  // Export public interface
  window.fap.chat.display = {
    render,
    addItem,
    clear,
    scrollToLatest
  };

})();