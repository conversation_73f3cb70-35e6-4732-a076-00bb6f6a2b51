// FapChat Smart Tooltip Positioning
// Lightweight JavaScript helper for edge case positioning

(function() {
  'use strict';

  // Initialize namespace
  window.fap = window.fap || {};
  window.fap.chat = window.fap.chat || {};

  // Smart positioning logic
  const adjustTooltipPosition = (element) => {
    if (!element.hasAttribute('data-tooltip')) return;
    
    // Clean up any previous auto positioning
    element.removeAttribute('data-tooltip-auto');
    element.removeAttribute('data-tooltip-position');
    
    const rect = element.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    // Estimate tooltip dimensions (approximate)
    const tooltipText = element.getAttribute('data-tooltip');
    const estimatedWidth = Math.min(tooltipText.length * 8 + 20, 200);
    const estimatedHeight = 35;
    
    // Check spaces around element
    const topSpace = rect.top;
    const bottomSpace = viewportHeight - rect.bottom;
    const leftSpace = rect.left;
    const rightSpace = viewportWidth - rect.right;
    const centerX = (rect.left + rect.right) / 2;
    
    let position = null;
    
    // Priority: vertical positioning first (top/bottom)
    if (topSpace >= estimatedHeight + 15) {
      // Enough space on top - check horizontal centering
      if (centerX - estimatedWidth/2 < 10) {
        position = 'right'; // Too close to left edge
      } else if (centerX + estimatedWidth/2 > viewportWidth - 10) {
        position = 'left'; // Too close to right edge
      } else {
        position = null; // Default top position is fine
      }
    } else if (bottomSpace >= estimatedHeight + 15) {
      // Not enough space on top, use bottom
      position = 'bottom';
    } else {
      // Very little vertical space, use horizontal
      if (rightSpace >= estimatedWidth + 15) {
        position = 'right';
      } else if (leftSpace >= estimatedWidth + 15) {
        position = 'left';
      } else {
        position = 'bottom'; // Fallback to bottom even if cramped
      }
    }
    
    // Apply positioning
    if (position) {
      element.setAttribute('data-tooltip-position', position);
    }
  };

  // Setup hover listeners for smart positioning
  const setupSmartTooltips = () => {
    // Use event delegation for better performance
    document.addEventListener('mouseenter', (e) => {
      if (e.target && e.target.nodeType === 1 && e.target.hasAttribute && e.target.hasAttribute('data-tooltip')) {
        // Mark as positioning to hide tooltip during calculation
        e.target.setAttribute('data-tooltip-positioning', '');
        
        // Calculate position immediately
        adjustTooltipPosition(e.target);
        
        // Remove positioning flag after longer delay to ensure smooth fade-in
        setTimeout(() => {
          if (e.target.hasAttribute('data-tooltip-positioning')) {
            e.target.removeAttribute('data-tooltip-positioning');
          }
        }, 200);
      }
    }, true);
    
    // Clean up positioning attributes on mouse leave
    document.addEventListener('mouseleave', (e) => {
      if (e.target && e.target.nodeType === 1 && e.target.hasAttribute && e.target.hasAttribute('data-tooltip')) {
        // Clean up all positioning attributes
        e.target.removeAttribute('data-tooltip-positioning');
        // Only remove auto-generated positioning, not manually set ones
        if (!e.target.hasAttribute('data-tooltip-manual-position')) {
          e.target.removeAttribute('data-tooltip-position');
        }
        e.target.removeAttribute('data-tooltip-auto');
      }
    }, true);
    
    // Handle window resize
    let resizeTimeout;
    window.addEventListener('resize', () => {
      clearTimeout(resizeTimeout);
      resizeTimeout = setTimeout(() => {
        // Reset all auto-positioned tooltips
        document.querySelectorAll('[data-tooltip-auto]').forEach(el => {
          el.removeAttribute('data-tooltip-auto');
          el.removeAttribute('data-tooltip-position');
        });
      }, 150);
    });
  };

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupSmartTooltips);
  } else {
    setupSmartTooltips();
  }

  // Export for manual use
  window.fap.chat.tooltip = {
    adjustPosition: adjustTooltipPosition,
    setup: setupSmartTooltips
  };

})();