/* Message Content Elements (message-*) */

message-bubble {
  align-items: center;
  background: var(--chat-input-bg);
  border: 1px solid var(--chat-border);
  border-radius: 12px;
  display: flex;
  justify-content: space-between;
  padding: 6px 8px;
  position: relative;
}

message-bubble:has(details) {
  display: block;
}

message-content {
  display: block;
  line-height: 1.4;
}

message-item {
  display: block;
  margin-bottom: 16px;
  max-width: 85%;
  word-wrap: break-word;
}

message-item:hover message-toolbar {
  opacity: 1;
}

message-item[type="error"] message-bubble {
  background: rgba(255, 107, 107, 0.1);
  border-color: #ff6b6b;
  color: #ff6b6b;
}

message-item[type="error"] {
  margin-left: 8px;
  margin-right: auto;
}

message-item[type="system"] message-bubble {
  background: var(--chat-input-bg);
  border-color: var(--chat-border);
  color: var(--chat-text);
}

message-item[type="system"] {
  margin-left: 8px;
  margin-right: auto;
}

message-item[type="user"] message-bubble {
  background: var(--chat-accent);
  border-color: var(--chat-accent);
  color: white;
}

message-item[type="user"] {
  margin-left: auto;
  margin-right: 8px;
}

message-toolbar {
  display: flex;
  gap: 4px;
  justify-content: flex-end;
  /* margin-top: 8px; */
  opacity: 0;
  transition: opacity 0.2s ease;
}

message-toolbar button {
  background: rgba(255, 255, 255, 0.5);
  border: 1px solid rgba(255, 255, 255, 0.7);
  border-radius: 4px;
  color: inherit;
  cursor: pointer;
  flex-shrink: 0;
  font-size: 24px;
  padding: 2px 4px;
  margin-right: 6px;
  transition: all 0.2s ease;
}

message-toolbar button:active {
  transform: translateY(0);
}

message-toolbar button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* Copy button (first button) */
message-toolbar button:first-child:hover {
  background: rgba(100, 100, 255, 0.3);
  border-color: rgba(100, 100, 255, 0.5);
}

/* Save button (second button) */
message-toolbar button:nth-child(2):hover {
  background: rgba(0, 150, 0, 0.3);
  border-color: rgba(0, 150, 0, 0.5);
}

/* Delete button (last button) */
message-toolbar button:last-child:hover {
  background: rgba(200, 0, 0, 0.3);
  border-color: rgba(200, 0, 0, 0.5);
}

/* Saved message visual indicator */
message-item[data-saved="true"] message-bubble {
  border-left: 3px solid rgba(0, 150, 0, 0.6);
}



/* Collapsible content styling */
message-item details {
  margin-top: 8px;
}

message-item details[open] summary {
  margin-bottom: 8px;
}

message-item summary {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  opacity: 0.8;
  padding: 4px 8px;
  user-select: none;
}

message-item summary:hover {
  background: rgba(255, 255, 255, 0.15);
  opacity: 1;
}

/* Expandable content container */
expandable-content {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  display: block;
  font-family: inherit;
  font-size: 0.95em;
  margin-top: 8px;
  padding: 8px;
  white-space: pre-wrap;
}