/* FapChat Custom Tooltip System */

/* Tooltip container */
[data-tooltip] {
  position: relative;
  cursor: pointer;
}

/* Tooltip content */
[data-tooltip]::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 8px;
  padding: 6px 10px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  font-size: 12px;
  font-weight: normal;
  line-height: 1.2;
  white-space: nowrap;
  border-radius: 4px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  pointer-events: none;
  z-index: 10000;

  /* Smart positioning - prevent overflow */
  max-width: min(200px, 80vw);
}

/* Tooltip arrow */
[data-tooltip]::after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 2px;
  border: 4px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.9);
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.2s ease, visibility 0.2s ease;
  pointer-events: none;
  z-index: 10000;
}

/* Show tooltip on hover - with positioning delay */
[data-tooltip]:hover::before,
[data-tooltip]:hover::after {
  opacity: 1;
  visibility: visible;
  transition-delay: 0.3s;
  /* Longer delay to allow positioning */
}

/* Hide tooltip immediately when positioning */
[data-tooltip-positioning]::before,
[data-tooltip-positioning]::after {
  opacity: 0 !important;
  visibility: hidden !important;
  transition: none !important;
}

/* Tooltip variants */

/* Bottom tooltip */
[data-tooltip-position="bottom"]::before {
  top: 100%;
  bottom: auto;
  margin-top: 15px;
  margin-bottom: 0;
  left: 25%;
  transform: translateX(-25%);
}

[data-tooltip-position="bottom"]::after {
  top: 100%;
  bottom: auto;
  margin-top: 9px;
  margin-bottom: 0;
  left: 25%;
  transform: translateX(-25%);
  border-top-color: transparent;
  border-bottom-color: rgba(0, 0, 0, 0.9);
}

/* Left tooltip */
[data-tooltip-position="left"]::before {
  top: 50%;
  bottom: auto;
  right: 100%;
  left: auto;
  transform: translateY(-50%);
  margin-right: 8px;
  margin-bottom: 0;
}

[data-tooltip-position="left"]::after {
  top: 50%;
  bottom: auto;
  right: 100%;
  left: auto;
  transform: translateY(-50%);
  margin-right: 2px;
  margin-bottom: 0;
  border-top-color: transparent;
  border-left-color: rgba(0, 0, 0, 0.9);
}

/* Right tooltip */
[data-tooltip-position="right"]::before {
  top: 50%;
  bottom: auto;
  left: 100%;
  transform: translateY(-50%);
  margin-left: 8px;
  margin-bottom: 0;
}

[data-tooltip-position="right"]::after {
  top: 50%;
  bottom: auto;
  left: 100%;
  transform: translateY(-50%);
  margin-left: 2px;
  margin-bottom: 0;
  border-top-color: transparent;
  border-right-color: rgba(0, 0, 0, 0.9);
}

/* Tooltip themes */

/* Success tooltip */
[data-tooltip-theme="success"]::before {
  background: rgba(0, 150, 0, 0.9);
}

[data-tooltip-theme="success"]::after {
  border-top-color: rgba(0, 150, 0, 0.9);
}

[data-tooltip-theme="success"][data-tooltip-position="bottom"]::after {
  border-top-color: transparent;
  border-bottom-color: rgba(0, 150, 0, 0.9);
}

/* Error tooltip */
[data-tooltip-theme="error"]::before {
  background: rgba(200, 0, 0, 0.9);
}

[data-tooltip-theme="error"]::after {
  border-top-color: rgba(200, 0, 0, 0.9);
}

[data-tooltip-theme="error"][data-tooltip-position="bottom"]::after {
  border-top-color: transparent;
  border-bottom-color: rgba(200, 0, 0, 0.9);
}

/* Info tooltip */
[data-tooltip-theme="info"]::before {
  background: rgba(0, 100, 200, 0.9);
}

[data-tooltip-theme="info"]::after {
  border-top-color: rgba(0, 100, 200, 0.9);
}

[data-tooltip-theme="info"][data-tooltip-position="bottom"]::after {
  border-top-color: transparent;
  border-bottom-color: rgba(0, 100, 200, 0.9);
}

/* Multiline tooltip support */
[data-tooltip-multiline]::before {
  white-space: pre-line;
  max-width: 200px;
  text-align: center;
}

/* Delay variants */
[data-tooltip-delay="fast"]:hover::before,
[data-tooltip-delay="fast"]:hover::after {
  transition-delay: 0.1s;
}

[data-tooltip-delay="slow"]:hover::before,
[data-tooltip-delay="slow"]:hover::after {
  transition-delay: 0.8s;
}

/* Disable tooltip */
[data-tooltip-disabled]::before,
[data-tooltip-disabled]::after {
  display: none !important;
}

/* Smart positioning for message toolbar buttons */
message-toolbar button[data-tooltip]::before {
  left: auto;
  right: 0;
  transform: translateX(0);
  z-index: 10001;
  /* Higher than regular tooltips */
}

message-toolbar button[data-tooltip]::after {
  left: auto;
  right: 4px;
  transform: translateX(0);
  z-index: 10001;
  /* Higher than regular tooltips */
}

/* Ensure message items don't interfere with tooltips */
message-item {
  position: relative;
  z-index: 1;
}

/* When tooltip is active, boost the parent message z-index */
message-item:has(button:hover) {
  z-index: 100;
}

/* Auto-flip for edge elements */
[data-tooltip-auto]::before {
  left: clamp(10px, 50%, calc(100vw - 100px));
  transform: translateX(-50%);
}

[data-tooltip-auto]::after {
  left: clamp(14px, 50%, calc(100vw - 96px));
  transform: translateX(-50%);
}

/* Responsive positioning for small containers */
@media (max-width: 480px) {
  [data-tooltip]::before {
    max-width: calc(100vw - 20px);
    white-space: pre-wrap;
    word-break: break-word;
  }

  /* Force bottom positioning on mobile */
  [data-tooltip]:not([data-tooltip-position])::before {
    top: 100%;
    bottom: auto;
    margin-top: 8px;
    margin-bottom: 0;
  }

  [data-tooltip]:not([data-tooltip-position])::after {
    top: 100%;
    bottom: auto;
    margin-top: 2px;
    margin-bottom: 0;
    border-top-color: transparent;
    border-bottom-color: rgba(0, 0, 0, 0.9);
  }
}