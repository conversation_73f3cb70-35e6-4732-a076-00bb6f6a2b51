/* Utility Classes (for state/behavior only) */

.error {
  background: rgba(255, 107, 107, 0.1) !important;
  border-color: #ff6b6b !important;
}

.hidden {
  display: none !important;
}

.loading {
  opacity: 0.5;
  pointer-events: none;
}

/* Generic scrollbar styling */
.scrollable::-webkit-scrollbar {
  width: 6px;
}

.scrollable::-webkit-scrollbar-track {
  background: var(--chat-bg);
}

.scrollable::-webkit-scrollbar-thumb {
  background: var(--chat-border);
  border-radius: 3px;
}

.scrollable::-webkit-scrollbar-thumb:hover {
  background: #606060;
}

/* Thin scrollbar variant */
.scrollable-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollable-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollable-thin::-webkit-scrollbar-thumb {
  background: var(--chat-border);
  border-radius: 2px;
}

.scrollable-thin::-webkit-scrollbar-thumb:hover {
  background: #606060;
}