/* Chat Layout Elements (chat-*) */

chat-display {
  background: var(--chat-bg);
  display: block;
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  padding: 16px;
}

chat-input {
  background: var(--chat-input-bg);
  border-top: 1px solid var(--chat-border);
  color: var(--chat-text);
  display: block;
  font-family: inherit;
  font-size: inherit;
  line-height: 1.4;
  max-height: var(--chat-input-max-height);
  min-height: 80px;
  outline: none;
  overflow-y: auto;
  padding: 16px;
  resize: none;
  white-space: pre-wrap;
  width: 100%;
  word-wrap: break-word;
  box-sizing: border-box;
}

chat-input:empty::before {
  color: #666;
  content: attr(placeholder);
  pointer-events: none;
}

chat-input:focus {
  background: rgba(255, 255, 255, 0.02);
  border-radius: 4px;
}

chat-input::selection {
  background: var(--chat-accent);
  color: white;
}

chat-terminal {
  background: var(--chat-bg);
  border: 1px solid var(--chat-border);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  width: 100%;
}