# pnpm Workspaces for Dummies

> A beginner-friendly guide to understanding and using pnpm workspaces in the Freedom Application Platform monorepo.

## 🤔 What is a pnpm Workspace?

Think of a pnpm workspace as a **big folder that contains multiple related projects**. Instead of having separate folders scattered around your computer, you keep everything together in one organized place.

**Traditional approach:**
```
~/projects/fap-chat/          # Separate project
~/projects/fap-tooltip/       # Another separate project  
~/projects/my-chat-app/       # App using the above
```

**Workspace approach:**
```
~/fap-monorepo/
├── packages/fap-chat/        # Component
├── packages/fap-tooltip/     # Component
└── apps/my-chat-app/         # App using components
```

## 🏗️ Our Workspace Structure

```
fap-monorepo/
├── packages/           # 📦 Reusable components (@fap/*)
│   ├── fap-core/      # @fap/core - Shared styles & utilities
│   ├── fap-chat/      # @fap/chat - Chat component
│   └── fap-tooltip/   # @fap/tooltip - Tooltip component
├── apps/              # 🚀 Applications that use components
│   └── chat-simple/   # Demo chat app
├── tools/             # 🔧 Build tools and configurations
├── docs/              # 📚 Documentation (you are here!)
└── pnpm-workspace.yaml # 📋 Workspace configuration
```

## 🎯 Key Concepts

### 1. **Packages vs Apps**
- **Packages** (`packages/`) = Reusable libraries (like LEGO blocks)
- **Apps** (`apps/`) = Complete applications (like LEGO creations)

### 2. **Scoped Names**
All our packages use `@fap/` prefix:
- `@fap/core` instead of `fap-core`
- `@fap/chat` instead of `fap-chat`
- `@fap/tooltip` instead of `fap-tooltip`

This keeps them organized and prevents naming conflicts.

### 3. **Workspace Dependencies**
When one package needs another, we use `workspace:*`:

```json
{
  "dependencies": {
    "@fap/core": "workspace:*"
  }
}
```

This means "use the local version from this workspace."

## 🚀 Common Commands

### Development
```bash
# Start everything
pnpm dev

# Start just the chat app
pnpm dev:chat

# Install dependencies for all packages
pnpm install
```

### Building
```bash
# Build everything
pnpm build

# Build only packages (not apps)
pnpm build:packages

# Build only apps (not packages)  
pnpm build:apps
```

### Working with Specific Packages
```bash
# Run a command in a specific package
pnpm --filter @fap/chat dev

# Run a command in the chat-simple app
pnpm --filter chat-simple dev

# Add a dependency to a specific package
pnpm --filter @fap/chat add some-library
```

### Workspace Information
```bash
# List all packages in the workspace
pnpm workspace:list

# See the dependency graph
pnpm workspace:graph
```

## 🔄 Versioning with Changesets

When you make changes to packages, use changesets to track versions:

### Step 1: Make Changes
Edit files in `packages/fap-chat/` or any other package.

### Step 2: Create a Changeset
```bash
pnpm changeset
```

This will ask you:
- Which packages changed?
- What type of change? (patch/minor/major)
- What did you change? (description)

### Step 3: Version Packages
```bash
pnpm version
```

This updates version numbers and creates changelogs.

### Step 4: Publish (when ready)
```bash
pnpm publish
```

## 🎨 Creating New Packages

### 1. Create the Directory
```bash
mkdir packages/fap-newpackage
cd packages/fap-newpackage
```

### 2. Create package.json
```json
{
  "name": "@fap/newpackage",
  "version": "1.0.0",
  "description": "Description of your new package",
  "type": "module",
  "main": "fap-newpackage.js",
  "dependencies": {
    "@fap/core": "workspace:*"
  }
}
```

### 3. Install Dependencies
```bash
pnpm install
```

The workspace automatically recognizes new packages!

## 🚀 Creating New Apps

### 1. Create the Directory
```bash
mkdir apps/my-new-app
cd apps/my-new-app
```

### 2. Create package.json
```json
{
  "name": "my-new-app",
  "version": "1.0.0",
  "dependencies": {
    "@fap/core": "workspace:*",
    "@fap/chat": "workspace:*",
    "@fap/tooltip": "workspace:*"
  }
}
```

### 3. Install Dependencies
```bash
pnpm install
```

## 🤝 Benefits of This Setup

**✅ Shared Dependencies** - No duplicate node_modules folders
**✅ Easy Cross-References** - Packages can easily use each other
**✅ Consistent Versions** - All packages use the same version of shared dependencies
**✅ Simplified Development** - One command to install/build everything
**✅ Professional Workflow** - Industry-standard monorepo practices

## 🆘 Troubleshooting

### "Package not found" errors
```bash
# Reinstall all dependencies
pnpm install

# Clear cache and reinstall
rm -rf node_modules && pnpm install
```

### "Workspace dependency not found"
Make sure the package name in `package.json` matches exactly:
- Use `@fap/core` not `fap-core`
- Use `workspace:*` not a version number

### Changes not reflected
```bash
# Restart development servers
pnpm dev
```

## 📚 Learn More

- [pnpm Workspaces Official Docs](https://pnpm.io/workspaces)
- [Changesets Documentation](https://github.com/changesets/changesets)
- [Monorepo Best Practices](https://monorepo.tools/)

---

*This guide covers the basics of working with our pnpm workspace. As you get comfortable with these concepts, you'll find that managing multiple related packages becomes much easier!*
