// GitHub Demo App - GitHub Pages Version
// This demonstrates the published @acumen-desktop packages running on GitHub Pages

// Initialize the demo app
window.githubDemo = {
    init() {
        this.setupChat();
        this.setupTooltips();
        this.addPagesBanner();
        console.log('🚀 GitHub Pages Demo initialized with published @acumen-desktop packages!');
    },

    setupChat() {
        // Initialize chat component from published @acumen-desktop/chat package
        const chatTerminal = document.getElementById('github-demo-chat');
        const chatDisplay = document.getElementById('github-demo-display');
        const chatInput = document.getElementById('github-demo-input');

        if (window.fap && window.fap.chat) {
            // Configure chat with GitHub Pages themed messages
            window.fap.chat.config.welcomeMessage = '🚀 Welcome to GitHub Pages Demo!';
            window.fap.chat.config.placeholder = 'Try: /packages, /pages, /help';
            
            // Initialize chat components
            window.fap.chat.display.init(chatDisplay);
            window.fap.chat.input.init(chatInput);
            
            // Add demo-specific commands
            this.addGitHubPagesCommands();
            
            // Add welcome message
            window.fap.chat.api.addMessage({
                type: 'system',
                content: '🎉 Live demo hosted on GitHub Pages consuming packages from GitHub Packages!',
                timestamp: new Date()
            });
        } else {
            console.warn('Chat component not loaded - check package paths');
        }
    },

    addGitHubPagesCommands() {
        // Add GitHub Pages specific chat commands
        const commands = {
            '/packages': () => {
                return {
                    type: 'info',
                    content: `📦 Published to GitHub Packages:
• @acumen-desktop/core@0.1.0 - Theme system
• @acumen-desktop/chat@0.1.0 - This chat component!
• @acumen-desktop/tooltip@0.1.0 - Interactive tooltips
• @acumen-desktop/dev-server@0.1.0 - Development tools

🔗 View: https://github.com/Acumen-Desktop?tab=packages`
                };
            },
            
            '/pages': () => {
                return {
                    type: 'success',
                    content: `🌐 GitHub Pages Integration:
✅ Static hosting enabled
✅ Assets copied from published packages
✅ Public demo accessible worldwide
✅ Complete GitHub ecosystem test

🔗 URL: https://acumen-desktop.github.io/fap_monorepo_july_2025/`
                };
            },
            
            '/ecosystem': () => {
                return {
                    type: 'info',
                    content: `🐙 Complete GitHub Ecosystem:
📦 GitHub Packages - Private package registry
🌐 GitHub Pages - Public demo hosting
🔗 Integration - Packages consumed by hosted app
🏗️ Architecture - Vanilla JS, semantic HTML
🚀 Philosophy - Complexity-free web development`
                };
            },
            
            '/install': () => {
                return {
                    type: 'code',
                    content: `# Configure .npmrc for GitHub Packages
@acumen-desktop:registry=https://npm.pkg.github.com
//npm.pkg.github.com/:_authToken=\${GITHUB_TOKEN}

# Install packages
npm install @acumen-desktop/core @acumen-desktop/chat

# Use in your project
<link rel="stylesheet" href="node_modules/@acumen-desktop/core/css/fap-theme.css">
<script src="node_modules/@acumen-desktop/chat/js/fap-chat-api.js"></script>`
                };
            },
            
            '/help': () => {
                return {
                    type: 'info',
                    content: `Available commands:
/packages - List published packages
/pages - GitHub Pages info
/ecosystem - Complete GitHub integration
/install - Installation instructions
/clear - Clear chat history

🎯 This demo proves that vanilla JavaScript components can be professionally packaged and distributed!`
                };
            }
        };

        // Register commands with chat system
        if (window.fap && window.fap.chat && window.fap.chat.plugins) {
            Object.entries(commands).forEach(([command, handler]) => {
                window.fap.chat.plugins.register({
                    name: `pages-${command.slice(1)}`,
                    command: command,
                    handler: handler,
                    description: `GitHub Pages demo command: ${command}`
                });
            });
        }
    },

    setupTooltips() {
        // Add tooltips to demonstrate @acumen-desktop/tooltip package
        if (window.fap && window.fap.tooltip) {
            // Add tooltips to package status badges
            document.querySelectorAll('.status-badge').forEach(badge => {
                badge.setAttribute('data-tooltip', 'Package successfully published to GitHub Packages');
                badge.setAttribute('data-tooltip-theme', 'success');
            });

            // Add tooltips to GitHub links
            document.querySelectorAll('.github-link').forEach(link => {
                link.setAttribute('data-tooltip', 'Opens in new tab');
                link.setAttribute('data-tooltip-theme', 'info');
            });

            // Add tooltip to pages banner
            const banner = document.querySelector('.pages-banner');
            if (banner) {
                banner.setAttribute('data-tooltip', 'This page is hosted on GitHub Pages!');
                banner.setAttribute('data-tooltip-theme', 'primary');
            }

            // Initialize tooltip system
            window.fap.tooltip.init();
            
            console.log('✨ Tooltips initialized from @acumen-desktop/tooltip package');
        }
    },

    addPagesBanner() {
        // Add dynamic content to show this is live on GitHub Pages
        const banner = document.querySelector('.pages-banner');
        if (banner) {
            // Add live indicator
            const indicator = document.createElement('div');
            indicator.innerHTML = '🟢 LIVE';
            indicator.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: #00ff00;
                color: #000;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                font-weight: bold;
            `;
            banner.style.position = 'relative';
            banner.appendChild(indicator);
        }
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Small delay to ensure all packages are loaded
    setTimeout(() => {
        window.githubDemo.init();
    }, 100);
});

// Add GitHub Pages specific styling
const style = document.createElement('style');
style.textContent = `
    .stat-item {
        padding: 0.75rem;
        background: var(--bg-primary);
        border-radius: var(--border-radius);
        border: 1px solid var(--border-color);
    }
    
    .stat-item strong {
        color: var(--text-accent);
    }
    
    /* GitHub Pages specific enhancements */
    .pages-banner {
        animation: glow 2s ease-in-out infinite alternate;
    }
    
    @keyframes glow {
        from { box-shadow: 0 0 5px rgba(102, 126, 234, 0.5); }
        to { box-shadow: 0 0 20px rgba(102, 126, 234, 0.8); }
    }
`;
document.head.appendChild(style);
