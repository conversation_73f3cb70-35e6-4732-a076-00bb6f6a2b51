version: '3.8'

services:
  terminusdb:
    image: terminusdb/terminusdb-server:latest
    container_name: fap-terminusdb-prod
    ports:
      - "127.0.0.1:6363:6363"  # Only bind to localhost, nginx handles external access
    volumes:
      - /opt/terminusdb/data:/app/terminusdb/storage
      - /opt/terminusdb/backups:/app/terminusdb/backups
      - /opt/terminusdb/logs:/app/terminusdb/logs
    environment:
      - TERMINUSDB_ADMIN_PASS_FILE=/run/secrets/admin_password
      - TERMINUSDB_SERVER_NAME=Acumen Desktop Production
      - TERMINUSDB_LOG_LEVEL=info
      - TERMINUSDB_HTTPS_ENABLED=false  # nginx handles SSL
      - TERMINUSDB_BACKUP_ENABLED=true
      - TERMINUSDB_BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
    secrets:
      - admin_password
    networks:
      - fap-internal
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6363/api/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  nginx:
    image: nginx:alpine
    container_name: fap-nginx-terminusdb
    ports:
      - "443:443"
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro
      - /var/log/nginx:/var/log/nginx
    depends_on:
      - terminusdb
    networks:
      - fap-internal
    restart: unless-stopped

  # Backup service
  terminusdb-backup:
    image: terminusdb/terminusdb-server:latest
    container_name: fap-terminusdb-backup
    volumes:
      - /opt/terminusdb/data:/app/terminusdb/storage:ro
      - /opt/terminusdb/backups:/backups
      - ./backup-scripts:/scripts
    environment:
      - BACKUP_RETENTION_DAYS=30
      - BACKUP_S3_BUCKET=${BACKUP_S3_BUCKET}
      - AWS_ACCESS_KEY_ID_FILE=/run/secrets/aws_access_key
      - AWS_SECRET_ACCESS_KEY_FILE=/run/secrets/aws_secret_key
    secrets:
      - aws_access_key
      - aws_secret_key
    networks:
      - fap-internal
    restart: "no"
    profiles:
      - backup

secrets:
  admin_password:
    file: ./secrets/admin_password.txt
  aws_access_key:
    file: ./secrets/aws_access_key.txt
  aws_secret_key:
    file: ./secrets/aws_secret_key.txt

networks:
  fap-internal:
    driver: bridge
    internal: false
