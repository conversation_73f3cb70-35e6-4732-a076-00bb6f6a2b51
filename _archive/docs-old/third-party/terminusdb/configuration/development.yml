version: '3.8'

services:
  terminusdb:
    image: terminusdb/terminusdb-server:latest
    container_name: fap-terminusdb-dev
    ports:
      - "6363:6363"
    volumes:
      - terminusdb-data:/app/terminusdb/storage
      - ./init-scripts:/docker-entrypoint-initdb.d
    environment:
      - TERMINUSDB_ADMIN_PASS=dev-password-change-me
      - TERMINUSDB_SERVER_NAME=FAP Development
      - TERMINUSDB_LOG_LEVEL=debug
    networks:
      - fap-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6363/api/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Optional: TerminusDB Dashboard (if available)
  terminusdb-dashboard:
    image: terminusdb/terminusdb-dashboard:latest
    container_name: fap-terminusdb-dashboard-dev
    ports:
      - "3005:3005"
    environment:
      - TERMINUSDB_SERVER=http://terminusdb:6363
    depends_on:
      - terminusdb
    networks:
      - fap-network
    restart: unless-stopped

volumes:
  terminusdb-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/data/terminusdb

networks:
  fap-network:
    driver: bridge
