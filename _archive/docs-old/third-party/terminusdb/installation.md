# TerminusDB Installation Guide

## Recommended: Native Installation from Source

> **Why Native?** Better performance, no container overhead, easier debugging, and direct system integration. TerminusDB is built in Rust + SWI-Prolog with clean dependencies.

### Prerequisites (macOS)
- **Homebrew** installed
- **4GB+ RAM** available  
- **2GB+ disk space** for build
- **Xcode Command Line Tools**: `xcode-select --install`

### 1. Install Dependencies

```bash
# Install core dependencies
brew install gmp swi-prolog rust

# Verify installations
swi-pl --version
rustc --version
```

### 2. Clone and Build TerminusDB

```bash
# Clone the repository
git clone https://github.com/terminusdb/terminusdb.git
cd terminusdb

# Build the native binary (takes 5-10 minutes)
make

# Verify build
./terminusdb --help
```

### 3. Initialize and Start

```bash
# Initialize system database with secure password
./terminusdb store init --key "your-secure-admin-password"

# Start the server
./terminusdb serve

# Server runs on: http://127.0.0.1:6363
```

### 4. Verify Installation

```bash
# Test API endpoint
curl http://127.0.0.1:6363/api/

# Should return server info JSON
```

### 5. Create System Service (Optional)

```bash
# Create launchd service for auto-start
sudo cp docs/terminusdb.plist /Library/LaunchDaemons/
sudo launchctl load /Library/LaunchDaemons/terminusdb.plist
```

## Alternative: Docker Installation

> **Note:** While Docker is convenient for some users, we recommend native installation for better performance and system integration.

### Quick Docker Setup

```bash
# Pull and run (if you prefer Docker)
docker pull terminusdb/terminusdb-server:latest

# Run with persistent storage
mkdir -p ~/terminusdb-data
docker run -d \
  --name terminusdb-server \
  -p 6363:6363 \
  -v ~/terminusdb-data:/app/terminusdb/storage \
  -e TERMINUSDB_ADMIN_PASS=your-secure-password \
  terminusdb/terminusdb-server:latest

# Access at: http://localhost:6363
```

### Docker vs Native Comparison

| Aspect | Native Build | Docker |
|--------|-------------|--------|
| **Performance** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **Memory Usage** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **Debugging** | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| **System Integration** | ⭐⭐⭐⭐⭐ | ⭐⭐ |
| **Setup Complexity** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| **Portability** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

## Production Setup (Self-Hosted)

### System Requirements
- **OS**: Ubuntu 20.04+ / CentOS 8+ / macOS 10.15+
- **RAM**: 8GB minimum, 16GB+ recommended
- **Storage**: SSD with 100GB+ available
- **Network**: HTTPS-capable domain

### 1. Install Dependencies

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install -y docker.io docker-compose nginx certbot

# macOS (with Homebrew)
brew install docker docker-compose nginx
```

### 2. Create Production Configuration

```bash
# Create production directory
mkdir -p /opt/terminusdb
cd /opt/terminusdb

# Create docker-compose.yml (see configuration/ directory)
```

### 3. SSL/TLS Setup

```bash
# Get SSL certificate (replace your-domain.com)
sudo certbot --nginx -d terminusdb.your-domain.com

# Configure nginx proxy (see configuration/nginx.conf)
```

## Development Setup

### Local Development with Docker Compose

```bash
# In your FAP monorepo root
cd docs/third-party/terminusdb/configuration

# Start development stack
docker-compose -f development.yml up -d

# View logs
docker-compose -f development.yml logs -f terminusdb
```

### Native Installation (Advanced)

For development or custom builds:

```bash
# Install SWI-Prolog
# Ubuntu
sudo apt install swi-prolog

# macOS
brew install swi-prolog

# Clone and build TerminusDB
git clone https://github.com/terminusdb/terminusdb.git
cd terminusdb
make install
```

## Client SDKs

### JavaScript/Node.js

```bash
# In your project directory
pnpm add @terminusdb/terminusdb-client

# Or globally for CLI tools
pnpm add -g @terminusdb/terminusdb-client
```

### Python

```bash
# Install Python client
pip install terminusdb-client

# Or with conda
conda install -c conda-forge terminusdb-client
```

## Initial Configuration

### 1. Create Admin User

```javascript
// Using JavaScript client
const TerminusClient = require('@terminusdb/terminusdb-client')

const client = new TerminusClient.WOQLClient('http://localhost:6363/', {
  user: 'admin',
  key: 'root'
})

// Change default password
await client.updateUser('admin', {
  password: 'your-secure-password'
})
```

### 2. Create Organization Database

```javascript
// Create Acumen Desktop organization
await client.createDatabase('acumen-desktop', {
  label: 'Acumen Desktop Corporate Data',
  comment: 'Single source of truth for Acumen Desktop Software Canada Inc.',
  public: false
})
```

### 3. Set Up Access Control

```javascript
// Create team for different access levels
await client.createRole('directors', {
  actions: ['read', 'write', 'create_database']
})

await client.createRole('employees', {
  actions: ['read', 'write']
})

await client.createRole('ai-agents', {
  actions: ['read', 'write'],
  rate_limit: 1000 // requests per hour
})
```

## Verification Checklist

- [ ] TerminusDB server running on port 6363
- [ ] Web interface accessible
- [ ] Admin password changed from default
- [ ] Test database created successfully
- [ ] Client SDK installed and working
- [ ] Backup strategy configured
- [ ] Monitoring setup (optional)

## Troubleshooting

### Common Issues

1. **Port 6363 already in use**
   ```bash
   # Find what's using the port
   lsof -i :6363
   
   # Use different port
   docker run -p 6364:6363 terminusdb/terminusdb-server:latest
   ```

2. **Permission denied on data directory**
   ```bash
   # Fix permissions
   sudo chown -R $(whoami):$(whoami) ~/terminusdb-data
   ```

3. **Docker out of memory**
   ```bash
   # Increase Docker memory limit to 4GB+
   # Docker Desktop → Settings → Resources → Memory
   ```

### Getting Help

- **Documentation**: https://terminusdb.com/docs/
- **GitHub Issues**: https://github.com/terminusdb/terminusdb/issues
- **Discord Community**: https://discord.gg/yTJKAma
- **Stack Overflow**: Tag with `terminusdb`

## Next Steps

1. **Schema Design** → See `integration/schemas/`
2. **API Integration** → See `integration/api-examples.js`
3. **MCP Server Setup** → See `integration/mcp-server/`
4. **Backup Configuration** → See `configuration/backup.md`
