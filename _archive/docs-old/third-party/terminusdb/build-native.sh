#!/bin/bash
# TerminusDB Native Build Script for macOS
# Part of the FAP Monorepo - Acumen Desktop Software Canada Inc.

set -e  # Exit on any error

echo "🚀 TerminusDB Native Build Script"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check if we're on macOS
if [[ "$OSTYPE" != "darwin"* ]]; then
    echo -e "${RED}❌ This script is designed for macOS. For other platforms, see installation.md${NC}"
    exit 1
fi

# Check if Homebrew is installed
if ! command -v brew &> /dev/null; then
    echo -e "${RED}❌ Homebrew is required but not installed.${NC}"
    echo "Install Homebrew: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
    exit 1
fi

echo -e "${GREEN}✅ Homebrew found${NC}"

# Check and install dependencies
echo -e "${YELLOW}📦 Checking dependencies...${NC}"

dependencies=("gmp" "swi-prolog" "rust")
missing_deps=()

for dep in "${dependencies[@]}"; do
    if ! brew list "$dep" &> /dev/null; then
        missing_deps+=("$dep")
    else
        echo -e "${GREEN}✅ $dep is installed${NC}"
    fi
done

if [ ${#missing_deps[@]} -ne 0 ]; then
    echo -e "${YELLOW}📥 Installing missing dependencies: ${missing_deps[*]}${NC}"
    brew install "${missing_deps[@]}"
fi

# Verify Xcode Command Line Tools
if ! xcode-select -p &> /dev/null; then
    echo -e "${YELLOW}📥 Installing Xcode Command Line Tools...${NC}"
    xcode-select --install
    echo "Please complete the Xcode Command Line Tools installation and run this script again."
    exit 1
fi

echo -e "${GREEN}✅ All dependencies satisfied${NC}"

# Create build directory
BUILD_DIR="$HOME/terminusdb-build"
echo -e "${YELLOW}📁 Creating build directory: $BUILD_DIR${NC}"
mkdir -p "$BUILD_DIR"
cd "$BUILD_DIR"

# Clone or update repository
if [ -d "terminusdb" ]; then
    echo -e "${YELLOW}🔄 Updating existing repository...${NC}"
    cd terminusdb
    git pull origin main
else
    echo -e "${YELLOW}📥 Cloning TerminusDB repository...${NC}"
    git clone https://github.com/terminusdb/terminusdb.git
    cd terminusdb
fi

# Build TerminusDB
echo -e "${YELLOW}🔨 Building TerminusDB (this may take 5-10 minutes)...${NC}"
make clean || true  # Clean previous builds, ignore errors
make

# Verify build
if [ -x "./terminusdb" ]; then
    echo -e "${GREEN}✅ Build successful!${NC}"
    echo -e "${GREEN}📍 TerminusDB binary location: $(pwd)/terminusdb${NC}"
else
    echo -e "${RED}❌ Build failed - terminusdb binary not found${NC}"
    exit 1
fi

# Test the binary
echo -e "${YELLOW}🧪 Testing binary...${NC}"
if ./terminusdb --help > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Binary test passed${NC}"
else
    echo -e "${RED}❌ Binary test failed${NC}"
    exit 1
fi

# Create data directory
DATA_DIR="$HOME/terminusdb-data"
echo -e "${YELLOW}📁 Creating data directory: $DATA_DIR${NC}"
mkdir -p "$DATA_DIR"

# Initialize database (if not already initialized)
if [ ! -d "$DATA_DIR/system" ]; then
    echo -e "${YELLOW}🔧 Initializing system database...${NC}"
    echo "Please enter a secure admin password for TerminusDB:"
    read -s -p "Admin password: " ADMIN_PASS
    echo
    
    if [ -z "$ADMIN_PASS" ]; then
        echo -e "${RED}❌ Password cannot be empty${NC}"
        exit 1
    fi
    
    TERMINUSDB_SERVER_DB_PATH="$DATA_DIR" ./terminusdb store init --key "$ADMIN_PASS"
    echo -e "${GREEN}✅ Database initialized${NC}"
else
    echo -e "${GREEN}✅ Database already initialized${NC}"
fi

# Create convenience script
SCRIPT_PATH="$HOME/bin/terminusdb"
echo -e "${YELLOW}📝 Creating convenience script: $SCRIPT_PATH${NC}"
mkdir -p "$HOME/bin"

cat > "$SCRIPT_PATH" << EOF
#!/bin/bash
# TerminusDB Convenience Script
# Generated by FAP Monorepo build script

export TERMINUSDB_SERVER_DB_PATH="$DATA_DIR"
cd "$BUILD_DIR/terminusdb"
exec ./terminusdb "\$@"
EOF

chmod +x "$SCRIPT_PATH"

# Add to PATH if not already there
if [[ ":$PATH:" != *":$HOME/bin:"* ]]; then
    echo -e "${YELLOW}📝 Adding $HOME/bin to PATH in ~/.zshrc${NC}"
    echo 'export PATH="$HOME/bin:$PATH"' >> ~/.zshrc
    echo -e "${YELLOW}💡 Run 'source ~/.zshrc' or restart your terminal to use 'terminusdb' command${NC}"
fi

echo
echo -e "${GREEN}🎉 TerminusDB Native Installation Complete!${NC}"
echo "=================================="
echo -e "${GREEN}📍 Binary location:${NC} $BUILD_DIR/terminusdb/terminusdb"
echo -e "${GREEN}📁 Data directory:${NC} $DATA_DIR"
echo -e "${GREEN}🔧 Convenience script:${NC} $SCRIPT_PATH"
echo
echo -e "${YELLOW}🚀 To start TerminusDB:${NC}"
echo "   terminusdb serve"
echo "   # OR directly:"
echo "   cd $BUILD_DIR/terminusdb && TERMINUSDB_SERVER_DB_PATH=$DATA_DIR ./terminusdb serve"
echo
echo -e "${YELLOW}🌐 Access TerminusDB at:${NC} http://127.0.0.1:6363"
echo -e "${YELLOW}📚 Next steps:${NC} See docs/third-party/terminusdb/integration/ for API examples"
