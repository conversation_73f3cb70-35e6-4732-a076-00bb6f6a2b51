# TerminusDB - Graph Database with Version Control

## Why TerminusDB for Acumen Desktop

TerminusDB is our **primary graph database** and **single source of truth** for all corporate data. It was selected because:

### 🎯 **Perfect Alignment with Monorepo Philosophy**
- **Git-like version control** for data matches our code versioning workflow
- **Branch and merge** data changes just like code changes
- **Commit history** provides complete audit trails for compliance
- **Rollback capabilities** for data recovery and testing

### 🏢 **Ideal for Corporate Structure Management**
- **Hierarchical records** perfect for corporate entities, directors, relationships
- **Semantic document graph** combines JSON documents with graph relationships  
- **Schema evolution** without painful migrations
- **Time-travel queries** to see corporate structure at any point in history

### 🤖 **AI and MCP Integration Ready**
- **Modern APIs** (REST, GraphQL) for AI agent access
- **Python/JavaScript SDKs** for MCP server integration
- **Datalog queries** provide powerful logic-based querying
- **Semantic content infrastructure** built for AI workflows

### 💰 **Cost-Effective and Open Source**
- **Completely open source** - no licensing fees
- **Self-hosted** - full control over our data
- **Community-driven** development with enterprise support options

## Architecture Role

```
┌─────────────────────────────────────────────────────────────┐
│                    FAP Monorepo Architecture                │
├─────────────────────────────────────────────────────────────┤
│  TerminusDB (Single Source of Truth)                       │
│  ├── Corporate Structure (Directors, Entities, Compliance) │
│  ├── Client Relationships (History, Contracts, Projects)   │
│  ├── Code Dependencies (Package Relationships, Versions)   │
│  ├── Business Processes (Workflows, Policies, Changes)     │
│  └── Knowledge Graph (Documents, Ideas, Decisions)         │
├─────────────────────────────────────────────────────────────┤
│  Subordinate Databases (Fed from TerminusDB)               │
│  ├── SQLite (Web serving, fast queries)                    │
│  ├── Vector DB (Semantic search, AI embeddings)            │
│  └── Cache Layers (Redis, in-memory)                       │
└─────────────────────────────────────────────────────────────┘
```

## Key Features for Our Use Case

### Version Control Features
- **Data Branches**: Create branches for major data changes
- **Commit Messages**: Document why data changed
- **Merge Conflicts**: Resolve concurrent data modifications
- **History Tracking**: Full audit trail for compliance
- **Rollback**: Restore to any previous state

### Corporate Data Management
- **Entity Relationships**: Directors ↔ Corporations ↔ Clients
- **Temporal Queries**: "Who were the directors on 2024-08-16?"
- **Change Tracking**: "When did we add this client relationship?"
- **Compliance Audits**: Full history for regulatory requirements

### AI Integration
- **MCP Server Access**: CRUD operations for AI agents
- **Semantic Queries**: Natural language to Datalog translation
- **Knowledge Extraction**: Automatic relationship discovery
- **Multi-modal Data**: Text, documents, structured data

## Next Steps

1. **Installation** → See `installation.md`
2. **Configuration** → See `configuration/` directory
3. **Schema Design** → See `integration/schemas/`
4. **API Integration** → See `integration/api-examples.js`
5. **MCP Server Setup** → See `integration/mcp-server/`

## Official Resources

- **Website**: https://terminusdb.com/
- **Documentation**: https://terminusdb.com/docs/
- **GitHub**: https://github.com/terminusdb/terminusdb
- **Community**: https://discord.gg/yTJKAma
