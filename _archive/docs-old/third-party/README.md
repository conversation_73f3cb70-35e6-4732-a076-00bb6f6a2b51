# Third-Party Software Documentation

This directory contains documentation, configuration, and integration notes for major third-party software used in the FAP monorepo.

## Organization Principle

Each major third-party tool gets its own subdirectory with standardized structure:

```
third-party/
├── terminusdb/           # Graph database (single source of truth)
├── electron/             # Desktop application framework
├── pear/                 # P2P application platform
├── neo4j/                # Alternative graph database (if needed)
├── sqlite/               # Subordinate database for web serving
├── docker/               # Containerization
├── github/               # Version control and CI/CD
└── nodejs/               # Runtime environment
```

## Standard Structure per Tool

Each tool directory follows this pattern:

```
tool-name/
├── README.md             # Overview and why we use this tool
├── installation.md       # Setup and installation instructions
├── configuration/        # Config files and templates
│   ├── development.yml
│   ├── production.yml
│   └── docker-compose.yml
├── integration/          # How it integrates with FAP
│   ├── api-examples.js
│   ├── schemas/
│   └── workflows/
├── troubleshooting.md    # Common issues and solutions
├── upgrade-notes.md      # Version upgrade considerations
└── external-resources.md # Links to official docs, tutorials
```

## Documentation Standards

- **README.md**: Executive summary of why we chose this tool and how it fits our architecture
- **installation.md**: Step-by-step setup for development and production
- **configuration/**: All config files with comments explaining FAP-specific choices
- **integration/**: Code examples and patterns for FAP integration
- **troubleshooting.md**: Solutions to problems we've encountered
- **upgrade-notes.md**: Breaking changes and migration notes between versions
- **external-resources.md**: Curated links to official docs, best practices, community resources

## Maintenance

- Keep documentation synchronized with actual usage
- Update when upgrading tool versions
- Document any FAP-specific customizations or patches
- Include performance benchmarks and monitoring setup where relevant

## Access Control

This documentation is part of the main repository and follows the same access control as other docs:
- Public for open-source tools
- Internal notes in separate private sections if needed
- Integration secrets referenced via environment variables, never committed
