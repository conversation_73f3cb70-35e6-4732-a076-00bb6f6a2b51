<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GitHub Packages Demo - Acumen Desktop FAP Components</title>
    
    <!-- Load @acumen-desktop packages from GitHub Pages static assets -->
    <link rel="stylesheet" href="css/fap-theme.css">
    <link rel="stylesheet" href="css/fap-utils.css">
    <link rel="stylesheet" href="css/fap-chat.css">
    <link rel="stylesheet" href="css/fap-message.css">
    <link rel="stylesheet" href="css/fap-tooltip.css">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: var(--font-family);
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        github-demo {
            display: block;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        demo-header {
            display: block;
            text-align: center;
            margin-bottom: 2rem;
            padding: 2rem;
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }
        
        demo-section {
            display: block;
            margin: 2rem 0;
            padding: 1.5rem;
            background: var(--bg-secondary);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-color);
        }
        
        demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 2rem;
        }
        
        package-info {
            display: block;
            padding: 1rem;
            background: var(--bg-tertiary);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-success);
        }
        
        github-stats {
            display: block;
            padding: 1rem;
            background: var(--bg-tertiary);
            border-radius: var(--border-radius);
            border: 1px solid var(--border-info);
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.8rem;
            font-weight: bold;
        }
        
        .status-published {
            background: var(--success-bg);
            color: var(--success-text);
        }
        
        .github-link {
            color: var(--link-color);
            text-decoration: none;
        }
        
        .github-link:hover {
            text-decoration: underline;
        }
        
        .pages-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: var(--border-radius);
            margin-bottom: 2rem;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            demo-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <github-demo>
        <div class="pages-banner">
            <h2>🚀 Live on GitHub Pages!</h2>
            <p>This demo is hosted on GitHub Pages and consumes packages published to GitHub Packages</p>
        </div>

        <demo-header>
            <h1>🎉 GitHub Packages Demo</h1>
            <p>Showcasing <strong>@acumen-desktop/*</strong> packages published to GitHub Packages</p>
            <p>
                <a href="https://github.com/Acumen-Desktop?tab=packages" class="github-link" target="_blank">
                    View Packages on GitHub →
                </a>
                |
                <a href="https://github.com/Acumen-Desktop/fap_monorepo_july_2025" class="github-link" target="_blank">
                    View Source Code →
                </a>
            </p>
        </demo-header>

        <demo-section>
            <h2>📦 Published Packages Status</h2>
            <p><strong>All packages successfully published to GitHub Packages registry!</strong></p>
            <demo-grid>
                <package-info>
                    <h3>@acumen-desktop/core</h3>
                    <span class="status-badge status-published">✅ Published v0.1.0</span>
                    <p>Core theme system and utilities for all FAP components</p>
                    <small>Registry: GitHub Packages</small>
                </package-info>
                
                <package-info>
                    <h3>@acumen-desktop/chat</h3>
                    <span class="status-badge status-published">✅ Published v0.1.0</span>
                    <p>Interactive chat terminal component with plugin system</p>
                    <small>Registry: GitHub Packages</small>
                </package-info>
                
                <package-info>
                    <h3>@acumen-desktop/tooltip</h3>
                    <span class="status-badge status-published">✅ Published v0.1.0</span>
                    <p>Flexible tooltip component with multiple themes</p>
                    <small>Registry: GitHub Packages</small>
                </package-info>
                
                <package-info>
                    <h3>@acumen-desktop/dev-server</h3>
                    <span class="status-badge status-published">✅ Published v0.1.0</span>
                    <p>Development server utilities and tools</p>
                    <small>Registry: GitHub Packages</small>
                </package-info>
            </demo-grid>
        </demo-section>

        <demo-section>
            <h2>💬 Live Chat Component Demo</h2>
            <p>This chat component is loaded from the published <code>@acumen-desktop/chat</code> package:</p>
            
            <chat-terminal id="github-demo-chat">
                <chat-display id="github-demo-display"></chat-display>
                <chat-input id="github-demo-input"></chat-input>
            </chat-terminal>
        </demo-section>

        <demo-section>
            <h2>🐙 GitHub Integration Showcase</h2>
            <github-stats id="repo-stats">
                <h3>📊 Complete GitHub Ecosystem Test</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem; margin-top: 1rem;">
                    <div class="stat-item">
                        <strong>📦 GitHub Packages:</strong><br>
                        4 packages published privately
                    </div>
                    <div class="stat-item">
                        <strong>🌐 GitHub Pages:</strong><br>
                        Public demo hosted
                    </div>
                    <div class="stat-item">
                        <strong>🔗 Integration:</strong><br>
                        Packages consumed by hosted app
                    </div>
                    <div class="stat-item">
                        <strong>🏗️ Architecture:</strong><br>
                        Vanilla JS, semantic HTML
                    </div>
                </div>
                
                <p style="margin-top: 2rem;">
                    <strong>Repository:</strong> 
                    <a href="https://github.com/Acumen-Desktop/fap_monorepo_july_2025" class="github-link" target="_blank">
                        Acumen-Desktop/fap_monorepo_july_2025
                    </a>
                </p>
            </github-stats>
        </demo-section>

        <demo-section>
            <h2>🔧 Package Installation</h2>
            <p><strong>To use these packages in your own projects:</strong></p>
            
            <h3>1. Configure .npmrc for GitHub Packages</h3>
            <pre><code>@acumen-desktop:registry=https://npm.pkg.github.com
//npm.pkg.github.com/:_authToken=${GITHUB_TOKEN}</code></pre>
            
            <h3>2. Install packages</h3>
            <pre><code>npm install @acumen-desktop/core @acumen-desktop/chat @acumen-desktop/tooltip</code></pre>
            
            <h3>3. Use in your HTML</h3>
            <pre><code>&lt;link rel="stylesheet" href="node_modules/@acumen-desktop/core/css/fap-theme.css"&gt;
&lt;script src="node_modules/@acumen-desktop/chat/js/fap-chat.js"&gt;&lt;/script&gt;</code></pre>

            <h3>4. Philosophy: Complexity-Free Web Development</h3>
            <ul>
                <li>✅ <strong>Plain vanilla</strong> - No frameworks, no build steps</li>
                <li>✅ <strong>Semantic elements</strong> - Self-documenting HTML structure</li>
                <li>✅ <strong>Professional tooling</strong> - pnpm workspaces, changesets, GitHub Packages</li>
                <li>✅ <strong>Modern practices</strong> - Scoped packages, semantic versioning</li>
            </ul>
        </demo-section>
    </github-demo>

    <!-- Load JavaScript modules from static assets -->
    <script src="js/fap-tooltip.js"></script>
    <script src="js/fap-chat-state.js"></script>
    <script src="js/fap-chat-events.js"></script>
    <script src="js/fap-chat-config.js"></script>
    <script src="js/fap-chat-display.js"></script>
    <script src="js/fap-chat-input.js"></script>
    <script src="js/fap-chat-plugins.js"></script>
    <script src="js/fap-chat-api.js"></script>
    
    <script src="github-demo.js"></script>
</body>
</html>
