# Secure Credential Setup for GitHub Packages

**🚨 SECURITY FIRST: Never share credentials with AI assistants or commit them to git!**

## 🔐 Step-by-Step Setup

### 1. Create GitHub Personal Access Token

1. Go to: https://github.com/settings/tokens
2. Click "Generate new token (classic)"
3. Give it a descriptive name: "FAP Monorepo Packages"
4. Select these scopes:
   - ✅ `write:packages` - Upload packages to GitHub Packages
   - ✅ `read:packages` - Download packages from GitHub Packages
   - ✅ `delete:packages` - Delete packages if needed
   - ✅ `repo` - Access your private repository
5. Click "Generate token"
6. **COPY THE TOKEN IMMEDIATELY** (you won't see it again!)

### 2. Secure Local Setup

```bash
# Navigate to your project
cd "/Volumes/Data/Software/2025/Acumen Desktop/Freedom Application Platform/fap_monorepo_july_2025"

# Create .env file (this is already in .gitignore)
cp .env.example .env

# Edit .env and paste your actual token
# Replace "your_github_personal_access_token_here" with your real token
```

### 3. Verify Setup

```bash
# Test authentication
npm whoami --registry=https://npm.pkg.github.com

# Should show your GitHub username
```

### 4. Test Publishing Workflow

```bash
# Make sure dependencies are installed
pnpm install

# Create a changeset (when you have changes)
pnpm changeset

# Version packages
pnpm version

# Publish to GitHub Packages
pnpm publish
```

## 🛡️ Security Best Practices

### **DO:**
- ✅ Store token in `.env` file (already in `.gitignore`)
- ✅ Use environment variables: `GITHUB_TOKEN=your_token`
- ✅ Set token expiration (GitHub recommends 90 days max)
- ✅ Use minimal required scopes
- ✅ Regenerate tokens periodically

### **DON'T:**
- ❌ Share tokens with anyone (including AI assistants!)
- ❌ Commit `.env` files to git
- ❌ Put tokens in code comments
- ❌ Use tokens with excessive permissions
- ❌ Store tokens in plain text files outside `.env`

## 🔄 Repository Configuration

Your repository is configured for:
- **Repository**: `https://github.com/Acumen-Desktop/fap_monorepo_july_2025`
- **Package scope**: `@fap/*`
- **Registry**: GitHub Packages (private)

## 📦 Package URLs

Once published, your packages will be available at:
- `@fap/core`: https://github.com/Acumen-Desktop/fap_monorepo_july_2025/packages
- `@fap/chat`: https://github.com/Acumen-Desktop/fap_monorepo_july_2025/packages
- `@fap/tooltip`: https://github.com/Acumen-Desktop/fap_monorepo_july_2025/packages

## 🆘 Troubleshooting

### "Authentication failed"
```bash
# Check if token is set
echo $GITHUB_TOKEN

# If empty, source your environment
source .env
# or restart your terminal
```

### "Package not found"
- Ensure repository exists and is accessible
- Check token has correct permissions
- Verify package name matches repository owner

### "Version already exists"
```bash
# Check current version
npm view @fap/core version --registry=https://npm.pkg.github.com

# Update version with changeset
pnpm changeset
pnpm version
```

## 🔄 Team Collaboration

To allow other developers to use your packages:

1. **Invite them to the repository** (Settings → Manage access)
2. **They need to create their own token** (same scopes)
3. **They configure their own `.npmrc`**:
   ```
   @fap:registry=https://npm.pkg.github.com
   //npm.pkg.github.com/:_authToken=${GITHUB_TOKEN}
   ```

## 🚀 Ready to Publish!

Once your `.env` is configured with your token:

```bash
# Your first publish
pnpm changeset
pnpm version
pnpm publish

# Check your packages at:
# https://github.com/Acumen-Desktop?tab=packages
```

---

*Remember: Keep your credentials secure and never share them. This setup gives you a private, professional environment to develop and test your FAP components!*
