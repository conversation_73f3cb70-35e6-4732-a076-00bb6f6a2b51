# GitHub Packages Setup & Learning Guide

This guide helps you set up GitHub Packages for private development and explores GitHub features you can learn along the way.

## 🎯 Why GitHub Packages for Development?

**Perfect for your current phase:**
- **Private practice** - Mess up safely without public visibility
- **Free for private repos** - No cost during development
- **Team collaboration** - Invite trusted developers to test
- **GitHub feature exploration** - Learn the ecosystem deeply
- **Seamless transition** - Easy switch to NPM when ready

## 🔧 Setup Steps

### 1. Create GitHub Personal Access Token

1. Go to GitHub → Settings → Developer settings → Personal access tokens → Tokens (classic)
2. Click "Generate new token (classic)"
3. Select scopes:
   - ✅ `write:packages` - Upload packages
   - ✅ `read:packages` - Download packages
   - ✅ `delete:packages` - Delete packages (for cleanup)
   - ✅ `repo` - Access private repositories
4. Copy the token (save it securely!)

### 2. Configure Environment

```bash
# Add to your shell profile (~/.zshrc, ~/.bashrc, etc.)
export GITHUB_TOKEN=your_token_here

# Or create a .env file (add to .gitignore!)
echo "GITHUB_TOKEN=your_token_here" >> .env
```

### 3. Test the Setup

```bash
# Install dependencies (should work with GitHub Packages now)
pnpm install

# Test publishing (when you have a component ready)
pnpm changeset
pnpm version
pnpm publish
```

## 🎓 GitHub Features to Explore

### **Package Management**
- **Package versions** - Practice semantic versioning
- **Package visibility** - Private vs public packages
- **Package permissions** - Team access control
- **Package insights** - Download statistics

### **Repository Features**
- **GitHub Actions** - Automate publishing, testing
- **Releases** - Create formal releases with changelogs
- **Issues & Projects** - Track component development
- **Wiki** - Document your architecture decisions

### **Collaboration Features**
- **Pull Requests** - Review component changes
- **Code Reviews** - Get feedback on your vanilla approach
- **Discussions** - Community around your components
- **Teams** - Organize collaborators

### **Advanced Features**
- **GitHub Pages** - Host component documentation
- **Codespaces** - Cloud development environment
- **Security** - Dependency scanning, secret scanning
- **Insights** - Repository analytics

## 🚀 Suggested Learning Path

### **Week 1: Basic Package Publishing**
```bash
# Practice the publishing workflow
pnpm changeset
pnpm version
pnpm publish

# Check your packages at: https://github.com/USERNAME?tab=packages
```

### **Week 2: GitHub Actions**
Create `.github/workflows/publish.yml`:
```yaml
name: Publish Packages
on:
  push:
    branches: [main]
jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: pnpm install
      - run: pnpm build
      - run: pnpm publish
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
```

### **Week 3: Team Collaboration**
- Invite a trusted developer
- Practice pull request workflow
- Use GitHub Issues for component planning
- Set up branch protection rules

### **Week 4: Documentation & Releases**
- Create component documentation with GitHub Pages
- Practice creating releases with changelogs
- Use GitHub Discussions for architecture decisions

## 🔄 Publishing Workflow

### **Development Cycle**
```bash
# 1. Make changes to components
# 2. Create changeset
pnpm changeset

# 3. Version packages
pnpm version

# 4. Publish to GitHub Packages
pnpm publish

# 5. Test consuming packages in apps
pnpm --filter chat-simple install
```

### **Consuming Your Packages**
Other developers (or you in different projects) can use:
```bash
# Configure their .npmrc
echo "@fap:registry=https://npm.pkg.github.com" >> .npmrc
echo "//npm.pkg.github.com/:_authToken=\${GITHUB_TOKEN}" >> .npmrc

# Install your components
npm install @fap/core @fap/chat @fap/tooltip
```

## 🎨 Component Development Practice

### **Perfect Components to Practice With:**
1. **@fap/core** - Theme system, utilities
2. **@fap/tooltip** - Simple, reusable component
3. **@fap/chat** - Complex component with dependencies
4. **@fap/toast** - New component to practice workflow

### **Practice Scenarios:**
- **Breaking changes** - Practice major version bumps
- **Bug fixes** - Practice patch releases
- **New features** - Practice minor version bumps
- **Dependency updates** - Practice workspace dependency management

## 🔄 Transition to NPM (Future)

When ready for public release:

```bash
# 1. Comment out GitHub Packages lines in .npmrc
# 2. Update changeset config if needed
# 3. Publish to NPM
pnpm publish

# Your packages will now be public on NPM!
```

## 🆘 Troubleshooting

### **Authentication Issues**
```bash
# Check token permissions
npm whoami --registry=https://npm.pkg.github.com

# Re-authenticate
npm login --registry=https://npm.pkg.github.com
```

### **Package Not Found**
- Ensure repository is accessible to the token
- Check package name matches repository owner
- Verify .npmrc configuration

### **Publishing Errors**
- Check token has `write:packages` permission
- Ensure package version is higher than existing
- Verify repository exists and is accessible

---

*This setup gives you a safe, private environment to perfect your components while learning valuable GitHub features. When you're ready to showcase your "complexity-free" approach to the world, switching to NPM will be seamless!*
