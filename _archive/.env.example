# GitHub Packages Authentication
# Copy this file to .env and fill in your actual token
# NEVER commit .env to git!

GITHUB_TOKEN=your_github_personal_access_token_here

# Instructions:
# 1. Go to GitHub → Settings → Developer settings → Personal access tokens
# 2. Create token with these scopes:
#    - write:packages
#    - read:packages  
#    - delete:packages
#    - repo (if using private repositories)
# 3. Copy the token and paste it above
# 4. Save this file as .env (not .env.example)
