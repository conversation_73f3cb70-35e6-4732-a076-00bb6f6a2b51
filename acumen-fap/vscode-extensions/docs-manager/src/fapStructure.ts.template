/**
 * FAP Documentation Structure Definition
 * 
 * Hardcoded structure for the FAP monorepo documentation system.
 * This defines where different types of documentation should be placed.
 */

export interface FAPSection {
  description: string;
  patterns: string[];
  subdirs: string[] | { [key: string]: string[] };
  examples: string[];
}

export const FAP_STRUCTURE: { [key: string]: FAPSection } = {
  'setup': {
    description: 'How do I get started?',
    patterns: [
      'install', 'installation', 'setup', 'getting started', 
      'prerequisites', 'requirements', 'environment', 'configure'
    ],
    subdirs: [
      'installation.md',
      'development-environment.md', 
      'first-time-setup.md',
      'logseq-setup.md'
    ],
    examples: [
      'How to install dependencies',
      'Setting up development environment',
      'First-time contributor guide'
    ]
  },

  'fap': {
    description: 'FAP platform documentation',
    patterns: [
      'component', 'fap-', 'semantic', 'vanilla', 'custom element',
      'platform', 'architecture', 'api', 'guide', 'tutorial'
    ],
    subdirs: {
      'components': ['fap-core', 'fap-chat', 'fap-tooltip', 'fap-modal'],
      'guides': ['creating-components', 'building-applications', 'styling-guide'],
      'architecture': ['semantic-elements', 'css-organization', 'vanilla-js-patterns'],
      'api': ['core-api', 'chat-api', 'tooltip-api']
    },
    examples: [
      'fap-chat component documentation',
      'How to create a new FAP component',
      'FAP architecture overview'
    ]
  },

  'landmax': {
    description: 'LandMax project documentation',
    patterns: [
      'landmax', 'business', 'requirements', 'user guide',
      'workflow', 'process', 'client'
    ],
    subdirs: {
      'user-guides': ['getting-started', 'advanced-features'],
      'setup': ['installation', 'configuration'],
      'business': ['requirements', 'workflows']
    },
    examples: [
      'LandMax user guide',
      'Business requirements document',
      'LandMax setup instructions'
    ]
  },

  'corporate': {
    description: 'Business and corporate information',
    patterns: [
      'corporate', 'business', 'legal', 'compliance', 'policy',
      'process', 'procedure', 'finance', 'contract', 'entity'
    ],
    subdirs: {
      'legal': ['entity-structure', 'compliance', 'contracts'],
      'processes': ['development-workflow', 'documentation-standards'],
      'finance': ['budgets', 'expenses', 'invoicing']
    },
    examples: [
      'Corporate entity structure',
      'Development workflow process',
      'Legal compliance documentation'
    ]
  },

  'integrations': {
    description: 'Third-party integrations and external services',
    patterns: [
      'integration', 'third-party', 'external', 'api', 'service',
      'terminusdb', 'github', 'deployment', 'hosting', 'database'
    ],
    subdirs: {
      'terminusdb': ['installation', 'configuration', 'api-examples'],
      'github-packages': ['setup', 'publishing', 'authentication'],
      'deployment': ['hosting', 'ci-cd', 'monitoring']
    },
    examples: [
      'TerminusDB integration guide',
      'GitHub Packages setup',
      'Deployment configuration'
    ]
  },

  'development': {
    description: 'How to contribute and develop',
    patterns: [
      'contribute', 'development', 'convention', 'standard', 'workflow',
      'tool', 'testing', 'debugging', 'publishing', 'release'
    ],
    subdirs: {
      'conventions': ['naming-conventions', 'coding-standards', 'documentation-standards'],
      'tools': ['pnpm-workspaces', 'testing', 'debugging'],
      'publishing': ['github-packages', 'versioning', 'release-process']
    },
    examples: [
      'Coding standards document',
      'How to publish packages',
      'Development tool setup'
    ]
  },

  'reference': {
    description: 'Quick reference and lookup information',
    patterns: [
      'reference', 'api', 'lookup', 'index', 'glossary',
      'troubleshooting', 'faq', 'cheat sheet'
    ],
    subdirs: [
      'api-reference.md',
      'component-index.md',
      'troubleshooting.md',
      'glossary.md'
    ],
    examples: [
      'Complete API reference',
      'Component index',
      'Troubleshooting guide'
    ]
  },

  'planning': {
    description: 'Project planning and coordination',
    patterns: [
      'planning', 'roadmap', 'session', 'notes', 'decision',
      'architecture decision', 'adr', 'timeline', 'milestone'
    ],
    subdirs: {
      'session-notes': ['current-session', 'previous-sessions'],
      'architecture-decisions': ['adr-001-structure', 'adr-002-ai'],
      'roadmaps': ['platform-roadmap', 'feature-roadmap']
    },
    examples: [
      'Development session notes',
      'Architecture decision record',
      'Project roadmap'
    ]
  }
};

/**
 * Analyze content and suggest the best FAP structure location
 */
export function suggestFAPLocation(content: string, filename: string): {
  suggestedPath: string;
  confidence: number;
  reasoning: string;
  alternativeLocations: string[];
} {
  const analysis = {
    suggestedPath: 'reference/',
    confidence: 0.1,
    reasoning: 'Default fallback location',
    alternativeLocations: [] as string[]
  };

  // Analyze filename first
  const filenameAnalysis = analyzeFilename(filename);
  if (filenameAnalysis.confidence > 0.7) {
    return filenameAnalysis;
  }

  // Analyze content
  const contentAnalysis = analyzeContent(content);
  if (contentAnalysis.confidence > filenameAnalysis.confidence) {
    return contentAnalysis;
  }

  return filenameAnalysis.confidence > 0 ? filenameAnalysis : contentAnalysis;
}

function analyzeFilename(filename: string): any {
  const lower = filename.toLowerCase();
  
  // FAP component patterns
  if (lower.includes('fap-') && (lower.includes('component') || lower.includes('.md'))) {
    return {
      suggestedPath: 'fap/components/',
      confidence: 0.9,
      reasoning: 'Filename indicates FAP component documentation',
      alternativeLocations: ['fap/api/', 'reference/']
    };
  }

  // Setup/installation patterns
  if (lower.includes('install') || lower.includes('setup') || lower.includes('getting-started')) {
    return {
      suggestedPath: 'setup/',
      confidence: 0.8,
      reasoning: 'Filename suggests setup or installation documentation',
      alternativeLocations: ['development/tools/']
    };
  }

  // API documentation patterns
  if (lower.includes('api') && !lower.includes('fap-')) {
    return {
      suggestedPath: 'reference/',
      confidence: 0.7,
      reasoning: 'Filename suggests API reference documentation',
      alternativeLocations: ['fap/api/']
    };
  }

  return {
    suggestedPath: 'reference/',
    confidence: 0.1,
    reasoning: 'No clear filename pattern detected',
    alternativeLocations: []
  };
}

function analyzeContent(content: string): any {
  const lower = content.toLowerCase();
  const scores: { [key: string]: number } = {};

  // Score each section based on keyword matches
  for (const [section, config] of Object.entries(FAP_STRUCTURE)) {
    let score = 0;
    for (const pattern of config.patterns) {
      const regex = new RegExp(`\\b${pattern}\\b`, 'gi');
      const matches = content.match(regex);
      if (matches) {
        score += matches.length;
      }
    }
    scores[section] = score;
  }

  // Find the highest scoring section
  const maxScore = Math.max(...Object.values(scores));
  const bestSection = Object.keys(scores).find(key => scores[key] === maxScore);

  if (maxScore === 0) {
    return {
      suggestedPath: 'reference/',
      confidence: 0.1,
      reasoning: 'No clear content patterns detected',
      alternativeLocations: []
    };
  }

  const confidence = Math.min(0.95, maxScore / 10); // Scale confidence
  const alternatives = Object.keys(scores)
    .filter(key => key !== bestSection && scores[key] > 0)
    .sort((a, b) => scores[b] - scores[a])
    .slice(0, 2)
    .map(key => `${key}/`);

  return {
    suggestedPath: `${bestSection}/`,
    confidence,
    reasoning: `Content analysis found ${maxScore} relevant keywords for ${bestSection}`,
    alternativeLocations: alternatives
  };
}

/**
 * Get all valid FAP documentation paths
 */
export function getAllFAPPaths(): string[] {
  const paths: string[] = [];
  
  for (const [section, config] of Object.entries(FAP_STRUCTURE)) {
    paths.push(`${section}/`);
    
    if (Array.isArray(config.subdirs)) {
      config.subdirs.forEach(subdir => {
        paths.push(`${section}/${subdir}`);
      });
    } else {
      Object.keys(config.subdirs).forEach(subdir => {
        paths.push(`${section}/${subdir}/`);
      });
    }
  }
  
  return paths.sort();
}

/**
 * Validate if a path follows FAP structure conventions
 */
export function validateFAPPath(filePath: string): {
  isValid: boolean;
  suggestion?: string;
  reasoning: string;
} {
  const normalizedPath = filePath.replace(/^docs(-intuitive)?\//, '');
  const pathParts = normalizedPath.split('/');
  
  if (pathParts.length === 0) {
    return {
      isValid: false,
      reasoning: 'Empty path'
    };
  }

  const section = pathParts[0];
  if (!FAP_STRUCTURE[section]) {
    const suggestion = Object.keys(FAP_STRUCTURE).find(key => 
      key.toLowerCase().includes(section.toLowerCase()) ||
      section.toLowerCase().includes(key.toLowerCase())
    );
    
    return {
      isValid: false,
      suggestion: suggestion ? `${suggestion}/` : 'reference/',
      reasoning: `Unknown section '${section}'. Did you mean '${suggestion}'?`
    };
  }

  return {
    isValid: true,
    reasoning: `Valid FAP structure path in '${section}' section`
  };
}
