{"name": "fap-docs-manager", "displayName": "FAP Documentation Manager", "description": "Simple documentation management for FAP monorepo", "version": "0.1.0", "publisher": "acumen-desktop", "private": true, "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "keywords": ["documentation", "fap", "monorepo", "markdown"], "main": "./extension.js", "activationEvents": ["workspaceContains:**/docs/", "workspaceContains:**/acumen-fap/", "onLanguage:markdown"], "contributes": {"commands": [{"command": "fapDocs.openManager", "title": "Open Documentation Manager", "category": "FAP Docs", "icon": "$(book)"}, {"command": "fapDocs.suggestLocation", "title": "Suggest File Location", "category": "FAP Docs", "icon": "$(target)"}, {"command": "fapDocs.validateStructure", "title": "Validate Documentation", "category": "FAP Docs", "icon": "$(check)"}, {"command": "fapDocs.createFromTemplate", "title": "New <PERSON> from Template", "category": "FAP Docs", "icon": "$(add)"}], "menus": {"explorer/context": [{"command": "fapDocs.suggestLocation", "when": "resourceExtname == .md", "group": "fapDocs@1"}], "editor/context": [{"command": "fapDocs.suggestLocation", "when": "resourceExtname == .md", "group": "fapDocs@1"}]}, "keybindings": [{"command": "fapDocs.openManager", "key": "ctrl+shift+d", "mac": "cmd+shift+d"}, {"command": "fapDocs.suggestLocation", "key": "ctrl+shift+l", "mac": "cmd+shift+l", "when": "editorTextFocus && resourceExtname == .md"}], "configuration": {"title": "FAP Documentation Manager", "properties": {"fapDocs.enableAI": {"type": "boolean", "default": true, "description": "Enable AI-powered content analysis"}, "fapDocs.autoValidate": {"type": "boolean", "default": true, "description": "Automatically validate documentation on save"}}}}, "scripts": {"package": "vsce package", "install-local": "code --install-extension fap-docs-manager-0.1.0.vsix"}, "devDependencies": {"vsce": "^2.15.0"}, "repository": {"type": "git", "url": "https://github.com/acumen-desktop/fap-docs-manager"}}