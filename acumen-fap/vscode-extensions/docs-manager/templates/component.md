# Component Name

**Brief description of what this component does and its purpose in the FAP ecosystem**

## 🎯 **Overview**

Describe the component's main functionality and key features:
- Primary use case
- Key capabilities
- Integration points with other FAP components

## 🏗️ **Architecture**

### **Semantic Structure**
```html
<component-name>
  <component-header>
    <!-- Header content -->
  </component-header>
  <component-body>
    <!-- Main content -->
  </component-body>
  <component-footer>
    <!-- Footer content -->
  </component-footer>
</component-name>
```

### **CSS Organization**
Following FAP's alphabetical CSS organization:
- `component-name.css` - Main component styles
- `component-header.css` - Header-specific styles
- `component-body.css` - Body-specific styles
- `component-footer.css` - Footer-specific styles

## 📦 **Installation**

### **Via GitHub Packages**
```bash
pnpm add @acumen-desktop/component-name
```

### **Direct Include**
```html
<!-- CSS -->
<link rel="stylesheet" href="path/to/component-name.css">

<!-- JavaScript -->
<script src="path/to/component-name.js"></script>
```

## 🚀 **Usage**

### **Basic Example**
```html
<component-name>
  <component-header>
    <h2>Component Title</h2>
  </component-header>
  <component-body>
    <p>Component content goes here</p>
  </component-body>
</component-name>
```

### **Advanced Example**
```html
<component-name class="custom-theme" data-config="advanced">
  <component-header>
    <h2>Advanced Component</h2>
    <component-controls>
      <button type="button">Action</button>
    </component-controls>
  </component-header>
  <component-body>
    <component-section>
      <h3>Section Title</h3>
      <p>Section content</p>
    </component-section>
  </component-body>
</component-name>
```

## ⚙️ **API Reference**

### **JavaScript API**
```javascript
// Access component API
const component = window.fap.componentName;

// Initialize component
component.init(element, options);

// Public methods
component.show();
component.hide();
component.update(data);
component.destroy();
```

### **Configuration Options**
```javascript
const options = {
  theme: 'default',           // Theme variant
  autoInit: true,            // Auto-initialize on load
  responsive: true,          // Enable responsive behavior
  customClass: '',           // Additional CSS classes
  onInit: function() {},     // Initialization callback
  onUpdate: function() {},   // Update callback
  onDestroy: function() {}   // Destruction callback
};
```

### **Events**
```javascript
// Listen for component events
element.addEventListener('component:init', function(event) {
  console.log('Component initialized', event.detail);
});

element.addEventListener('component:update', function(event) {
  console.log('Component updated', event.detail);
});

element.addEventListener('component:destroy', function(event) {
  console.log('Component destroyed', event.detail);
});
```

## 🎨 **Styling**

### **CSS Custom Properties**
```css
component-name {
  --component-background: var(--fap-surface);
  --component-color: var(--fap-on-surface);
  --component-border: var(--fap-outline);
  --component-radius: var(--fap-radius);
  --component-spacing: var(--fap-spacing);
}
```

### **State Classes**
- `.loading` - Loading state
- `.error` - Error state
- `.disabled` - Disabled state
- `.hidden` - Hidden state

### **Theme Variants**
- `.theme-primary` - Primary theme
- `.theme-secondary` - Secondary theme
- `.theme-accent` - Accent theme

## 🔗 **Dependencies**

### **Required**
- [FAP Core](fap-core.md) - Base theme system and utilities

### **Optional**
- [FAP Tooltip](fap-tooltip.md) - For enhanced tooltips
- [FAP Modal](fap-modal.md) - For modal interactions

## 📁 **File Structure**

```
fap-component-name/
├── css/
│   ├── component-name.css
│   ├── component-header.css
│   ├── component-body.css
│   └── component-footer.css
├── js/
│   ├── component-name-api.js
│   ├── component-name-core.js
│   └── component-name-utils.js
├── examples/
│   ├── basic.html
│   ├── advanced.html
│   └── themes.html
├── package.json
└── README.md
```

## 🧪 **Testing**

### **Manual Testing**
1. Open `examples/basic.html` in browser
2. Verify component renders correctly
3. Test all interactive features
4. Check responsive behavior

### **Automated Testing**
```javascript
// Component test suite
describe('ComponentName', function() {
  it('should initialize correctly', function() {
    // Test initialization
  });
  
  it('should respond to API calls', function() {
    // Test API methods
  });
  
  it('should handle events properly', function() {
    // Test event handling
  });
});
```

## 🐛 **Troubleshooting**

### **Common Issues**

**Component not rendering:**
- Check CSS is loaded correctly
- Verify JavaScript is included after HTML
- Ensure FAP Core is loaded first

**Styling issues:**
- Check CSS custom properties are defined
- Verify theme classes are applied correctly
- Check for CSS conflicts with other styles

**JavaScript errors:**
- Ensure `window.fap` namespace exists
- Check browser console for error messages
- Verify component dependencies are loaded

## 📚 **Related Documentation**

- [FAP Platform Overview](../README.md)
- [Creating Components Guide](../guides/creating-components.md)
- [CSS Organization](../architecture/css-organization.md)
- [API Reference](../api/README.md)

## 📝 **Changelog**

### **v1.0.0**
- Initial release
- Basic component functionality
- Core API implementation

---

**Questions?** Check the [FAP documentation](../README.md) or [create an issue](https://github.com/acumen-desktop/issues).
