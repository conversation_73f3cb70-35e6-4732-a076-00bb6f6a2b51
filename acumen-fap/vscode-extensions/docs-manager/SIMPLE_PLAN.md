# FAP Documentation Manager - Simple Vanilla Approach

**Pure JavaScript VSCode extension - No TypeScript, no build steps, no complexity**

## 🎯 **Simplified Philosophy**

Following FAP principles:
- **Pure Vanilla JavaScript** - No TypeScript, no compilation
- **No Build Steps** - Direct file execution
- **Simple Structure** - Minimal files, maximum clarity
- **PNPM Integration** - Use pnpm instead of npm
- **Zero Dependencies** - Only VSCode API

## 🏗️ **Ultra-Simple Architecture**

```
docs-manager/
├── package.json              # Extension manifest (only required file)
├── extension.js              # Main entry point (pure JS)
├── fap-structure.js          # FAP documentation logic
├── webview/
│   ├── panel.html           # Documentation manager UI
│   ├── style.css            # Clean styling
│   └── script.js            # Webview JavaScript
├── templates/               # Documentation templates
│   ├── component.md
│   ├── guide.md
│   └── api.md
└── README.md
```

## 📦 **Minimal Package.json**

```json
{
  "name": "fap-docs-manager",
  "displayName": "FAP Documentation Manager",
  "description": "Simple documentation management for FAP monorepo",
  "version": "0.1.0",
  "engines": { "vscode": "^1.60.0" },
  "main": "./extension.js",
  "activationEvents": [
    "workspaceContains:**/docs/",
    "onLanguage:markdown"
  ],
  "contributes": {
    "commands": [
      {
        "command": "fapDocs.openManager",
        "title": "Open Documentation Manager",
        "category": "FAP Docs"
      },
      {
        "command": "fapDocs.suggestLocation",
        "title": "Suggest File Location",
        "category": "FAP Docs"
      }
    ],
    "keybindings": [
      {
        "command": "fapDocs.openManager",
        "key": "ctrl+shift+d",
        "mac": "cmd+shift+d"
      }
    ]
  }
}
```

## 🚀 **Pure JavaScript Implementation**

### **Main Extension File**
```javascript
// extension.js - Pure vanilla JavaScript
const vscode = require('vscode');
const path = require('path');
const fs = require('fs');
const { analyzeFAPContent, FAP_STRUCTURE } = require('./fap-structure');

function activate(context) {
  console.log('FAP Documentation Manager activated');

  // Command: Open Documentation Manager
  const openManagerCommand = vscode.commands.registerCommand('fapDocs.openManager', () => {
    createDocumentationPanel(context);
  });

  // Command: Suggest Location
  const suggestLocationCommand = vscode.commands.registerCommand('fapDocs.suggestLocation', () => {
    suggestFileLocation();
  });

  context.subscriptions.push(openManagerCommand, suggestLocationCommand);
}

function createDocumentationPanel(context) {
  // Create webview panel in editor area
  const panel = vscode.window.createWebviewPanel(
    'fapDocsManager',
    'FAP Documentation Manager',
    vscode.ViewColumn.One,
    {
      enableScripts: true,
      retainContextWhenHidden: true
    }
  );

  // Load HTML content
  const htmlPath = path.join(context.extensionPath, 'webview', 'panel.html');
  const htmlContent = fs.readFileSync(htmlPath, 'utf8');
  
  // Replace placeholders with actual data
  const fapStructureJson = JSON.stringify(FAP_STRUCTURE, null, 2);
  const finalHtml = htmlContent.replace('{{FAP_STRUCTURE}}', fapStructureJson);
  
  panel.webview.html = finalHtml;

  // Handle messages from webview
  panel.webview.onDidReceiveMessage(
    message => {
      switch (message.command) {
        case 'analyzeFile':
          handleAnalyzeFile(message.filePath, panel);
          break;
        case 'createFromTemplate':
          handleCreateFromTemplate(message.templateType);
          break;
      }
    }
  );
}

function suggestFileLocation() {
  const activeEditor = vscode.window.activeTextEditor;
  if (!activeEditor) {
    vscode.window.showWarningMessage('No active file to analyze');
    return;
  }

  const document = activeEditor.document;
  const content = document.getText();
  const filename = path.basename(document.fileName);

  const analysis = analyzeFAPContent(content, filename);
  
  // Show quick pick with suggestions
  const items = [
    {
      label: `📍 ${analysis.suggestedPath}`,
      description: `Confidence: ${Math.round(analysis.confidence * 100)}%`,
      detail: analysis.reasoning
    },
    ...analysis.alternativeLocations.map(alt => ({
      label: `📂 ${alt}`,
      description: 'Alternative location',
      detail: 'Consider this location as well'
    }))
  ];

  vscode.window.showQuickPick(items, {
    placeHolder: 'Suggested locations for this file'
  }).then(selection => {
    if (selection) {
      vscode.window.showInformationMessage(`Selected: ${selection.label}`);
    }
  });
}

function handleAnalyzeFile(filePath, panel) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const filename = path.basename(filePath);
    const analysis = analyzeFAPContent(content, filename);
    
    // Send analysis back to webview
    panel.webview.postMessage({
      command: 'analysisResult',
      analysis: analysis
    });
  } catch (error) {
    vscode.window.showErrorMessage(`Error analyzing file: ${error.message}`);
  }
}

function handleCreateFromTemplate(templateType) {
  // Implementation for creating files from templates
  vscode.window.showInformationMessage(`Creating ${templateType} template...`);
}

function deactivate() {}

module.exports = { activate, deactivate };
```

### **FAP Structure Logic**
```javascript
// fap-structure.js - Pure JavaScript FAP logic
const FAP_STRUCTURE = {
  'setup': {
    description: 'How do I get started?',
    patterns: ['install', 'setup', 'getting started', 'prerequisites'],
    examples: ['Installation guide', 'Development setup']
  },
  'fap': {
    description: 'FAP platform documentation',
    patterns: ['component', 'fap-', 'semantic', 'vanilla', 'custom element'],
    examples: ['Component docs', 'Architecture guides']
  },
  'development': {
    description: 'How do I contribute?',
    patterns: ['contribute', 'convention', 'standard', 'workflow'],
    examples: ['Coding standards', 'Publishing guides']
  },
  'integrations': {
    description: 'How do I integrate X?',
    patterns: ['integration', 'third-party', 'api', 'terminusdb'],
    examples: ['TerminusDB setup', 'GitHub integration']
  },
  'reference': {
    description: 'Where can I look things up?',
    patterns: ['reference', 'api', 'lookup', 'troubleshooting'],
    examples: ['API docs', 'Troubleshooting guide']
  }
};

function analyzeFAPContent(content, filename) {
  const analysis = {
    suggestedPath: 'reference/',
    confidence: 0.1,
    reasoning: 'Default fallback location',
    alternativeLocations: []
  };

  // Simple keyword matching
  const contentLower = content.toLowerCase();
  const filenameLower = filename.toLowerCase();
  
  let bestMatch = '';
  let bestScore = 0;

  for (const [section, config] of Object.entries(FAP_STRUCTURE)) {
    let score = 0;
    
    // Check filename
    for (const pattern of config.patterns) {
      if (filenameLower.includes(pattern)) score += 2;
      if (contentLower.includes(pattern)) score += 1;
    }
    
    if (score > bestScore) {
      bestScore = score;
      bestMatch = section;
    }
  }

  if (bestScore > 0) {
    analysis.suggestedPath = `${bestMatch}/`;
    analysis.confidence = Math.min(0.95, bestScore / 5);
    analysis.reasoning = `Found ${bestScore} matching patterns for ${bestMatch}`;
    
    // Add alternatives
    analysis.alternativeLocations = Object.keys(FAP_STRUCTURE)
      .filter(key => key !== bestMatch)
      .slice(0, 2)
      .map(key => `${key}/`);
  }

  return analysis;
}

module.exports = { FAP_STRUCTURE, analyzeFAPContent };
```

### **Webview HTML**
```html
<!-- webview/panel.html - Clean HTML interface -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAP Documentation Manager</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <header>
        <h1>📚 FAP Documentation Manager</h1>
        <div class="actions">
            <button id="analyze-current">🎯 Analyze Current File</button>
            <button id="validate-docs">✅ Validate Structure</button>
            <button id="new-template">📝 New from Template</button>
        </div>
    </header>

    <main>
        <section id="current-analysis">
            <h2>Current File Analysis</h2>
            <div id="analysis-result">
                <p>Select a markdown file and click "Analyze Current File"</p>
            </div>
        </section>

        <section id="fap-structure">
            <h2>FAP Documentation Structure</h2>
            <div id="structure-tree">
                <!-- Populated by JavaScript -->
            </div>
        </section>

        <section id="quick-actions">
            <h2>Quick Actions</h2>
            <div class="action-grid">
                <button class="action-card" data-action="setup">
                    <h3>🚀 Setup</h3>
                    <p>Getting started docs</p>
                </button>
                <button class="action-card" data-action="fap">
                    <h3>🧩 FAP Platform</h3>
                    <p>Component & platform docs</p>
                </button>
                <button class="action-card" data-action="development">
                    <h3>🛠️ Development</h3>
                    <p>Contributing & conventions</p>
                </button>
                <button class="action-card" data-action="integrations">
                    <h3>🔗 Integrations</h3>
                    <p>Third-party services</p>
                </button>
            </div>
        </section>
    </main>

    <script src="script.js"></script>
    <script>
        // Inject FAP structure data
        window.FAP_STRUCTURE = {{FAP_STRUCTURE}};
    </script>
</body>
</html>
```

### **Webview CSS**
```css
/* webview/style.css - Clean FAP-style CSS */
body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    margin: 0;
    padding: 20px;
    background: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
}

header {
    border-bottom: 1px solid var(--vscode-panel-border);
    margin-bottom: 20px;
    padding-bottom: 15px;
}

header h1 {
    margin: 0 0 10px 0;
    color: var(--vscode-titleBar-activeForeground);
}

.actions {
    display: flex;
    gap: 10px;
}

button {
    background: var(--vscode-button-background);
    border: none;
    border-radius: 4px;
    color: var(--vscode-button-foreground);
    cursor: pointer;
    padding: 8px 16px;
}

button:hover {
    background: var(--vscode-button-hoverBackground);
}

section {
    margin-bottom: 30px;
}

section h2 {
    border-bottom: 1px solid var(--vscode-panel-border);
    margin-bottom: 15px;
    padding-bottom: 5px;
}

.action-grid {
    display: grid;
    gap: 15px;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.action-card {
    background: var(--vscode-editor-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 8px;
    padding: 15px;
    text-align: left;
    transition: all 0.2s;
}

.action-card:hover {
    border-color: var(--vscode-focusBorder);
    transform: translateY(-2px);
}

.action-card h3 {
    margin: 0 0 5px 0;
}

.action-card p {
    color: var(--vscode-descriptionForeground);
    margin: 0;
}

#analysis-result {
    background: var(--vscode-textBlockQuote-background);
    border-left: 4px solid var(--vscode-textBlockQuote-border);
    padding: 15px;
}

.confidence-high { color: var(--vscode-gitDecoration-addedResourceForeground); }
.confidence-medium { color: var(--vscode-gitDecoration-modifiedResourceForeground); }
.confidence-low { color: var(--vscode-gitDecoration-deletedResourceForeground); }
```

### **Webview JavaScript**
```javascript
// webview/script.js - Pure vanilla JavaScript
(function() {
    const vscode = acquireVsCodeApi();

    // Initialize interface
    document.addEventListener('DOMContentLoaded', () => {
        setupEventListeners();
        renderFAPStructure();
    });

    function setupEventListeners() {
        document.getElementById('analyze-current').addEventListener('click', () => {
            vscode.postMessage({ command: 'analyzeFile' });
        });

        document.getElementById('validate-docs').addEventListener('click', () => {
            vscode.postMessage({ command: 'validateStructure' });
        });

        document.getElementById('new-template').addEventListener('click', () => {
            showTemplateSelector();
        });

        // Action cards
        document.querySelectorAll('.action-card').forEach(card => {
            card.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                navigateToSection(action);
            });
        });
    }

    function renderFAPStructure() {
        const container = document.getElementById('structure-tree');
        const structure = window.FAP_STRUCTURE;

        let html = '<ul class="structure-list">';
        for (const [section, config] of Object.entries(structure)) {
            html += `
                <li class="structure-item">
                    <strong>${section}/</strong>
                    <span class="description">${config.description}</span>
                    <div class="examples">
                        ${config.examples.map(ex => `<span class="example">${ex}</span>`).join('')}
                    </div>
                </li>
            `;
        }
        html += '</ul>';

        container.innerHTML = html;
    }

    function showTemplateSelector() {
        const templates = ['component', 'guide', 'api', 'integration'];
        // Simple template selection - could be enhanced with a modal
        vscode.postMessage({
            command: 'createFromTemplate',
            templateType: 'component' // Default for now
        });
    }

    function navigateToSection(section) {
        vscode.postMessage({
            command: 'navigateToSection',
            section: section
        });
    }

    // Listen for messages from extension
    window.addEventListener('message', event => {
        const message = event.data;

        switch (message.command) {
            case 'analysisResult':
                displayAnalysisResult(message.analysis);
                break;
        }
    });

    function displayAnalysisResult(analysis) {
        const container = document.getElementById('analysis-result');
        const confidenceClass = getConfidenceClass(analysis.confidence);

        container.innerHTML = `
            <h3>📍 Suggested Location</h3>
            <p><strong>${analysis.suggestedPath}</strong></p>
            <p class="${confidenceClass}">Confidence: ${Math.round(analysis.confidence * 100)}%</p>
            <p><em>${analysis.reasoning}</em></p>
            ${analysis.alternativeLocations.length > 0 ? `
                <h4>Alternative Locations:</h4>
                <ul>
                    ${analysis.alternativeLocations.map(alt => `<li>${alt}</li>`).join('')}
                </ul>
            ` : ''}
        `;
    }

    function getConfidenceClass(confidence) {
        if (confidence > 0.7) return 'confidence-high';
        if (confidence > 0.4) return 'confidence-medium';
        return 'confidence-low';
    }
})();
```

## 🚀 **Simple Setup Process**

### **No Yo, No TypeScript, No Build Steps**
```bash
# 1. Create directory structure manually
mkdir -p acumen-fap/vscode-extensions/docs-manager/webview
mkdir -p acumen-fap/vscode-extensions/docs-manager/templates

# 2. Create files directly (no generators)
# Copy the code above into respective files

# 3. Use pnpm for any dependencies (minimal)
cd acumen-fap/vscode-extensions/docs-manager
echo '{}' > package.json
# Edit package.json with the minimal version above

# 4. Test immediately
# Press F5 in VSCode to launch Extension Development Host

# 5. Package when ready
npx vsce package
```

## 🎯 **Benefits of Simple Approach**

### **✅ Aligns with FAP Philosophy**
- **Pure vanilla JavaScript** - No compilation, no build steps
- **Simple file structure** - Easy to understand and modify
- **Direct execution** - Files run as-is in VSCode
- **Minimal dependencies** - Only VSCode API

### **✅ Faster Development**
- **No build step** - Edit and test immediately
- **No TypeScript** - Direct JavaScript debugging
- **No complex tooling** - Just VSCode and your editor
- **Instant feedback** - F5 to test changes

### **✅ Easier Maintenance**
- **Readable code** - Pure JavaScript, no compilation artifacts
- **Simple debugging** - Standard JavaScript debugging
- **No version conflicts** - No complex dependency chains
- **Team friendly** - Anyone can understand and modify

## 📋 **Next Steps**

1. **Create the simple structure** manually (no generators)
2. **Copy the vanilla JavaScript code** above
3. **Test immediately** with F5 in VSCode
4. **Iterate quickly** without build steps
5. **Add AI integration** as simple HTTP calls to Claude Code

**This approach is much more aligned with your FAP philosophy - simple, direct, and effective!**
