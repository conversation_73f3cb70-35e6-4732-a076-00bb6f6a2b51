{"name": "fap-docs-manager", "displayName": "FAP Documentation Manager", "description": "Intelligent documentation management for the FAP monorepo", "version": "0.1.0", "publisher": "acumen-desktop", "private": true, "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "keywords": ["documentation", "fap", "monorepo", "markdown", "organization"], "activationEvents": ["workspaceContains:**/docs/", "workspaceContains:**/acumen-fap/", "onLanguage:markdown"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "fapDocs.openManager", "title": "Open Documentation Manager", "category": "FAP Docs", "icon": "$(book)"}, {"command": "fapDocs.suggestLocation", "title": "Suggest File Location", "category": "FAP Docs", "icon": "$(target)"}, {"command": "fapDocs.validateStructure", "title": "Validate Documentation", "category": "FAP Docs", "icon": "$(check)"}, {"command": "fapDocs.createFromTemplate", "title": "New <PERSON> from Template", "category": "FAP Docs", "icon": "$(add)"}, {"command": "fapDocs.analyzeWithAI", "title": "Analyze with AI", "category": "FAP Docs", "icon": "$(sparkle)"}], "menus": {"explorer/context": [{"command": "fapDocs.suggestLocation", "when": "resourceExtname == .md", "group": "fapDocs@1"}, {"command": "fapDocs.analyzeWithAI", "when": "resourceExtname == .md", "group": "fapDocs@2"}], "editor/context": [{"command": "fapDocs.suggestLocation", "when": "resourceExtname == .md", "group": "fapDocs@1"}, {"command": "fapDocs.analyzeWithAI", "when": "resourceExtname == .md", "group": "fapDocs@2"}], "commandPalette": [{"command": "fapDocs.openManager", "when": "workspaceHasDocumentation"}]}, "keybindings": [{"command": "fapDocs.openManager", "key": "ctrl+shift+d", "mac": "cmd+shift+d", "when": "workspaceHasDocumentation"}, {"command": "fapDocs.suggestLocation", "key": "ctrl+shift+l", "mac": "cmd+shift+l", "when": "editorTextFocus && resourceExtname == .md"}], "configuration": {"title": "FAP Documentation Manager", "properties": {"fapDocs.enableAI": {"type": "boolean", "default": true, "description": "Enable AI-powered content analysis"}, "fapDocs.aiProvider": {"type": "string", "enum": ["claude", "local"], "default": "claude", "description": "AI provider for content analysis"}, "fapDocs.autoValidate": {"type": "boolean", "default": true, "description": "Automatically validate documentation on save"}, "fapDocs.templatePath": {"type": "string", "default": "docs/_internal/templates/", "description": "Path to documentation templates"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "package": "vsce package", "install-local": "code --install-extension fap-docs-manager-0.1.0.vsix"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0", "vsce": "^2.15.0"}, "dependencies": {"axios": "^1.6.0", "marked": "^9.1.0"}, "repository": {"type": "git", "url": "https://github.com/acumen-desktop/fap-docs-manager"}, "bugs": {"url": "https://github.com/acumen-desktop/fap-docs-manager/issues"}, "homepage": "https://github.com/acumen-desktop/fap-docs-manager#readme"}