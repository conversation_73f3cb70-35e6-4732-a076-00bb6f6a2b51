# FAP Documentation Manager

**VSCode extension for intelligent documentation management in the FAP monorepo**

## 🎯 **What It Does**

This extension eliminates the confusion of where to put documentation in the FAP monorepo by providing:

- **Smart Location Suggestions** - AI-powered analysis tells you exactly where files should go
- **Editor Area Interface** - Clean webview panels in the middle editor area (not sidebar clutter)
- **FAP-Specific Logic** - Hardcoded for FAP's question-based documentation structure
- **Real-time Validation** - Catch broken links and structural issues as you work
- **Template Integration** - Quick access to FAP documentation templates

## 🚀 **Quick Start**

### **Installation**
```bash
# Install the VSIX package
code --install-extension fap-docs-manager-0.1.0.vsix
```

### **Usage**
```bash
# Open Documentation Manager
Ctrl+Shift+D (Cmd+Shift+D on Mac)

# Suggest location for current file
Ctrl+Shift+L (Cmd+Shift+L on Mac)

# Or use Command Palette
Ctrl+Shift+P → "FAP Docs: Open Documentation Manager"
```

## 🎨 **Features**

### **Smart File Placement**
- Analyzes file content and suggests optimal location
- Understands FAP structure: `setup/`, `fap/`, `development/`, etc.
- Provides confidence scores and reasoning
- Suggests alternative locations

### **Editor Area Interface**
- Clean webview panels in the editor area (middle of screen)
- No sidebar clutter - works alongside your existing layout
- Interactive documentation structure visualization
- Real-time analysis and suggestions

### **AI Integration**
- Claude Code sub-agent integration for content analysis
- Smart template selection based on content type
- Cross-reference suggestions
- Quality scoring and improvement recommendations

### **FAP-Specific Logic**
- Hardcoded for FAP monorepo structure
- Understands component documentation patterns
- Recognizes setup, development, and integration docs
- Validates against FAP naming conventions

## 📁 **Supported File Types**

- **Markdown files** (`.md`) - Primary focus
- **Documentation templates** - Auto-populated templates
- **README files** - Special handling for navigation files
- **CLAUDE.md files** - AI context file management

## 🎯 **Commands**

| Command | Shortcut | Description |
|---------|----------|-------------|
| `FAP Docs: Open Documentation Manager` | `Ctrl+Shift+D` | Open main documentation panel |
| `FAP Docs: Suggest File Location` | `Ctrl+Shift+L` | Analyze current file and suggest location |
| `FAP Docs: Validate Documentation` | - | Check for broken links and structure issues |
| `FAP Docs: New Doc from Template` | - | Create new documentation from template |
| `FAP Docs: Analyze with AI` | - | Deep AI analysis of current file |

## 🏗️ **FAP Structure Integration**

The extension understands FAP's question-based documentation structure:

```
docs/
├── setup/           # "How do I get started?"
├── fap/             # "Where's the FAP platform docs?"
├── landmax/         # "Where's LandMax documentation?"
├── corporate/       # "Where's business information?"
├── integrations/    # "How do I integrate X?"
├── development/     # "How do I contribute/develop?"
├── reference/       # "Where can I look things up?"
└── planning/        # "What's the roadmap/status?"
```

### **Smart Suggestions**
- **Component docs** → `fap/components/`
- **Setup guides** → `setup/`
- **API reference** → `fap/api/` or `reference/`
- **Integration guides** → `integrations/service-name/`
- **Contributing docs** → `development/`

## 🤖 **AI Features**

### **Content Analysis**
- Analyzes file content to determine optimal location
- Understands FAP terminology and patterns
- Provides confidence scores and reasoning
- Suggests improvements and cross-references

### **Template Intelligence**
- Smart template selection based on content type
- Auto-populates metadata and cross-references
- Suggests related documentation to link

### **Quality Scoring**
- Evaluates documentation completeness
- Checks for missing cross-references
- Validates against FAP conventions
- Suggests improvements

## 🔧 **Configuration**

```json
{
  "fapDocs.enableAI": true,
  "fapDocs.aiProvider": "claude",
  "fapDocs.autoValidate": true,
  "fapDocs.templatePath": "docs/_internal/templates/"
}
```

## 🛠️ **Development**

### **Building from Source**
```bash
# Clone repository
git clone https://github.com/acumen-desktop/fap-docs-manager.git
cd fap-docs-manager

# Install dependencies
npm install

# Compile TypeScript
npm run compile

# Package extension
npm run package
```

### **Testing**
```bash
# Run tests
npm test

# Launch Extension Development Host
# Press F5 in VSCode
```

## 📊 **Privacy & Security**

- **Private extension** - Not published to marketplace
- **Local processing** - Content analysis happens locally when possible
- **Claude Code integration** - Uses existing authentication
- **No data collection** - Extension doesn't send data externally

## 🤝 **Contributing**

This is a private extension for the FAP team. For issues or feature requests:

1. Create an issue in the private repository
2. Follow FAP development conventions
3. Test thoroughly with FAP monorepo content

## 📋 **Roadmap**

### **Phase 1** ✅
- [x] Basic webview panel in editor area
- [x] File location suggestions
- [x] FAP structure integration
- [x] Command palette integration

### **Phase 2** 🚧
- [ ] AI-powered content analysis
- [ ] Template system integration
- [ ] Real-time validation
- [ ] Cross-reference suggestions

### **Phase 3** 📋
- [ ] Auto-navigation updates
- [ ] CLAUDE.md hierarchy management
- [ ] Quality scoring
- [ ] Advanced AI features

## 🆘 **Support**

- **Documentation**: See [PROJECT_PLAN.md](PROJECT_PLAN.md) for detailed architecture
- **Setup**: Follow [SETUP_GUIDE.md](SETUP_GUIDE.md) for development setup
- **Issues**: Create issues in the private GitHub repository

---

**Built specifically for the FAP monorepo to eliminate documentation organization confusion.**
