// FAP Documentation Manager - Main Extension
// Pure vanilla JavaScript - no TypeScript, no build steps

const vscode = require('vscode');
const path = require('path');
const fs = require('fs');
const { analyzeFAPContent, FAP_STRUCTURE } = require('./fap-structure');

/**
 * Extension activation
 */
function activate(context) {
  console.log('FAP Documentation Manager activated');

  // Register commands
  const commands = [
    vscode.commands.registerCommand('fapDocs.openManager', () => {
      createDocumentationPanel(context);
    }),
    
    vscode.commands.registerCommand('fapDocs.suggestLocation', () => {
      suggestFileLocation();
    }),
    
    vscode.commands.registerCommand('fapDocs.validateStructure', () => {
      validateDocumentationStructure();
    }),
    
    vscode.commands.registerCommand('fapDocs.createFromTemplate', () => {
      createFromTemplate(context);
    })
  ];

  context.subscriptions.push(...commands);
  
  // Show welcome message on first activation
  vscode.window.showInformationMessage('FAP Documentation Manager is ready! Press Ctrl+Shift+D to open.');
}

/**
 * Create the main documentation panel in editor area
 */
function createDocumentationPanel(context) {
  const panel = vscode.window.createWebviewPanel(
    'fapDocsManager',
    'FAP Documentation Manager',
    vscode.ViewColumn.One,
    {
      enableScripts: true,
      retainContextWhenHidden: true,
      localResourceRoots: [vscode.Uri.file(path.join(context.extensionPath, 'webview'))]
    }
  );

  // Load and setup HTML content
  const htmlPath = path.join(context.extensionPath, 'webview', 'panel.html');
  const cssPath = path.join(context.extensionPath, 'webview', 'style.css');
  const jsPath = path.join(context.extensionPath, 'webview', 'script.js');
  
  try {
    let htmlContent = fs.readFileSync(htmlPath, 'utf8');
    
    // Convert file paths to webview URIs
    const cssUri = panel.webview.asWebviewUri(vscode.Uri.file(cssPath));
    const jsUri = panel.webview.asWebviewUri(vscode.Uri.file(jsPath));
    
    // Replace placeholders
    htmlContent = htmlContent
      .replace('{{CSS_URI}}', cssUri.toString())
      .replace('{{JS_URI}}', jsUri.toString())
      .replace('{{FAP_STRUCTURE}}', JSON.stringify(FAP_STRUCTURE, null, 2));
    
    panel.webview.html = htmlContent;
  } catch (error) {
    vscode.window.showErrorMessage(`Failed to load webview: ${error.message}`);
    return;
  }

  // Handle messages from webview
  panel.webview.onDidReceiveMessage(
    message => {
      switch (message.command) {
        case 'analyzeCurrentFile':
          handleAnalyzeCurrentFile(panel);
          break;
        case 'analyzeFile':
          handleAnalyzeFile(message.filePath, panel);
          break;
        case 'createFromTemplate':
          handleCreateFromTemplate(message.templateType, context);
          break;
        case 'navigateToSection':
          handleNavigateToSection(message.section);
          break;
        case 'validateStructure':
          validateDocumentationStructure();
          break;
      }
    }
  );
}

/**
 * Suggest location for current active file
 */
function suggestFileLocation() {
  const activeEditor = vscode.window.activeTextEditor;
  if (!activeEditor) {
    vscode.window.showWarningMessage('No active file to analyze');
    return;
  }

  const document = activeEditor.document;
  if (!document.fileName.endsWith('.md')) {
    vscode.window.showWarningMessage('Please select a markdown file');
    return;
  }

  const content = document.getText();
  const filename = path.basename(document.fileName);
  const analysis = analyzeFAPContent(content, filename);
  
  // Create quick pick items
  const items = [
    {
      label: `📍 ${analysis.suggestedPath}`,
      description: `Confidence: ${Math.round(analysis.confidence * 100)}%`,
      detail: analysis.reasoning,
      path: analysis.suggestedPath
    }
  ];

  // Add alternative locations
  analysis.alternativeLocations.forEach(alt => {
    items.push({
      label: `📂 ${alt}`,
      description: 'Alternative location',
      detail: 'Consider this location as well',
      path: alt
    });
  });

  vscode.window.showQuickPick(items, {
    placeHolder: 'Suggested locations for this file',
    ignoreFocusOut: true
  }).then(selection => {
    if (selection) {
      const message = `Suggested location: ${selection.path}`;
      vscode.window.showInformationMessage(message, 'Open Folder', 'Copy Path').then(action => {
        if (action === 'Copy Path') {
          vscode.env.clipboard.writeText(selection.path);
        } else if (action === 'Open Folder') {
          // Try to open the suggested folder
          const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
          if (workspaceFolder) {
            const fullPath = path.join(workspaceFolder.uri.fsPath, 'docs', selection.path);
            vscode.commands.executeCommand('revealFileInOS', vscode.Uri.file(fullPath));
          }
        }
      });
    }
  });
}

/**
 * Handle analyze current file from webview
 */
function handleAnalyzeCurrentFile(panel) {
  const activeEditor = vscode.window.activeTextEditor;
  if (!activeEditor) {
    panel.webview.postMessage({
      command: 'analysisResult',
      error: 'No active file to analyze'
    });
    return;
  }

  const document = activeEditor.document;
  const content = document.getText();
  const filename = path.basename(document.fileName);
  const analysis = analyzeFAPContent(content, filename);
  
  panel.webview.postMessage({
    command: 'analysisResult',
    analysis: analysis,
    filename: filename
  });
}

/**
 * Handle analyze specific file from webview
 */
function handleAnalyzeFile(filePath, panel) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const filename = path.basename(filePath);
    const analysis = analyzeFAPContent(content, filename);
    
    panel.webview.postMessage({
      command: 'analysisResult',
      analysis: analysis,
      filename: filename
    });
  } catch (error) {
    panel.webview.postMessage({
      command: 'analysisResult',
      error: `Error analyzing file: ${error.message}`
    });
  }
}

/**
 * Create new file from template
 */
function createFromTemplate(context) {
  const templates = [
    { label: '🧩 Component', description: 'FAP component documentation', value: 'component' },
    { label: '📖 Guide', description: 'How-to guide or tutorial', value: 'guide' },
    { label: '⚙️ API Reference', description: 'API documentation', value: 'api' },
    { label: '🔗 Integration', description: 'Third-party integration guide', value: 'integration' }
  ];

  vscode.window.showQuickPick(templates, {
    placeHolder: 'Select template type'
  }).then(selection => {
    if (selection) {
      handleCreateFromTemplate(selection.value, context);
    }
  });
}

/**
 * Handle create from template
 */
function handleCreateFromTemplate(templateType, context) {
  const templatePath = path.join(context.extensionPath, 'templates', `${templateType}.md`);
  
  try {
    const templateContent = fs.readFileSync(templatePath, 'utf8');
    
    // Create new untitled document with template content
    vscode.workspace.openTextDocument({
      content: templateContent,
      language: 'markdown'
    }).then(document => {
      vscode.window.showTextDocument(document);
      vscode.window.showInformationMessage(`Created new ${templateType} document from template`);
    });
  } catch (error) {
    vscode.window.showErrorMessage(`Template not found: ${templateType}.md`);
  }
}

/**
 * Handle navigate to section
 */
function handleNavigateToSection(section) {
  const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
  if (!workspaceFolder) {
    vscode.window.showWarningMessage('No workspace folder found');
    return;
  }

  const sectionPath = path.join(workspaceFolder.uri.fsPath, 'docs', section);
  
  // Try to open the section folder
  vscode.commands.executeCommand('revealFileInOS', vscode.Uri.file(sectionPath));
}

/**
 * Validate documentation structure
 */
function validateDocumentationStructure() {
  vscode.window.showInformationMessage('Documentation validation coming soon!');
  // TODO: Implement validation logic
}

/**
 * Extension deactivation
 */
function deactivate() {
  console.log('FAP Documentation Manager deactivated');
}

module.exports = { activate, deactivate };
