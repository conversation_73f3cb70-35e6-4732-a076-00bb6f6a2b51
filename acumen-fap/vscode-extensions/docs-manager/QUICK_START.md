# FAP Documentation Manager - Quick Start

**Get the extension running in 5 minutes - no TypeScript, no build steps, pure vanilla JavaScript**

## 🚀 **Instant Setup**

### **1. Install VSCE (one-time setup)**
```bash
# Install VSCode extension packager
npm install -g vsce
```

### **2. Test the Extension**
```bash
# Navigate to extension directory
cd acumen-fap/vscode-extensions/docs-manager

# Open in VSCode
code .

# Press F5 to launch Extension Development Host
# This opens a new VSCode window with your extension loaded
```

### **3. Try It Out**
In the Extension Development Host window:
```bash
# Open Command Palette
Ctrl+Shift+P (Cmd+Shift+P on Mac)

# Type: "FAP Docs: Open Documentation Manager"
# Or use keyboard shortcut: Ctrl+Shift+D

# Open a markdown file and try:
# "FAP Docs: Suggest File Location" (Ctrl+Shift+L)
```

## 📦 **Package for Team Use**

### **Create VSIX Package**
```bash
# In the extension directory
vsce package

# This creates: fap-docs-manager-0.1.0.vsix
```

### **Install on Team Machines**
```bash
# Install the VSIX file
code --install-extension fap-docs-manager-0.1.0.vsix

# Or in VSCode:
# Ctrl+Shift+P → "Extensions: Install from VSIX"
# Select the .vsix file
```

## 🎯 **What You Get**

### **Commands Available**
- **`Ctrl+Shift+D`** - Open Documentation Manager panel
- **`Ctrl+Shift+L`** - Suggest location for current file
- **Right-click on .md files** - Context menu with FAP docs options

### **Smart Analysis**
The extension analyzes your markdown files and suggests where they should go:
- **FAP components** → `fap/components/`
- **Setup guides** → `setup/`
- **Integration docs** → `integrations/`
- **API reference** → `reference/` or `fap/api/`

### **Editor Area Interface**
Clean webview panel opens in the middle editor area (not sidebar):
- File analysis results
- FAP structure overview
- Quick navigation to sections
- Recent suggestions history

## 🔧 **Development Workflow**

### **Making Changes**
```bash
# 1. Edit any .js, .html, or .css file
# 2. Press Ctrl+R in Extension Development Host to reload
# 3. Test your changes immediately
```

### **No Build Steps Required**
- Edit `extension.js` directly
- Modify `webview/` files directly
- Changes take effect immediately on reload
- No TypeScript compilation
- No webpack bundling

### **Debugging**
```bash
# Set breakpoints in .js files
# Press F5 to start debugging
# Use console.log() - output appears in "Extension Host" channel
# Right-click in webview → "Inspect Element" for webview debugging
```

## 📁 **File Structure**

```
docs-manager/
├── package.json              # Extension manifest (✅ Ready)
├── extension.js              # Main extension logic (✅ Ready)
├── fap-structure.js          # FAP-specific analysis (✅ Ready)
├── webview/                  # UI components (✅ Ready)
│   ├── panel.html           # Main interface
│   ├── style.css            # Clean styling
│   └── script.js            # Webview JavaScript
├── templates/               # Documentation templates (✅ Ready)
│   └── component.md         # Component template
└── README.md                # Documentation (✅ Ready)
```

## 🎨 **Customization**

### **Add New FAP Sections**
Edit `fap-structure.js`:
```javascript
const FAP_STRUCTURE = {
  'your-new-section': {
    description: 'What does this section contain?',
    patterns: ['keyword1', 'keyword2'],
    examples: ['Example 1', 'Example 2']
  }
  // ... existing sections
};
```

### **Modify UI**
Edit `webview/` files directly:
- `panel.html` - Structure and content
- `style.css` - Styling (uses VSCode CSS variables)
- `script.js` - Behavior and interactions

### **Add New Templates**
Create new `.md` files in `templates/`:
- `guide.md` - How-to guide template
- `api.md` - API documentation template
- `integration.md` - Integration guide template

## 🔒 **Private Distribution**

### **GitHub Repository**
```bash
# Initialize git repository
git init
git add .
git commit -m "Initial FAP Documentation Manager"

# Create private repository on GitHub
# Push to: https://github.com/acumen-desktop/fap-docs-manager
```

### **Team Distribution Options**

**Option 1: VSIX File Sharing**
- Share `fap-docs-manager-0.1.0.vsix` directly
- Team installs with `code --install-extension`

**Option 2: GitHub Releases**
- Create release on GitHub with VSIX as asset
- Team downloads and installs

**Option 3: Internal Package Registry**
- Set up internal npm registry
- Publish extension privately

## 🧪 **Testing Checklist**

### **Basic Functionality**
- [ ] Extension activates when opening FAP monorepo
- [ ] `Ctrl+Shift+D` opens documentation manager
- [ ] `Ctrl+Shift+L` suggests file location
- [ ] Right-click context menu works on .md files
- [ ] Webview panel displays correctly

### **Analysis Accuracy**
- [ ] FAP component files → `fap/components/`
- [ ] Installation guides → `setup/`
- [ ] TerminusDB docs → `integrations/terminusdb/`
- [ ] API docs → `reference/` or `fap/api/`
- [ ] Session notes → `planning/session-notes/`

### **User Experience**
- [ ] Interface is intuitive and responsive
- [ ] Confidence scores make sense
- [ ] Recent suggestions are helpful
- [ ] Quick actions work correctly

## 🆘 **Troubleshooting**

### **Extension Not Loading**
- Check `package.json` syntax
- Verify `activationEvents` match your workspace
- Look for errors in "Extension Host" output channel

### **Webview Not Displaying**
- Check Content Security Policy in `panel.html`
- Verify file paths in `extension.js`
- Use "Inspect Element" on webview for debugging

### **Analysis Not Working**
- Check `fap-structure.js` for syntax errors
- Verify patterns match your content
- Add console.log() for debugging

## ➡️ **Next Steps**

1. **Test with real FAP documentation** - Try analyzing actual files
2. **Customize for your needs** - Adjust patterns and structure
3. **Add AI integration** - Connect to Claude Code sub-agents
4. **Gather team feedback** - Iterate based on usage
5. **Add advanced features** - Validation, auto-navigation updates

---

**Ready to start?** Press `F5` in VSCode to launch the Extension Development Host and try it out!
