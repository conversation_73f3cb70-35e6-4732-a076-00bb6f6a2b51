# FAP Documentation Manager - VSCode Extension

**Private VSCode extension for intelligent documentation management in the FAP monorepo**

## 🎯 **Project Overview**

### **Goal**
Create a FAP-specific VSCode extension that eliminates documentation organization confusion by providing intelligent file placement, validation, and management directly in the editor area.

### **Key Design Decisions**
1. **FAP-Specific**: Hardcoded for FAP monorepo structure and conventions
2. **Editor Area UI**: Uses VSCode's webview panels in the middle editor area
3. **Hardcoded Values**: Start with fixed structure, make configurable later
4. **AI Integration**: Claude Code sub-agents + potential local Hugging Face models
5. **Private Distribution**: GitHub repository with VSIX packaging

## 🏗️ **Architecture Overview**

### **Extension Structure**
```
docs-manager/
├── package.json              # Extension manifest
├── src/
│   ├── extension.ts          # Main entry point
│   ├── webview/              # Editor area UI components
│   │   ├── docsPanel.ts      # Main documentation panel
│   │   ├── templatePanel.ts  # Template selection panel
│   │   └── validationPanel.ts # Structure validation panel
│   ├── commands/             # Command implementations
│   │   ├── suggestLocation.ts
│   │   ├── validateDocs.ts
│   │   └── generateNav.ts
│   ├── ai/                   # AI integration
│   │   ├── claudeAgent.ts    # Claude Code integration
│   │   └── contentAnalyzer.ts # Content analysis
│   ├── core/                 # Core FAP logic
│   │   ├── fapStructure.ts   # Hardcoded FAP structure
│   │   ├── fileAnalyzer.ts   # File content analysis
│   │   └── validator.ts      # Documentation validation
│   └── utils/                # Utilities
│       ├── fileUtils.ts
│       └── templateUtils.ts
├── media/                    # Webview assets (HTML, CSS, JS)
│   ├── main.html
│   ├── style.css
│   └── script.js
├── templates/                # Documentation templates
│   ├── component.md
│   ├── guide.md
│   └── api.md
└── README.md
```

### **UI Architecture: Editor Area Integration**
```typescript
// Uses VSCode's webview API to create panels in editor area
const panel = vscode.window.createWebviewPanel(
  'fapDocsManager',
  'FAP Documentation Manager',
  vscode.ViewColumn.One, // Opens in editor area
  {
    enableScripts: true,
    retainContextWhenHidden: true
  }
);
```

## 📋 **Detailed Implementation Plan**

### **Phase 1: Foundation (Week 1)**

#### **Day 1-2: Project Setup**
- [ ] Initialize extension scaffold with `yo code`
- [ ] Set up TypeScript configuration
- [ ] Create basic package.json with FAP-specific commands
- [ ] Set up GitHub repository (private)
- [ ] Configure build and packaging scripts

#### **Day 3-4: Core Structure**
- [ ] Implement hardcoded FAP documentation structure
- [ ] Create basic file analyzer for content classification
- [ ] Set up webview panel infrastructure
- [ ] Create basic HTML/CSS for editor area UI

#### **Day 5-7: First Command**
- [ ] Implement "Suggest Location" command
- [ ] Create webview panel showing suggestions
- [ ] Add basic content analysis (keywords, file patterns)
- [ ] Test with real FAP documentation files

### **Phase 2: AI Integration (Week 2)**

#### **Day 8-10: Claude Code Integration**
- [ ] Set up Claude Code sub-agent communication
- [ ] Create content analysis prompts for documentation classification
- [ ] Implement AI-powered location suggestions
- [ ] Add confidence scoring for suggestions

#### **Day 11-12: Enhanced Analysis**
- [ ] Improve content analysis with AI
- [ ] Add relationship detection between documents
- [ ] Implement cross-reference suggestions
- [ ] Create smart template selection

#### **Day 13-14: Validation Features**
- [ ] AI-powered structure validation
- [ ] Broken link detection and suggestions
- [ ] Missing documentation identification
- [ ] Quality scoring for documentation

### **Phase 3: Advanced Features (Week 3)**

#### **Day 15-17: Template System**
- [ ] Create template selection panel
- [ ] Implement template customization with AI
- [ ] Add auto-population of metadata
- [ ] Smart cross-reference insertion

#### **Day 18-19: Navigation Management**
- [ ] Auto-update README files when structure changes
- [ ] Maintain component indexes automatically
- [ ] Generate breadcrumb navigation
- [ ] Update CLAUDE.md hierarchy

#### **Day 20-21: Polish & Testing**
- [ ] Comprehensive testing with FAP monorepo
- [ ] Performance optimization
- [ ] Error handling and user feedback
- [ ] Documentation and setup guide

## 🎨 **User Interface Design**

### **Main Documentation Panel**
```html
<!-- Opens in editor area as webview -->
<div class="fap-docs-manager">
  <header>
    <h2>📚 FAP Documentation Manager</h2>
    <div class="actions">
      <button id="suggest-location">🎯 Suggest Location</button>
      <button id="validate-structure">✅ Validate</button>
      <button id="create-from-template">📝 New Doc</button>
    </div>
  </header>
  
  <main>
    <section class="current-file">
      <h3>Current File Analysis</h3>
      <div class="file-info">
        <p><strong>File:</strong> <span id="current-file-path"></span></p>
        <p><strong>Suggested Location:</strong> <span id="suggested-path"></span></p>
        <p><strong>Confidence:</strong> <span id="confidence-score"></span></p>
        <p><strong>Reasoning:</strong> <span id="ai-reasoning"></span></p>
      </div>
    </section>
    
    <section class="structure-overview">
      <h3>FAP Documentation Structure</h3>
      <div class="structure-tree">
        <!-- Interactive tree showing FAP structure -->
      </div>
    </section>
    
    <section class="suggestions">
      <h3>AI Suggestions</h3>
      <div class="suggestion-list">
        <!-- AI-powered suggestions for improvements -->
      </div>
    </section>
  </main>
</div>
```

### **Template Selection Panel**
```html
<div class="template-selector">
  <h3>📝 Create New Documentation</h3>
  <div class="template-grid">
    <div class="template-card" data-template="component">
      <h4>🧩 Component</h4>
      <p>Document a FAP component</p>
    </div>
    <div class="template-card" data-template="guide">
      <h4>📖 Guide</h4>
      <p>How-to guide or tutorial</p>
    </div>
    <div class="template-card" data-template="api">
      <h4>⚙️ API Reference</h4>
      <p>API documentation</p>
    </div>
  </div>
</div>
```

## 🤖 **AI Integration Strategy**

### **Claude Code Sub-Agent Integration**
```typescript
// ai/claudeAgent.ts
export class ClaudeAgent {
  async analyzeDocumentContent(content: string, filename: string) {
    const prompt = `
    Analyze this FAP monorepo documentation file and suggest the best location:
    
    Filename: ${filename}
    Content: ${content}
    
    FAP Structure:
    - setup/ (getting started, installation)
    - fap/ (platform docs, components, guides)
    - development/ (contributing, conventions)
    - integrations/ (third-party services)
    - corporate/ (business information)
    - reference/ (lookup information)
    - planning/ (roadmaps, decisions)
    
    Respond with: {
      "suggestedPath": "path/to/location",
      "confidence": 0.95,
      "reasoning": "explanation",
      "alternativeLocations": ["alt1", "alt2"]
    }
    `;
    
    return await this.callClaudeSubAgent(prompt);
  }
  
  async suggestCrossReferences(content: string, currentPath: string) {
    // AI finds related documentation that should be linked
  }
  
  async validateDocumentStructure(filePath: string) {
    // AI checks if document follows FAP conventions
  }
}
```

### **Local Hugging Face Integration (Future)**
```typescript
// ai/localModel.ts
export class LocalAIModel {
  private model: any; // Hugging Face transformers.js
  
  async initializeModel() {
    // Load lightweight model for content classification
    // Could use: microsoft/DialoGPT-medium or similar
  }
  
  async classifyContent(content: string): Promise<DocumentType> {
    // Local classification for privacy-sensitive content
  }
}
```

## 🔧 **Core FAP Logic**

### **Hardcoded FAP Structure**
```typescript
// core/fapStructure.ts
export const FAP_STRUCTURE = {
  'setup': {
    description: 'How do I get started?',
    patterns: ['install', 'setup', 'getting started', 'prerequisites'],
    subdirs: ['installation.md', 'development-environment.md', 'first-time-setup.md']
  },
  'fap': {
    description: 'FAP platform documentation',
    patterns: ['component', 'fap-', 'semantic', 'vanilla', 'custom element'],
    subdirs: {
      'components': ['fap-core', 'fap-chat', 'fap-tooltip'],
      'guides': ['creating-components', 'building-applications'],
      'architecture': ['semantic-elements', 'css-organization'],
      'api': ['core-api', 'chat-api', 'tooltip-api']
    }
  },
  'development': {
    description: 'How do I contribute?',
    patterns: ['contribute', 'convention', 'standard', 'workflow'],
    subdirs: {
      'conventions': ['naming-conventions', 'coding-standards'],
      'tools': ['pnpm-workspaces', 'testing'],
      'publishing': ['github-packages', 'versioning']
    }
  }
  // ... etc for all FAP structure
};
```

### **Content Analysis Engine**
```typescript
// core/fileAnalyzer.ts
export class FAPFileAnalyzer {
  analyzeContent(content: string, filename: string): AnalysisResult {
    const analysis = {
      suggestedLocation: '',
      confidence: 0,
      reasoning: '',
      detectedType: '',
      keywords: []
    };
    
    // Analyze filename patterns
    if (filename.includes('fap-') && filename.includes('component')) {
      analysis.suggestedLocation = 'fap/components/';
      analysis.confidence = 0.9;
      analysis.reasoning = 'Filename indicates FAP component documentation';
    }
    
    // Analyze content patterns
    const keywords = this.extractKeywords(content);
    const structureMatch = this.matchToStructure(keywords);
    
    return analysis;
  }
  
  private matchToStructure(keywords: string[]): string {
    // Match keywords to FAP structure patterns
    for (const [section, config] of Object.entries(FAP_STRUCTURE)) {
      const matches = keywords.filter(k => 
        config.patterns.some(p => k.toLowerCase().includes(p))
      );
      if (matches.length > 0) {
        return section;
      }
    }
    return 'reference'; // Default fallback
  }
}
```

## 📦 **Commands & Features**

### **Core Commands**
```json
// package.json contributions
{
  "contributes": {
    "commands": [
      {
        "command": "fapDocs.openManager",
        "title": "Open Documentation Manager",
        "category": "FAP Docs",
        "icon": "$(book)"
      },
      {
        "command": "fapDocs.suggestLocation",
        "title": "Suggest File Location",
        "category": "FAP Docs",
        "icon": "$(target)"
      },
      {
        "command": "fapDocs.validateStructure",
        "title": "Validate Documentation",
        "category": "FAP Docs",
        "icon": "$(check)"
      },
      {
        "command": "fapDocs.createFromTemplate",
        "title": "New Doc from Template",
        "category": "FAP Docs",
        "icon": "$(add)"
      }
    ],
    "menus": {
      "explorer/context": [
        {
          "command": "fapDocs.suggestLocation",
          "when": "resourceExtname == .md",
          "group": "fapDocs"
        }
      ],
      "editor/context": [
        {
          "command": "fapDocs.suggestLocation",
          "when": "resourceExtname == .md",
          "group": "fapDocs"
        }
      ]
    }
  }
}
```

### **Keyboard Shortcuts**
```json
{
  "contributes": {
    "keybindings": [
      {
        "command": "fapDocs.openManager",
        "key": "ctrl+shift+d",
        "mac": "cmd+shift+d",
        "when": "workspaceHasDocumentation"
      },
      {
        "command": "fapDocs.suggestLocation",
        "key": "ctrl+shift+l",
        "mac": "cmd+shift+l",
        "when": "editorTextFocus && resourceExtname == .md"
      }
    ]
  }
}
```

## 🚀 **Getting Started Guide**

### **Development Setup**
```bash
# 1. Navigate to extension directory
cd acumen-fap/vscode-extensions/docs-manager

# 2. Initialize extension
npm install -g yo generator-code vsce
yo code
# Choose: New Extension (TypeScript)
# Name: fap-docs-manager

# 3. Install dependencies
npm install

# 4. Development
npm run compile
# Press F5 to launch Extension Development Host

# 5. Package for distribution
vsce package
# Creates: fap-docs-manager-0.1.0.vsix
```

### **Installation & Distribution**
```bash
# Install locally
code --install-extension fap-docs-manager-0.1.0.vsix

# Or install from VSIX file in VSCode:
# Ctrl+Shift+P → "Extensions: Install from VSIX"
```

## 🔒 **Privacy & Security**

### **Private GitHub Repository**
- Repository: `https://github.com/acumen-desktop/fap-docs-manager` (private)
- Team access only
- VSIX distribution for internal use
- No marketplace publication until ready

### **AI Data Handling**
- Claude Code integration uses existing authentication
- Content analysis happens locally when possible
- Sensitive business content stays within FAP monorepo
- Optional local Hugging Face models for complete privacy

## 📊 **Success Metrics**

### **Phase 1 Success Criteria**
- [ ] Extension loads and activates in FAP monorepo
- [ ] Basic webview panel opens in editor area
- [ ] "Suggest Location" command works for 80% of test files
- [ ] Team can install and use extension

### **Phase 2 Success Criteria**
- [ ] AI suggestions have >90% accuracy for FAP content
- [ ] Claude Code integration provides helpful reasoning
- [ ] Validation catches common documentation issues
- [ ] Template system speeds up documentation creation

### **Phase 3 Success Criteria**
- [ ] Zero broken links in FAP documentation
- [ ] Navigation files auto-update correctly
- [ ] Team adoption >80% for new documentation
- [ ] Documentation quality scores improve measurably

## 🔄 **Future Enhancements**

### **Advanced AI Features**
- Content quality scoring and improvement suggestions
- Automatic cross-reference generation
- Documentation gap analysis
- Style and tone consistency checking

### **Integration Opportunities**
- Git hooks for documentation validation
- CI/CD integration for automated checks
- Slack/Teams notifications for documentation updates
- Integration with project management tools

### **Potential Open Source Components**
- Generic documentation structure validator
- Markdown link checker
- Template engine for technical documentation
- VSCode extension framework for documentation management

---

**Next Step**: Run `yo code` in the `docs-manager` directory to initialize the extension scaffold, then implement Phase 1 features.
