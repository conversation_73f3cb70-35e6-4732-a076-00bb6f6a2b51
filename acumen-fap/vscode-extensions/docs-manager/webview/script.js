// FAP Documentation Manager - Webview JavaScript
// Pure vanilla JavaScript - no frameworks, no build steps

(function() {
    'use strict';

    // Get VSCode API
    const vscode = acquireVsCodeApi();
    
    // State management
    let recentSuggestions = [];
    
    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        console.log('FAP Documentation Manager webview loaded');
        setupEventListeners();
        renderFAPStructure();
        loadRecentSuggestions();
    });

    /**
     * Setup all event listeners
     */
    function setupEventListeners() {
        // Main action buttons
        document.getElementById('analyze-current').addEventListener('click', function() {
            analyzeCurrentFile();
        });

        document.getElementById('validate-docs').addEventListener('click', function() {
            validateDocumentation();
        });

        document.getElementById('new-template').addEventListener('click', function() {
            showTemplateSelector();
        });

        // Quick action cards
        document.querySelectorAll('.action-card').forEach(function(card) {
            card.addEventListener('click', function(e) {
                const action = e.currentTarget.dataset.action;
                navigateToSection(action);
            });
        });

        // Listen for messages from extension
        window.addEventListener('message', function(event) {
            const message = event.data;
            handleExtensionMessage(message);
        });
    }

    /**
     * Analyze current file
     */
    function analyzeCurrentFile() {
        showLoading('analyze-current');
        vscode.postMessage({ command: 'analyzeCurrentFile' });
    }

    /**
     * Validate documentation structure
     */
    function validateDocumentation() {
        showLoading('validate-docs');
        vscode.postMessage({ command: 'validateStructure' });
    }

    /**
     * Show template selector
     */
    function showTemplateSelector() {
        // For now, just trigger the extension's template selector
        vscode.postMessage({ command: 'createFromTemplate', templateType: 'component' });
    }

    /**
     * Navigate to documentation section
     */
    function navigateToSection(section) {
        vscode.postMessage({ 
            command: 'navigateToSection', 
            section: section 
        });
    }

    /**
     * Handle messages from the extension
     */
    function handleExtensionMessage(message) {
        switch (message.command) {
            case 'analysisResult':
                hideLoading('analyze-current');
                displayAnalysisResult(message);
                break;
            case 'validationResult':
                hideLoading('validate-docs');
                displayValidationResult(message);
                break;
            default:
                console.log('Unknown message:', message);
        }
    }

    /**
     * Display analysis result
     */
    function displayAnalysisResult(message) {
        const container = document.getElementById('analysis-result');
        
        if (message.error) {
            container.className = 'analysis-result error';
            container.innerHTML = `
                <h3>❌ Analysis Error</h3>
                <p>${message.error}</p>
            `;
            return;
        }

        const analysis = message.analysis;
        const filename = message.filename || 'Current file';
        const confidenceClass = getConfidenceClass(analysis.confidence);
        const confidencePercent = Math.round(analysis.confidence * 100);
        
        container.className = 'analysis-result success';
        container.innerHTML = `
            <h3>📍 Suggested Location for "${filename}"</h3>
            <div style="margin: 12px 0;">
                <strong style="font-size: 16px; color: var(--vscode-textLink-foreground);">${analysis.suggestedPath}</strong>
            </div>
            <div style="margin: 8px 0;">
                <span class="${confidenceClass}">Confidence: ${confidencePercent}%</span>
            </div>
            <div style="margin: 8px 0; font-style: italic;">
                ${analysis.reasoning}
            </div>
            ${analysis.alternativeLocations && analysis.alternativeLocations.length > 0 ? `
                <div style="margin-top: 12px;">
                    <h4>Alternative Locations:</h4>
                    <ul style="margin: 4px 0 0 20px;">
                        ${analysis.alternativeLocations.map(alt => `<li>${alt}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}
            ${analysis.detectedPatterns && analysis.detectedPatterns.length > 0 ? `
                <details style="margin-top: 12px;">
                    <summary style="cursor: pointer; color: var(--vscode-descriptionForeground);">
                        Detected Patterns (${analysis.detectedPatterns.length})
                    </summary>
                    <ul style="margin: 4px 0 0 20px; font-size: 12px;">
                        ${analysis.detectedPatterns.map(pattern => `<li>${pattern}</li>`).join('')}
                    </ul>
                </details>
            ` : ''}
        `;

        // Add to recent suggestions
        addRecentSuggestion({
            filename: filename,
            suggestedPath: analysis.suggestedPath,
            confidence: analysis.confidence,
            timestamp: new Date()
        });
    }

    /**
     * Display validation result
     */
    function displayValidationResult(message) {
        // TODO: Implement validation result display
        console.log('Validation result:', message);
    }

    /**
     * Render FAP structure tree
     */
    function renderFAPStructure() {
        const container = document.getElementById('structure-tree');
        const structure = window.FAP_STRUCTURE;

        if (!structure) {
            container.innerHTML = '<p class="placeholder">FAP structure not loaded</p>';
            return;
        }

        let html = '<div class="structure-list">';
        
        for (const [section, config] of Object.entries(structure)) {
            html += `
                <div class="structure-item">
                    <div class="structure-path">${section}/</div>
                    <div class="structure-description">${config.description}</div>
                    <div class="structure-examples">
                        ${config.examples.map(example => 
                            `<span class="structure-example">${example}</span>`
                        ).join('')}
                    </div>
                </div>
            `;
        }
        
        html += '</div>';
        container.innerHTML = html;
    }

    /**
     * Get confidence CSS class
     */
    function getConfidenceClass(confidence) {
        if (confidence > 0.7) return 'confidence-high';
        if (confidence > 0.4) return 'confidence-medium';
        return 'confidence-low';
    }

    /**
     * Show loading state
     */
    function showLoading(buttonId) {
        const button = document.getElementById(buttonId);
        if (button) {
            button.disabled = true;
            button.classList.add('loading');
            const originalText = button.textContent;
            button.textContent = '⏳ ' + originalText.substring(2);
            button.dataset.originalText = originalText;
        }
    }

    /**
     * Hide loading state
     */
    function hideLoading(buttonId) {
        const button = document.getElementById(buttonId);
        if (button && button.dataset.originalText) {
            button.disabled = false;
            button.classList.remove('loading');
            button.textContent = button.dataset.originalText;
            delete button.dataset.originalText;
        }
    }

    /**
     * Add recent suggestion
     */
    function addRecentSuggestion(suggestion) {
        recentSuggestions.unshift(suggestion);
        
        // Keep only last 5 suggestions
        if (recentSuggestions.length > 5) {
            recentSuggestions = recentSuggestions.slice(0, 5);
        }
        
        renderRecentSuggestions();
        saveRecentSuggestions();
    }

    /**
     * Render recent suggestions
     */
    function renderRecentSuggestions() {
        const container = document.getElementById('recent-list');
        
        if (recentSuggestions.length === 0) {
            container.innerHTML = '<p class="placeholder">No recent suggestions yet</p>';
            return;
        }

        let html = '';
        recentSuggestions.forEach(function(suggestion) {
            const confidenceClass = getConfidenceClass(suggestion.confidence);
            const timeAgo = getTimeAgo(suggestion.timestamp);
            
            html += `
                <div class="recent-item">
                    <div class="recent-path">${suggestion.suggestedPath}</div>
                    <div style="font-size: 12px; color: var(--vscode-descriptionForeground);">
                        ${suggestion.filename} • 
                        <span class="${confidenceClass}">${Math.round(suggestion.confidence * 100)}%</span> • 
                        <span class="recent-time">${timeAgo}</span>
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
    }

    /**
     * Get time ago string
     */
    function getTimeAgo(timestamp) {
        const now = new Date();
        const diff = now - new Date(timestamp);
        const minutes = Math.floor(diff / 60000);
        
        if (minutes < 1) return 'just now';
        if (minutes < 60) return `${minutes}m ago`;
        
        const hours = Math.floor(minutes / 60);
        if (hours < 24) return `${hours}h ago`;
        
        const days = Math.floor(hours / 24);
        return `${days}d ago`;
    }

    /**
     * Save recent suggestions to VSCode state
     */
    function saveRecentSuggestions() {
        try {
            vscode.setState({ recentSuggestions: recentSuggestions });
        } catch (error) {
            console.warn('Failed to save recent suggestions:', error);
        }
    }

    /**
     * Load recent suggestions from VSCode state
     */
    function loadRecentSuggestions() {
        try {
            const state = vscode.getState();
            if (state && state.recentSuggestions) {
                recentSuggestions = state.recentSuggestions;
                renderRecentSuggestions();
            }
        } catch (error) {
            console.warn('Failed to load recent suggestions:', error);
        }
    }

})();
