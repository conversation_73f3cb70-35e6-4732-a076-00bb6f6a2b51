<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src {{CSS_URI}} 'unsafe-inline'; script-src {{JS_URI}} 'unsafe-inline';">
    <title>FAP Documentation Manager</title>
    <link rel="stylesheet" href="{{CSS_URI}}">
</head>
<body>
    <header class="header">
        <h1>📚 FAP Documentation Manager</h1>
        <div class="actions">
            <button id="analyze-current" class="btn btn-primary">🎯 Analyze Current File</button>
            <button id="validate-docs" class="btn btn-secondary">✅ Validate Structure</button>
            <button id="new-template" class="btn btn-secondary">📝 New from Template</button>
        </div>
    </header>

    <main class="main">
        <section id="current-analysis" class="section">
            <h2>📍 Current File Analysis</h2>
            <div id="analysis-result" class="analysis-result">
                <div class="placeholder">
                    <p>💡 Select a markdown file and click "Analyze Current File" to get location suggestions</p>
                    <p>Or use <kbd>Ctrl+Shift+L</kbd> for quick suggestions</p>
                </div>
            </div>
        </section>

        <section id="fap-structure" class="section">
            <h2>🗂️ FAP Documentation Structure</h2>
            <div id="structure-tree" class="structure-tree">
                <!-- Populated by JavaScript -->
            </div>
        </section>

        <section id="quick-actions" class="section">
            <h2>⚡ Quick Actions</h2>
            <div class="action-grid">
                <button class="action-card" data-action="setup">
                    <div class="action-icon">🚀</div>
                    <h3>Setup</h3>
                    <p>Getting started documentation</p>
                </button>
                <button class="action-card" data-action="fap">
                    <div class="action-icon">🧩</div>
                    <h3>FAP Platform</h3>
                    <p>Components & platform docs</p>
                </button>
                <button class="action-card" data-action="development">
                    <div class="action-icon">🛠️</div>
                    <h3>Development</h3>
                    <p>Contributing & conventions</p>
                </button>
                <button class="action-card" data-action="integrations">
                    <div class="action-icon">🔗</div>
                    <h3>Integrations</h3>
                    <p>Third-party services</p>
                </button>
                <button class="action-card" data-action="corporate">
                    <div class="action-icon">🏢</div>
                    <h3>Corporate</h3>
                    <p>Business information</p>
                </button>
                <button class="action-card" data-action="reference">
                    <div class="action-icon">📖</div>
                    <h3>Reference</h3>
                    <p>Quick lookup & APIs</p>
                </button>
            </div>
        </section>

        <section id="recent-suggestions" class="section">
            <h2>🕒 Recent Suggestions</h2>
            <div id="recent-list" class="recent-list">
                <p class="placeholder">No recent suggestions yet</p>
            </div>
        </section>
    </main>

    <footer class="footer">
        <p>FAP Documentation Manager - Intelligent documentation organization for the FAP monorepo</p>
    </footer>

    <script>
        // Inject FAP structure data
        window.FAP_STRUCTURE = {{FAP_STRUCTURE}};
    </script>
    <script src="{{JS_URI}}"></script>
</body>
</html>
