/* FAP Documentation Manager - Clean Vanilla CSS */
/* Following FAP principles: semantic, alphabetical organization */

/* Base styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    background: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    padding: 0;
}

/* Action cards */
.action-card {
    background: var(--vscode-editor-background);
    border: 1px solid var(--vscode-panel-border);
    border-radius: 8px;
    cursor: pointer;
    padding: 16px;
    text-align: center;
    transition: all 0.2s ease;
}

.action-card:hover {
    border-color: var(--vscode-focusBorder);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.action-card h3 {
    color: var(--vscode-titleBar-activeForeground);
    font-size: 16px;
    margin: 8px 0 4px 0;
}

.action-card p {
    color: var(--vscode-descriptionForeground);
    font-size: 12px;
    margin: 0;
}

.action-grid {
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
}

.action-icon {
    font-size: 24px;
    margin-bottom: 4px;
}

/* Analysis result */
.analysis-result {
    background: var(--vscode-textBlockQuote-background);
    border: 1px solid var(--vscode-textBlockQuote-border);
    border-radius: 6px;
    padding: 16px;
}

.analysis-result.error {
    background: var(--vscode-inputValidation-errorBackground);
    border-color: var(--vscode-inputValidation-errorBorder);
}

.analysis-result.success {
    background: var(--vscode-diffEditor-insertedTextBackground);
    border-color: var(--vscode-gitDecoration-addedResourceForeground);
}

/* Buttons */
.btn {
    background: var(--vscode-button-background);
    border: none;
    border-radius: 4px;
    color: var(--vscode-button-foreground);
    cursor: pointer;
    font-size: 13px;
    padding: 8px 16px;
    transition: background-color 0.2s ease;
}

.btn:hover {
    background: var(--vscode-button-hoverBackground);
}

.btn-primary {
    background: var(--vscode-button-background);
}

.btn-secondary {
    background: var(--vscode-button-secondaryBackground);
    color: var(--vscode-button-secondaryForeground);
}

.btn-secondary:hover {
    background: var(--vscode-button-secondaryHoverBackground);
}

/* Confidence indicators */
.confidence-high {
    color: var(--vscode-gitDecoration-addedResourceForeground);
    font-weight: 600;
}

.confidence-medium {
    color: var(--vscode-gitDecoration-modifiedResourceForeground);
    font-weight: 600;
}

.confidence-low {
    color: var(--vscode-gitDecoration-deletedResourceForeground);
    font-weight: 600;
}

/* Footer */
.footer {
    border-top: 1px solid var(--vscode-panel-border);
    color: var(--vscode-descriptionForeground);
    font-size: 12px;
    margin-top: 40px;
    padding: 16px 20px;
    text-align: center;
}

/* Header */
.header {
    border-bottom: 1px solid var(--vscode-panel-border);
    margin-bottom: 24px;
    padding: 20px 20px 16px 20px;
}

.header h1 {
    color: var(--vscode-titleBar-activeForeground);
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 12px;
}

/* Keyboard shortcuts */
kbd {
    background: var(--vscode-keybindingLabel-background);
    border: 1px solid var(--vscode-keybindingLabel-border);
    border-radius: 3px;
    color: var(--vscode-keybindingLabel-foreground);
    font-family: monospace;
    font-size: 11px;
    padding: 2px 6px;
}

/* Main content */
.main {
    padding: 0 20px 20px 20px;
}

/* Placeholders */
.placeholder {
    color: var(--vscode-descriptionForeground);
    font-style: italic;
    text-align: center;
}

/* Recent suggestions */
.recent-list {
    max-height: 200px;
    overflow-y: auto;
}

.recent-item {
    border-bottom: 1px solid var(--vscode-panel-border);
    padding: 8px 0;
}

.recent-item:last-child {
    border-bottom: none;
}

.recent-path {
    color: var(--vscode-textLink-foreground);
    font-weight: 600;
}

.recent-time {
    color: var(--vscode-descriptionForeground);
    font-size: 11px;
}

/* Sections */
.section {
    margin-bottom: 32px;
}

.section h2 {
    border-bottom: 1px solid var(--vscode-panel-border);
    color: var(--vscode-titleBar-activeForeground);
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 16px;
    padding-bottom: 8px;
}

/* Structure tree */
.structure-item {
    border-bottom: 1px solid var(--vscode-panel-border);
    padding: 12px 0;
}

.structure-item:last-child {
    border-bottom: none;
}

.structure-list {
    list-style: none;
}

.structure-path {
    color: var(--vscode-textLink-foreground);
    font-family: monospace;
    font-weight: 600;
}

.structure-description {
    color: var(--vscode-descriptionForeground);
    font-size: 13px;
    margin: 4px 0;
}

.structure-examples {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-top: 6px;
}

.structure-example {
    background: var(--vscode-badge-background);
    border-radius: 12px;
    color: var(--vscode-badge-foreground);
    font-size: 11px;
    padding: 2px 8px;
}

/* Utility classes */
.actions {
    display: flex;
    gap: 8px;
}

.hidden {
    display: none;
}

.loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Responsive design */
@media (max-width: 600px) {
    .action-grid {
        grid-template-columns: 1fr;
    }
    
    .actions {
        flex-direction: column;
    }
    
    .header {
        padding: 16px;
    }
    
    .main {
        padding: 0 16px 16px 16px;
    }
}
