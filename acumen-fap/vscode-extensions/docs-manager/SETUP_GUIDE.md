# FAP Documentation Manager - Setup Guide

**Step-by-step guide to build and deploy the FAP Documentation Manager VSCode extension**

## 🚀 **Quick Start**

### **Prerequisites**
```bash
# Install required tools
npm install -g yo generator-code vsce

# Verify installation
yo --version
vsce --version
```

### **Initialize Extension**
```bash
# Navigate to the extension directory
cd acumen-fap/vscode-extensions/docs-manager

# Generate extension scaffold
yo code

# When prompted, choose:
# ? What type of extension do you want to create? New Extension (TypeScript)
# ? What's the name of your extension? FAP Documentation Manager
# ? What's the identifier of your extension? fap-docs-manager
# ? What's the description of your extension? Intelligent documentation management for the FAP monorepo
# ? Initialize a git repository? Yes
# ? Bundle the source code with webpack? No
# ? Package manager to use? npm

# This will create the basic extension structure
```

### **Replace Generated Files**
```bash
# Replace the generated package.json with our template
cp package.json.template package.json

# The yo generator will have created:
# - src/extension.ts (main entry point)
# - package.json (extension manifest)
# - tsconfig.json (TypeScript config)
# - .vscode/launch.json (debug config)
```

## 📁 **Project Structure After Setup**

```
docs-manager/
├── .vscode/
│   ├── extensions.json
│   ├── launch.json          # Debug configuration
│   ├── settings.json
│   └── tasks.json
├── src/
│   ├── extension.ts         # Main entry point (generated)
│   └── test/               # Test files (generated)
├── out/                    # Compiled JavaScript (after build)
├── node_modules/           # Dependencies
├── .gitignore
├── .eslintrc.json
├── CHANGELOG.md
├── package.json            # Extension manifest
├── README.md
├── tsconfig.json
├── vsc-extension-quickstart.md
├── PROJECT_PLAN.md         # Our detailed plan
├── package.json.template   # Our custom package.json
└── SETUP_GUIDE.md         # This file
```

## 🔧 **Development Workflow**

### **1. Install Dependencies**
```bash
npm install
```

### **2. Compile TypeScript**
```bash
# Compile once
npm run compile

# Watch for changes (recommended during development)
npm run watch
```

### **3. Test Extension**
```bash
# Launch Extension Development Host
# Press F5 in VSCode, or:
npm run test

# This opens a new VSCode window with your extension loaded
# Test your commands in the new window
```

### **4. Debug Extension**
```bash
# Set breakpoints in src/extension.ts
# Press F5 to start debugging
# Use the Extension Development Host to trigger your code
```

## 📦 **Building & Packaging**

### **Create VSIX Package**
```bash
# Package extension for distribution
npm run package
# Creates: fap-docs-manager-0.1.0.vsix

# Install locally for testing
npm run install-local
# Or manually: code --install-extension fap-docs-manager-0.1.0.vsix
```

### **Version Management**
```bash
# Update version in package.json, then:
npm run package

# For pre-release versions:
vsce package --pre-release
```

## 🔒 **Private Distribution**

### **GitHub Repository Setup**
```bash
# Initialize git (if not done by yo code)
git init
git add .
git commit -m "Initial extension scaffold"

# Create private repository on GitHub
# Then push:
git remote add origin https://github.com/acumen-desktop/fap-docs-manager.git
git branch -M main
git push -u origin main
```

### **Team Distribution**
```bash
# Option 1: Share VSIX file directly
# Send fap-docs-manager-0.1.0.vsix to team members
# They install with: code --install-extension fap-docs-manager-0.1.0.vsix

# Option 2: GitHub Releases
# Create release on GitHub with VSIX as asset
# Team downloads and installs

# Option 3: Internal package registry (advanced)
# Set up internal npm registry or Azure DevOps artifacts
```

## 🧪 **Testing Strategy**

### **Manual Testing Checklist**
- [ ] Extension activates when opening FAP monorepo
- [ ] Commands appear in Command Palette (Ctrl+Shift+P)
- [ ] Right-click context menus work on .md files
- [ ] Keyboard shortcuts work (Ctrl+Shift+D, Ctrl+Shift+L)
- [ ] Webview panel opens in editor area
- [ ] AI integration works (if implemented)

### **Automated Testing**
```bash
# Run unit tests
npm test

# Add tests in src/test/
# Test extension commands, file analysis, etc.
```

## 🔧 **Development Tips**

### **VSCode Extension Development**
- Use `console.log()` for debugging - output appears in "Extension Host" output channel
- Reload extension with `Ctrl+R` in Extension Development Host
- Check "Developer Tools" for webview debugging
- Use `vscode.window.showInformationMessage()` for user feedback

### **Webview Development**
```typescript
// Debug webview content
const panel = vscode.window.createWebviewPanel(
  'fapDocs',
  'FAP Docs Manager',
  vscode.ViewColumn.One,
  {
    enableScripts: true,
    retainContextWhenHidden: true,
    // Enable dev tools for webview
    enableCommandUris: true
  }
);

// Access webview dev tools:
// Right-click in webview → "Inspect Element"
```

### **Common Issues & Solutions**

**Extension not activating:**
- Check `activationEvents` in package.json
- Ensure workspace contains trigger files/folders

**Commands not appearing:**
- Verify `contributes.commands` in package.json
- Check `when` clauses in menus

**TypeScript compilation errors:**
- Run `npm run compile` to see detailed errors
- Check tsconfig.json configuration

**Webview not loading:**
- Check Content Security Policy
- Verify script enablement in webview options
- Use local file URIs for resources

## 📋 **Next Steps**

1. **Complete setup** following this guide
2. **Implement Phase 1** features from PROJECT_PLAN.md:
   - Basic webview panel
   - File location suggestions
   - Content analysis
3. **Test with real FAP documentation**
4. **Iterate based on team feedback**
5. **Add AI integration** (Phase 2)

## 🆘 **Getting Help**

### **VSCode Extension Documentation**
- [VSCode Extension API](https://code.visualstudio.com/api)
- [Extension Guides](https://code.visualstudio.com/api/extension-guides/overview)
- [Webview API](https://code.visualstudio.com/api/extension-guides/webview)

### **Debugging Resources**
- Extension Development Host logs
- VSCode Developer Tools (Help → Toggle Developer Tools)
- Extension output channel

### **Community Resources**
- [VSCode Extension Samples](https://github.com/microsoft/vscode-extension-samples)
- [Extension Generator](https://github.com/Microsoft/vscode-generator-code)

---

**Ready to start?** Run `yo code` in this directory and follow the prompts!
