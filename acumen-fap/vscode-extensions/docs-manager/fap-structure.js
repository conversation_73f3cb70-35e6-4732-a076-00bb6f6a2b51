// FAP Documentation Structure Logic
// Pure vanilla JavaScript - hardcoded FAP structure and analysis

const FAP_STRUCTURE = {
  'setup': {
    description: 'How do I get started?',
    patterns: [
      'install', 'installation', 'setup', 'getting started', 
      'prerequisites', 'requirements', 'environment', 'configure',
      'first time', 'onboarding', 'quickstart'
    ],
    examples: [
      'Installation guide',
      'Development environment setup',
      'First-time contributor guide',
      'Prerequisites and requirements'
    ]
  },

  'fap': {
    description: 'FAP platform documentation',
    patterns: [
      'component', 'fap-', 'semantic', 'vanilla', 'custom element',
      'platform', 'architecture', 'api', 'guide', 'tutorial',
      'chat', 'tooltip', 'core', 'theme', 'css organization'
    ],
    examples: [
      'fap-chat component documentation',
      'How to create FAP components',
      'FAP architecture overview',
      'Semantic elements guide'
    ]
  },

  'landmax': {
    description: 'LandMax project documentation',
    patterns: [
      'landmax', 'business', 'requirements', 'user guide',
      'workflow', 'process', 'client', 'project management'
    ],
    examples: [
      'LandMax user guide',
      'Business requirements document',
      'LandMax setup instructions',
      'Project workflows'
    ]
  },

  'corporate': {
    description: 'Business and corporate information',
    patterns: [
      'corporate', 'business', 'legal', 'compliance', 'policy',
      'process', 'procedure', 'finance', 'contract', 'entity',
      'acumen desktop', 'company', 'organization'
    ],
    examples: [
      'Corporate entity structure',
      'Development workflow process',
      'Legal compliance documentation',
      'Business policies'
    ]
  },

  'integrations': {
    description: 'Third-party integrations and external services',
    patterns: [
      'integration', 'third-party', 'external', 'api', 'service',
      'terminusdb', 'github', 'deployment', 'hosting', 'database',
      'packages', 'publishing', 'ci/cd'
    ],
    examples: [
      'TerminusDB integration guide',
      'GitHub Packages setup',
      'Deployment configuration',
      'Third-party API integration'
    ]
  },

  'development': {
    description: 'How to contribute and develop',
    patterns: [
      'contribute', 'development', 'convention', 'standard', 'workflow',
      'tool', 'testing', 'debugging', 'publishing', 'release',
      'coding standards', 'naming convention', 'pnpm'
    ],
    examples: [
      'Coding standards document',
      'How to publish packages',
      'Development tool setup',
      'Contributing guidelines'
    ]
  },

  'reference': {
    description: 'Quick reference and lookup information',
    patterns: [
      'reference', 'api', 'lookup', 'index', 'glossary',
      'troubleshooting', 'faq', 'cheat sheet', 'quick reference'
    ],
    examples: [
      'Complete API reference',
      'Component index',
      'Troubleshooting guide',
      'Glossary of terms'
    ]
  },

  'planning': {
    description: 'Project planning and coordination',
    patterns: [
      'planning', 'roadmap', 'session', 'notes', 'decision',
      'architecture decision', 'adr', 'timeline', 'milestone',
      'restructure', 'migration', 'strategy'
    ],
    examples: [
      'Development session notes',
      'Architecture decision record',
      'Project roadmap',
      'Migration planning'
    ]
  }
};

/**
 * Analyze content and suggest the best FAP structure location
 */
function analyzeFAPContent(content, filename) {
  const analysis = {
    suggestedPath: 'reference/',
    confidence: 0.1,
    reasoning: 'Default fallback location',
    alternativeLocations: [],
    detectedPatterns: []
  };

  // Normalize inputs
  const contentLower = content.toLowerCase();
  const filenameLower = filename.toLowerCase();
  const combinedText = `${filenameLower} ${contentLower}`;

  // Score each section
  const sectionScores = {};
  const patternMatches = {};

  for (const [section, config] of Object.entries(FAP_STRUCTURE)) {
    let score = 0;
    const matches = [];

    for (const pattern of config.patterns) {
      // Check filename (higher weight)
      const filenameMatches = (filenameLower.match(new RegExp(`\\b${pattern}\\b`, 'g')) || []).length;
      if (filenameMatches > 0) {
        score += filenameMatches * 3; // Filename matches are more important
        matches.push(`filename: "${pattern}"`);
      }

      // Check content
      const contentMatches = (contentLower.match(new RegExp(`\\b${pattern}\\b`, 'g')) || []).length;
      if (contentMatches > 0) {
        score += contentMatches;
        matches.push(`content: "${pattern}" (${contentMatches}x)`);
      }
    }

    sectionScores[section] = score;
    if (matches.length > 0) {
      patternMatches[section] = matches;
    }
  }

  // Find best match
  const maxScore = Math.max(...Object.values(sectionScores));
  const bestSection = Object.keys(sectionScores).find(key => sectionScores[key] === maxScore);

  if (maxScore > 0) {
    analysis.suggestedPath = `${bestSection}/`;
    analysis.confidence = Math.min(0.95, maxScore / 8); // Scale confidence
    analysis.detectedPatterns = patternMatches[bestSection] || [];
    
    // Create reasoning
    const patternCount = analysis.detectedPatterns.length;
    analysis.reasoning = `Found ${maxScore} pattern matches (${patternCount} unique patterns) for ${bestSection} section`;

    // Add alternative locations (sections with scores > 0, excluding best)
    analysis.alternativeLocations = Object.keys(sectionScores)
      .filter(key => key !== bestSection && sectionScores[key] > 0)
      .sort((a, b) => sectionScores[b] - sectionScores[a])
      .slice(0, 2)
      .map(key => `${key}/`);
  }

  // Special case handling
  analysis = applySpecialCases(analysis, filename, content);

  return analysis;
}

/**
 * Apply special case rules for better accuracy
 */
function applySpecialCases(analysis, filename, content) {
  const filenameLower = filename.toLowerCase();
  const contentLower = content.toLowerCase();

  // FAP component files
  if (filenameLower.startsWith('fap-') && filenameLower.endsWith('.md')) {
    analysis.suggestedPath = 'fap/components/';
    analysis.confidence = Math.max(analysis.confidence, 0.9);
    analysis.reasoning = 'Filename follows FAP component naming convention (fap-*.md)';
    return analysis;
  }

  // README files in component directories
  if (filenameLower === 'readme.md' && contentLower.includes('component')) {
    analysis.suggestedPath = 'fap/components/';
    analysis.confidence = Math.max(analysis.confidence, 0.8);
    analysis.reasoning = 'README file with component content';
    return analysis;
  }

  // Installation/setup files
  if (filenameLower.includes('install') || filenameLower.includes('setup')) {
    analysis.suggestedPath = 'setup/';
    analysis.confidence = Math.max(analysis.confidence, 0.85);
    analysis.reasoning = 'Filename indicates installation or setup documentation';
    return analysis;
  }

  // API documentation
  if (filenameLower.includes('api') && !filenameLower.includes('fap-')) {
    if (contentLower.includes('fap') || contentLower.includes('component')) {
      analysis.suggestedPath = 'fap/api/';
    } else {
      analysis.suggestedPath = 'reference/';
    }
    analysis.confidence = Math.max(analysis.confidence, 0.8);
    analysis.reasoning = 'API documentation detected';
    return analysis;
  }

  // TerminusDB specific
  if (contentLower.includes('terminusdb') || filenameLower.includes('terminus')) {
    analysis.suggestedPath = 'integrations/terminusdb/';
    analysis.confidence = Math.max(analysis.confidence, 0.9);
    analysis.reasoning = 'TerminusDB integration documentation';
    return analysis;
  }

  // Session notes
  if (filenameLower.includes('session') || filenameLower.includes('notes')) {
    analysis.suggestedPath = 'planning/session-notes/';
    analysis.confidence = Math.max(analysis.confidence, 0.8);
    analysis.reasoning = 'Session notes or planning documentation';
    return analysis;
  }

  return analysis;
}

/**
 * Get all valid FAP documentation paths
 */
function getAllFAPPaths() {
  return Object.keys(FAP_STRUCTURE).map(section => `${section}/`).sort();
}

/**
 * Validate if a path follows FAP structure conventions
 */
function validateFAPPath(filePath) {
  const normalizedPath = filePath.replace(/^docs(-intuitive)?\//, '');
  const pathParts = normalizedPath.split('/');
  
  if (pathParts.length === 0) {
    return {
      isValid: false,
      reasoning: 'Empty path'
    };
  }

  const section = pathParts[0];
  if (!FAP_STRUCTURE[section]) {
    const suggestion = Object.keys(FAP_STRUCTURE).find(key => 
      key.toLowerCase().includes(section.toLowerCase()) ||
      section.toLowerCase().includes(key.toLowerCase())
    );
    
    return {
      isValid: false,
      suggestion: suggestion ? `${suggestion}/` : 'reference/',
      reasoning: `Unknown section '${section}'. Did you mean '${suggestion}'?`
    };
  }

  return {
    isValid: true,
    reasoning: `Valid FAP structure path in '${section}' section`
  };
}

/**
 * Get section information
 */
function getSectionInfo(sectionName) {
  return FAP_STRUCTURE[sectionName] || null;
}

module.exports = {
  FAP_STRUCTURE,
  analyzeFAPContent,
  getAllFAPPaths,
  validateFAPPath,
  getSectionInfo
};
