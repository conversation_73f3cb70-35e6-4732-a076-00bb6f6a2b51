# Documentation Maintenance Tools

**Automated tools for maintaining the unified documentation system**

This directory contains tools to prevent documentation disorganization and maintain consistency across the FAP monorepo.

## 🛠️ Available Tools

### [validate-docs.js](validate-docs.js)
**Comprehensive documentation validation**

Validates the documentation structure for:
- ✅ Broken internal links
- ✅ Missing navigation entries  
- ✅ Inconsistent naming conventions
- ✅ Orphaned files
- ✅ Content freshness

```bash
# Run validation
node docs/tools/validate-docs.js

# Example output
🔍 Starting documentation validation...
📄 Found 45 files
🔗 Found 2 broken links
📝 Found 3 naming convention issues
❌ Errors: 2
⚠️ Warnings: 3
```

### [execute-migration.js](execute-migration.js)
**Migration execution tool**

Executes the migration plan to consolidate documentation into the unified structure:
- 📦 Creates backup of existing documentation
- 🚀 Migrates content using enhanced migration script
- 📊 Provides detailed migration report
- ✅ Handles duplicates and conflicts

```bash
# Execute migration
node docs/tools/execute-migration.js

# Creates backup and migrates all content
```

### [../scripts/migrate-docs.js](../scripts/migrate-docs.js)
**Enhanced migration analysis**

Updated migration script that:
- 🔍 Discovers and analyzes all markdown files
- 📋 Maps content to unified structure
- 🔄 Detects duplicates and conflicts
- 📊 Generates comprehensive analysis report

```bash
# Analyze migration requirements
node scripts/migrate-docs.js
```

## 🔄 Maintenance Workflows

### Daily Maintenance
```bash
# Validate documentation integrity
node docs/tools/validate-docs.js
```

### After Content Changes
```bash
# Re-validate after adding/modifying documentation
node docs/tools/validate-docs.js

# Check for new files that need migration
node scripts/migrate-docs.js
```

### Major Restructuring
```bash
# Create backup before major changes
node docs/tools/execute-migration.js

# Validate after restructuring
node docs/tools/validate-docs.js
```

## 📋 Naming Conventions

### Files
- **Standard docs**: `Title Case.md` (e.g., `Platform Overview.md`)
- **System files**: `UPPERCASE.md` (e.g., `README.md`, `CLAUDE.md`)
- **Templates**: `lowercase.md` (e.g., `component.md`)

### Directories
- **Content dirs**: `lowercase-with-hyphens/` (e.g., `getting-started/`)
- **Numbered dirs**: `00-category/` (e.g., `03-fap-platform/`)

### Links
- **Internal links**: Use relative paths from current file
- **Cross-references**: Link to related documentation
- **Navigation**: Update hubs when adding significant content

## 🚨 Error Types

### Critical Errors (Fix Immediately)
- **Broken links**: Internal links that don't resolve
- **Missing navigation**: Required navigation files missing
- **Structural issues**: Missing required directories

### Warnings (Address When Convenient)
- **Naming conventions**: Files/directories not following standards
- **Orphaned content**: Files not linked from navigation
- **Content freshness**: Outdated information

## 🔧 Future Enhancements

### Planned Tools
- **Link validator**: Real-time link checking on file save
- **CLAUDE.md sync**: Auto-update AI context files
- **Content freshness**: Track and alert on outdated content
- **Cross-reference generator**: Auto-generate related links

### Integration Opportunities
- **Git hooks**: Run validation on commit
- **CI/CD**: Automated validation in build pipeline
- **IDE integration**: Real-time validation in editor
- **Logseq plugins**: Enhanced Logseq integration

## 📊 Monitoring

### Key Metrics
- **Total documentation files**: Track growth
- **Broken links**: Maintain zero broken links
- **Naming compliance**: Improve consistency over time
- **Navigation coverage**: Ensure all content is discoverable

### Reports
- **Daily validation**: Automated health checks
- **Weekly summary**: Documentation system status
- **Migration progress**: Track consolidation efforts

## 🤝 Contributing

When adding new maintenance tools:

1. **Follow naming conventions** for tool files
2. **Add comprehensive documentation** in this README
3. **Include usage examples** and expected output
4. **Test thoroughly** with existing documentation
5. **Update workflows** to include new tools

---

**Questions?** Check the [Documentation Hub](../content/00-overview/Documentation%20Hub.md) or create an issue.
