#!/usr/bin/env node

/**
 * Documentation Migration Executor
 * 
 * Executes the migration plan to consolidate documentation
 * into the unified docs/ structure
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { DocumentationMigrator } from '../../scripts/migrate-docs.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '../..');

class MigrationExecutor {
  constructor() {
    this.migrator = new DocumentationMigrator();
    this.migrated = [];
    this.skipped = [];
    this.errors = [];
  }

  /**
   * Execute the complete migration
   */
  async execute() {
    console.log('🚀 Starting documentation migration execution...\n');
    
    try {
      // Discover and analyze files
      await this.migrator.discoverFiles();
      this.migrator.generateMigrationMap();
      
      // Execute migration
      await this.executeMigration();
      
      // Generate report
      this.generateReport();
      
      console.log('\n✅ Migration execution complete!');
      return true;
      
    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      return false;
    }
  }

  /**
   * Execute the migration plan
   */
  async executeMigration() {
    console.log('📁 Executing migration plan...');
    
    for (const [sourcePath, mapping] of this.migrator.migrationMap) {
      try {
        await this.migrateFile(mapping);
      } catch (error) {
        this.errors.push({
          source: sourcePath,
          target: mapping.target,
          error: error.message
        });
      }
    }
    
    console.log(`Processed ${this.migrator.migrationMap.size} files`);
  }

  /**
   * Migrate a single file
   */
  async migrateFile(mapping) {
    const { source, target, action } = mapping;
    
    switch (action) {
      case 'migrate':
        await this.copyFile(source, target);
        this.migrated.push({ source: source.relativePath, target });
        break;
        
      case 'skip-duplicate':
        this.skipped.push({ 
          source: source.relativePath, 
          reason: 'duplicate content' 
        });
        break;
        
      case 'skip-already-migrated':
        this.skipped.push({ 
          source: source.relativePath, 
          reason: 'already in target location' 
        });
        break;
        
      case 'archive':
        // For now, just skip archived files
        this.skipped.push({ 
          source: source.relativePath, 
          reason: 'archived content' 
        });
        break;
        
      default:
        this.skipped.push({ 
          source: source.relativePath, 
          reason: `unknown action: ${action}` 
        });
    }
  }

  /**
   * Copy file to target location
   */
  async copyFile(source, targetPath) {
    const sourcePath = source.path;
    const fullTargetPath = path.resolve(rootDir, targetPath);
    
    // Ensure target directory exists
    const targetDir = path.dirname(fullTargetPath);
    await fs.mkdir(targetDir, { recursive: true });
    
    // Check if target already exists
    try {
      await fs.access(fullTargetPath);
      console.log(`⚠️  Target exists, skipping: ${targetPath}`);
      return;
    } catch {
      // Target doesn't exist, proceed with copy
    }
    
    // Copy file content
    const content = await fs.readFile(sourcePath, 'utf-8');
    
    // Enhance content with metadata if needed
    const enhancedContent = this.enhanceContent(content, source);
    
    await fs.writeFile(fullTargetPath, enhancedContent);
    console.log(`✅ Migrated: ${source.relativePath} → ${targetPath}`);
  }

  /**
   * Enhance content with metadata and formatting
   */
  enhanceContent(content, source) {
    // For now, return content as-is
    // Future enhancements could add:
    // - Logseq block structure
    // - Metadata headers
    // - Cross-reference links
    return content;
  }

  /**
   * Generate migration report
   */
  generateReport() {
    console.log('\n📊 Migration Report');
    console.log('==================');
    console.log(`Files migrated: ${this.migrated.length}`);
    console.log(`Files skipped: ${this.skipped.length}`);
    console.log(`Errors: ${this.errors.length}`);
    
    if (this.migrated.length > 0) {
      console.log('\n✅ Successfully Migrated:');
      this.migrated.forEach(item => {
        console.log(`  ${item.source} → ${item.target}`);
      });
    }
    
    if (this.skipped.length > 0) {
      console.log('\n⏭️ Skipped Files:');
      this.skipped.forEach(item => {
        console.log(`  ${item.source} (${item.reason})`);
      });
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.errors.forEach(error => {
        console.log(`  ${error.source} → ${error.target}: ${error.error}`);
      });
    }
  }

  /**
   * Create backup of existing docs
   */
  async createBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.resolve(rootDir, `_backup/docs-${timestamp}`);
    
    console.log(`📦 Creating backup at ${backupDir}...`);
    
    try {
      await fs.mkdir(backupDir, { recursive: true });
      
      // Copy existing docs directory
      const docsDir = path.resolve(rootDir, 'docs');
      try {
        await fs.access(docsDir);
        await this.copyDirectory(docsDir, path.join(backupDir, 'docs'));
      } catch {
        // docs directory doesn't exist yet
      }
      
      // Copy docs-new directory
      const docsNewDir = path.resolve(rootDir, 'docs-new');
      try {
        await fs.access(docsNewDir);
        await this.copyDirectory(docsNewDir, path.join(backupDir, 'docs-new'));
      } catch {
        // docs-new directory doesn't exist
      }
      
      console.log('✅ Backup created successfully');
      
    } catch (error) {
      console.warn('⚠️ Backup creation failed:', error.message);
    }
  }

  /**
   * Recursively copy directory
   */
  async copyDirectory(src, dest) {
    await fs.mkdir(dest, { recursive: true });
    const entries = await fs.readdir(src, { withFileTypes: true });
    
    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);
      
      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const executor = new MigrationExecutor();
  
  // Create backup first
  await executor.createBackup();
  
  // Execute migration
  executor.execute()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Migration execution failed:', error.message);
      process.exit(1);
    });
}

export { MigrationExecutor };
