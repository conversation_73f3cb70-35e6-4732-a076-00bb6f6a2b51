#!/usr/bin/env node

/**
 * Documentation Validation Tool
 * 
 * Validates the unified documentation structure for:
 * - Broken internal links
 * - Missing navigation entries
 * - Inconsistent naming conventions
 * - Orphaned files
 * - Content freshness
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const docsDir = path.resolve(__dirname, '..');

class DocumentationValidator {
  constructor() {
    this.files = [];
    this.links = [];
    this.errors = [];
    this.warnings = [];
    this.stats = {
      totalFiles: 0,
      brokenLinks: 0,
      orphanedFiles: 0,
      namingIssues: 0
    };
  }

  /**
   * Run complete validation suite
   */
  async validate() {
    console.log('🔍 Starting documentation validation...\n');
    
    await this.discoverFiles();
    await this.validateLinks();
    await this.validateNaming();
    await this.validateNavigation();
    await this.validateStructure();
    
    this.generateReport();
    return this.errors.length === 0;
  }

  /**
   * Discover all markdown files in docs
   */
  async discoverFiles() {
    console.log('📄 Discovering documentation files...');
    await this.scanDirectory(docsDir);
    this.stats.totalFiles = this.files.length;
    console.log(`Found ${this.files.length} files\n`);
  }

  /**
   * Recursively scan directory for markdown files
   */
  async scanDirectory(dir) {
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        const relativePath = path.relative(docsDir, fullPath);
        
        // Skip hidden directories and node_modules
        if (entry.name.startsWith('.') || entry.name === 'node_modules') {
          continue;
        }
        
        if (entry.isDirectory()) {
          await this.scanDirectory(fullPath);
        } else if (entry.name.endsWith('.md')) {
          await this.analyzeFile(fullPath, relativePath);
        }
      }
    } catch (error) {
      this.errors.push({
        type: 'scan_error',
        path: dir,
        message: error.message
      });
    }
  }

  /**
   * Analyze a markdown file
   */
  async analyzeFile(filePath, relativePath) {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const stats = await fs.stat(filePath);
      
      const fileInfo = {
        path: filePath,
        relativePath: relativePath,
        content: content,
        size: stats.size,
        modified: stats.mtime,
        title: this.extractTitle(content),
        links: this.extractLinks(content, relativePath)
      };
      
      this.files.push(fileInfo);
      this.links.push(...fileInfo.links);
      
    } catch (error) {
      this.errors.push({
        type: 'file_analysis_error',
        path: filePath,
        message: error.message
      });
    }
  }

  /**
   * Extract title from markdown content
   */
  extractTitle(content) {
    const h1Match = content.match(/^#\s+(.+)$/m);
    return h1Match ? h1Match[1].trim() : 'Untitled';
  }

  /**
   * Extract links from markdown content
   */
  extractLinks(content, sourceFile) {
    const links = [];
    
    // Extract markdown links [text](url)
    const markdownLinks = content.match(/\[([^\]]+)\]\(([^)]+)\)/g) || [];
    markdownLinks.forEach(link => {
      const match = link.match(/\[([^\]]+)\]\(([^)]+)\)/);
      if (match && !match[2].startsWith('http')) {
        links.push({
          type: 'markdown',
          text: match[1],
          url: match[2],
          sourceFile: sourceFile
        });
      }
    });
    
    return links;
  }

  /**
   * Validate all internal links
   */
  async validateLinks() {
    console.log('🔗 Validating internal links...');
    let brokenCount = 0;
    
    for (const link of this.links) {
      if (await this.isLinkBroken(link)) {
        brokenCount++;
        this.errors.push({
          type: 'broken_link',
          sourceFile: link.sourceFile,
          linkText: link.text,
          linkUrl: link.url,
          message: `Broken link: "${link.text}" -> "${link.url}"`
        });
      }
    }
    
    this.stats.brokenLinks = brokenCount;
    console.log(`Found ${brokenCount} broken links\n`);
  }

  /**
   * Check if a link is broken
   */
  async isLinkBroken(link) {
    try {
      // Resolve relative path from source file
      const sourceDir = path.dirname(path.join(docsDir, link.sourceFile));
      const targetPath = path.resolve(sourceDir, link.url);
      
      // Check if target exists
      await fs.access(targetPath);
      return false;
    } catch {
      return true;
    }
  }

  /**
   * Validate naming conventions
   */
  async validateNaming() {
    console.log('📝 Validating naming conventions...');
    let namingIssues = 0;
    
    for (const file of this.files) {
      const fileName = path.basename(file.relativePath);
      const dirName = path.dirname(file.relativePath);
      
      // Check file naming (should be Title Case.md or UPPERCASE.md)
      if (!this.isValidFileName(fileName)) {
        namingIssues++;
        this.warnings.push({
          type: 'naming_convention',
          path: file.relativePath,
          message: `File name should be "Title Case.md" or "UPPERCASE.md": ${fileName}`
        });
      }
      
      // Check directory naming (should be lowercase-with-hyphens)
      if (!this.isValidDirectoryName(dirName)) {
        namingIssues++;
        this.warnings.push({
          type: 'naming_convention',
          path: file.relativePath,
          message: `Directory should use lowercase-with-hyphens: ${dirName}`
        });
      }
    }
    
    this.stats.namingIssues = namingIssues;
    console.log(`Found ${namingIssues} naming convention issues\n`);
  }

  /**
   * Check if file name follows conventions
   */
  isValidFileName(fileName) {
    // Allow UPPERCASE.md (like README.md, CLAUDE.md)
    if (/^[A-Z][A-Z_-]*\.md$/.test(fileName)) return true;
    
    // Allow Title Case.md
    if (/^[A-Z][a-zA-Z0-9\s]*\.md$/.test(fileName)) return true;
    
    return false;
  }

  /**
   * Check if directory name follows conventions
   */
  isValidDirectoryName(dirName) {
    if (dirName === '.' || dirName === '') return true;
    
    // Should be lowercase with hyphens, numbers allowed
    const parts = dirName.split('/');
    return parts.every(part => /^[a-z0-9-]+$/.test(part) || part === '.');
  }

  /**
   * Validate navigation structure
   */
  async validateNavigation() {
    console.log('🧭 Validating navigation structure...');
    
    // Check for required navigation files
    const requiredNavFiles = [
      'README.md',
      'content/00-overview/Documentation Hub.md',
      'ai-context/CLAUDE.md'
    ];
    
    for (const requiredFile of requiredNavFiles) {
      const found = this.files.some(f => f.relativePath === requiredFile);
      if (!found) {
        this.errors.push({
          type: 'missing_navigation',
          path: requiredFile,
          message: `Required navigation file missing: ${requiredFile}`
        });
      }
    }
    
    console.log('Navigation structure validated\n');
  }

  /**
   * Validate overall structure
   */
  async validateStructure() {
    console.log('🏗️ Validating documentation structure...');
    
    // Check for required directories
    const requiredDirs = [
      'content',
      'ai-context',
      'tools'
    ];
    
    for (const dir of requiredDirs) {
      const hasFiles = this.files.some(f => f.relativePath.startsWith(dir + '/'));
      if (!hasFiles) {
        this.warnings.push({
          type: 'missing_directory',
          path: dir,
          message: `Required directory appears empty: ${dir}/`
        });
      }
    }
    
    console.log('Structure validation complete\n');
  }

  /**
   * Generate validation report
   */
  generateReport() {
    console.log('📊 Validation Report');
    console.log('===================');
    console.log(`Total files: ${this.stats.totalFiles}`);
    console.log(`Broken links: ${this.stats.brokenLinks}`);
    console.log(`Naming issues: ${this.stats.namingIssues}`);
    console.log(`Errors: ${this.errors.length}`);
    console.log(`Warnings: ${this.warnings.length}`);
    
    if (this.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.errors.forEach(error => {
        console.log(`  ${error.type}: ${error.message}`);
      });
    }
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️ Warnings:');
      this.warnings.forEach(warning => {
        console.log(`  ${warning.type}: ${warning.message}`);
      });
    }
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('\n✅ All validation checks passed!');
    }
  }
}

// Run validation if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const validator = new DocumentationValidator();
  validator.validate()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Validation failed:', error.message);
      process.exit(1);
    });
}

export { DocumentationValidator };
