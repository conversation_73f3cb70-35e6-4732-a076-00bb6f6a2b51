{:meta/version 1
 
 ;; Preferred workflow for development documentation
 :preferred-workflow :now
 
 ;; Graph settings optimized for monorepo knowledge management
 :graph/settings
 {:journal? true
  :project? true
  :workflow :now}
 
 ;; Enable features for better documentation experience
 :feature/enable-block-timestamps? true
 :feature/enable-search-remove-accents? true
 :feature/enable-journals? true
 :feature/enable-whiteboards? true
 
 ;; Custom CSS for FAP branding
 :custom-css-url ".logseq/custom.css"
 
 ;; Default templates for consistent documentation
 :default-templates
 {:journals ".logseq/templates/journal.md"
  :pages ".logseq/templates/page.md"}
 
 ;; Preferred format
 :preferred-format :markdown
 
 ;; Plugin configuration
 :plugins []
 
 ;; Publishing settings
 :publishing/all-pages-public? false
 
 ;; Search settings
 :search/default-limit 50
 
 ;; Editor settings
 :editor/command-trigger "/"
 :editor/logical-outdenting? true
 
 ;; UI settings
 :ui/enable-tooltip? true
 :ui/show-brackets? false}
