- # {{component-name}} #fap/components #status/{{status}}
  template:: component
  created:: {{today}}
  type:: component
  - ## Overview
    - Brief description of what this component does and its purpose in the FAP ecosystem
    - Key features and capabilities
  - ## Dependencies
    - [[FAP Core]] - Base theme system and utilities
    - Add other component dependencies as page links
  - ## Used By
    - List applications, components, or systems that use this component
    - Link to specific implementations: [[Application Name]]
  - ## API Reference
    - ### Key Methods
      - `method()` - Description of what this method does
    - ### Properties
      - `property` - Description of this property
    - ### Events
      - `event-name` - When this event is triggered
  - ## File Locations
    - **Source**: `{{source-path}}`
    - **Documentation**: `{{docs-path}}`
    - **Examples**: `{{examples-path}}`
  - ## Related Documentation
    - [[{{component-name}} Requirements]] - Functional requirements
    - [[{{component-name}} Design]] - Technical design document
    - [[{{component-name}} Implementation]] - Implementation details
  - ## Status Information
    - **Current Status**: {{status}}
    - **Version**: {{version}}
    - **Last Updated**: {{today}}
    - **Maintainer**: {{maintainer}}
  - ## Examples
    - ### Basic Usage
      ```html
      <!-- Add basic usage example here -->
      ```
    - ### Advanced Usage
      ```javascript
      // Add advanced usage example here
      ```
