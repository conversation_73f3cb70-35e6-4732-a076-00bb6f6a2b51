# Documentation Hub

**Central navigation for the FAP Monorepo Knowledge Base**

Welcome to the comprehensive documentation system for the Freedom Application Platform (FAP) monorepo, Acumen Desktop Software Canada Inc., and related business operations.

## 🚀 Quick Navigation

### For Developers
- **Start Here**: [Installation Guide](../01-getting-started/Installation.md)
- **Platform Docs**: [FAP Platform Overview](../03-fap-platform/Platform%20Overview.md)
- **Technical Reference**: [Technical Map](../05-technical/Technical%20Map.md)
- **Component Docs**: [Component Index](../03-fap-platform/components/Component%20Index.md)

### For Business Users
- **Corporate Info**: [Corporate Map](../02-corporate/Corporate%20Map.md)
- **LandMax Project**: [LandMax Overview](../04-landmax/Business%20Overview.md)
- **Business Processes**: [Process Documentation](../02-corporate/processes/)
- **Legal & Compliance**: [Legal Information](../02-corporate/Legal%20and%20Compliance.md)

### For AI Agents
- **AI Context**: [CLAUDE.md](../../ai-context/CLAUDE.md)
- **Component Relationships**: [Component Index](../../ai-context/component-index.md)
- **API Reference**: [API Documentation](../../ai-context/api-reference.md)
- **Structured Metadata**: All pages include machine-readable metadata

## 📁 Documentation Categories

### [00-overview/](../00-overview/) - Project Overview
Central navigation and high-level project information

### [01-getting-started/](../01-getting-started/) - Getting Started
Installation, setup, and onboarding guides for new contributors

### [02-corporate/](../02-corporate/) - Corporate Documentation
Acumen Desktop Software Canada Inc. business information and processes

### [03-fap-platform/](../03-fap-platform/) - FAP Platform
Freedom Application Platform technical documentation and components

### [04-landmax/](../04-landmax/) - LandMax Business
LandMax project documentation and business requirements

### [05-technical/](../05-technical/) - Technical Documentation
Development guides, architecture, and technical conventions

### [06-integrations/](../06-integrations/) - Third-Party Integrations
External service integrations, APIs, and configurations

### [07-open-source/](../07-open-source/) - Open Source
Community guidelines, contribution processes, and open source projects

### [08-project-mgmt/](../08-project-mgmt/) - Project Management
Planning documents, session notes, and project coordination

## 🔍 Finding Information

### Search Strategies
1. **Browse by category** using the navigation above
2. **Use specific navigation hubs** for guided exploration:
   - [Technical Map](../05-technical/Technical%20Map.md) for technical topics
   - [Corporate Map](../02-corporate/Corporate%20Map.md) for business topics
   - [Component Index](../03-fap-platform/components/Component%20Index.md) for FAP components
3. **Search by tags** using the tag system
4. **Follow cross-references** between related documents

### Documentation Features
- **Bi-directional linking** between related concepts
- **Semantic tagging** for discoverability
- **Template-based consistency** for predictable structure
- **Multi-modal access** for humans and AI systems

## 🛠️ Using This System

### For Regular Users
This documentation works as standard markdown files with clear navigation and cross-references.

### For Logseq Users
Enhanced experience with:
- Block-based structure for granular linking
- Graph view for exploring relationships
- Custom templates for consistent formatting
- See [Logseq Setup Guide](../01-getting-started/Logseq%20Setup.md)

### For AI Systems
Structured context and metadata for efficient information retrieval:
- Hierarchical CLAUDE.md files for path-specific context
- Machine-readable component relationships
- Consistent tagging and categorization

## 📊 System Status

- **Documentation Files**: 65+ markdown files
- **Migration Status**: [View Progress](../../MIGRATION_STATUS.md)
- **Last Updated**: 2025-01-24
- **Maintenance**: Automated validation and link checking

## 🤝 Contributing

1. **Follow conventions**: See [Documentation Standards](../05-technical/conventions/Documentation%20Standards.md)
2. **Use templates**: Available in [.logseq/templates/](../../.logseq/templates/)
3. **Add cross-references**: Link to related documentation
4. **Update navigation**: Ensure new content is discoverable

---

**Questions?** Check the specific navigation hubs above or refer to the [main documentation index](../../README.md).
