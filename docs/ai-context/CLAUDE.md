# AI Context - Documentation System

**Structured context for AI assistants working with the FAP monorepo documentation**

## Documentation System Overview

This is the unified documentation system for the FAP monorepo, consolidating all markdown content into a single, well-organized structure under `docs/`.

### Key Principles
- **Single Source of Truth**: All documentation lives in `docs/`
- **Multi-Modal Access**: Supports human browsing, Logseq enhancement, and AI consumption
- **Automated Maintenance**: Tools prevent disorganization and maintain consistency
- **Scalable Structure**: Handles multiple teams and projects

## Directory Structure

```
docs/
├── README.md                    # Master documentation index
├── content/                     # Main documentation content
│   ├── 00-overview/            # Project overview & navigation hubs
│   ├── 01-getting-started/     # Installation & setup guides
│   ├── 02-corporate/           # Business documentation
│   ├── 03-fap-platform/        # FAP technical documentation
│   ├── 04-landmax/             # LandMax business documentation
│   ├── 05-technical/           # Technical guides & conventions
│   ├── 06-integrations/        # Third-party integrations
│   ├── 07-open-source/         # Community & open source
│   └── 08-project-mgmt/        # Planning & coordination
├── ai-context/                  # AI-specific documentation
│   ├── CLAUDE.md               # This file - AI context
│   ├── component-index.md      # Component relationships
│   └── api-reference.md        # API documentation
├── .logseq/                    # Optional Logseq enhancement
│   ├── config.edn              # Logseq configuration
│   ├── custom.css              # FAP-themed styling
│   └── templates/              # Documentation templates
└── tools/                      # Documentation maintenance tools
    ├── migrate.js              # Content migration
    ├── validate.js             # Link validation
    └── sync-claude.js          # CLAUDE.md synchronization
```

## Navigation Patterns

### Primary Entry Points
1. **[Documentation Hub](../content/00-overview/Documentation%20Hub.md)** - Master navigation
2. **[Technical Map](../content/05-technical/Technical%20Map.md)** - Technical overview
3. **[Corporate Map](../content/02-corporate/Corporate%20Map.md)** - Business structure
4. **[Component Index](../content/03-fap-platform/components/Component%20Index.md)** - FAP components

### Content Discovery
- **By Category**: Use numbered directory structure (00-08)
- **By Relationship**: Follow cross-references and links
- **By Tag**: Search using semantic tags (#category/subcategory)
- **By Component**: Use component relationship mapping

## AI Assistant Guidelines

### When Working with Documentation
1. **Check existing structure** before creating new files
2. **Follow naming conventions**: `Title Case.md` for files, `lowercase-with-hyphens/` for directories
3. **Use appropriate category** based on content type
4. **Add cross-references** to related documentation
5. **Update navigation hubs** when adding significant content

### File Naming Conventions
- **Files**: `Title Case.md` (e.g., `Platform Overview.md`)
- **Directories**: `lowercase-with-hyphens/` (e.g., `getting-started/`)
- **Special files**: `UPPERCASE.md` for system files (e.g., `README.md`, `CLAUDE.md`)

### Content Organization
- **00-overview/**: Navigation hubs and project overview
- **01-getting-started/**: Onboarding and setup
- **02-corporate/**: Business and legal information
- **03-fap-platform/**: FAP technical documentation
- **04-landmax/**: LandMax project documentation
- **05-technical/**: Development and architecture
- **06-integrations/**: Third-party services and APIs
- **07-open-source/**: Community and contribution
- **08-project-mgmt/**: Planning and coordination

### Maintenance Tasks
- **Link validation**: Ensure all internal links work
- **Content freshness**: Update outdated information
- **Cross-references**: Add links between related content
- **Navigation updates**: Keep hubs and indexes current

## Integration Points

### With Existing Systems
- **CLAUDE.md hierarchy**: Maintains path-specific context
- **Logseq enhancement**: Optional block-based structure
- **Migration tools**: Automated content organization
- **Validation tools**: Link checking and consistency

### With Development Workflow
- **Component documentation**: Linked to package development
- **API documentation**: Generated from code comments
- **Architecture docs**: Updated with system changes
- **Process documentation**: Reflects actual workflows

## Status and Migration

### Current State
- **Total Files**: 65+ markdown files across monorepo
- **Migration Progress**: Consolidating to unified structure
- **Automation**: Tools for maintenance and validation
- **Enhancement**: Optional Logseq integration for power users

### Next Steps
1. Complete migration of remaining content
2. Implement automated validation
3. Establish maintenance procedures
4. Create component relationship mapping

---

**For AI assistants**: This documentation system is designed to be both human-friendly and AI-accessible. Use the structured navigation and cross-references to understand relationships between concepts and maintain consistency when making updates.
