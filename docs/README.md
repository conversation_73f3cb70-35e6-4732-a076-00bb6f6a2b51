# FAP Monorepo Documentation

**Single Source of Truth for all Freedom Application Platform documentation**

Welcome to the comprehensive documentation for the FAP monorepo, Acumen Desktop Software Canada Inc., and related business operations.

## 🚀 Quick Start

- **New to the project?** → [Getting Started Guide](content/01-getting-started/Installation.md)
- **Looking for FAP docs?** → [FAP Platform Overview](content/03-fap-platform/Platform%20Overview.md)
- **Need technical info?** → [Technical Documentation](content/05-technical/)
- **Business documentation?** → [Corporate Information](content/02-corporate/)

## 📚 Documentation Structure

### 🗺️ Navigation Hubs
- [📖 Documentation Hub](content/00-overview/Documentation%20Hub.md) - Master navigation
- [🏢 Corporate Map](content/02-corporate/Corporate%20Map.md) - Business structure
- [⚙️ Technical Map](content/05-technical/Technical%20Map.md) - Technical overview
- [🔧 Component Index](content/03-fap-platform/components/Component%20Index.md) - FAP components

### 📁 Content Categories

#### [00-overview/](content/00-overview/) - Project Overview
- Project documentation hub and navigation
- High-level architecture and vision
- Getting oriented in the monorepo

#### [01-getting-started/](content/01-getting-started/) - Getting Started
- Installation and setup guides
- Development environment configuration
- First steps for new contributors

#### [02-corporate/](content/02-corporate/) - Corporate Documentation
- Acumen Desktop Software Canada Inc. information
- Legal structure and compliance
- Business processes and policies

#### [03-fap-platform/](content/03-fap-platform/) - FAP Platform
- Freedom Application Platform documentation
- Component documentation and APIs
- Architecture and design principles

#### [04-landmax/](content/04-landmax/) - LandMax Business
- LandMax project documentation
- Business requirements and processes
- User guides and setup instructions

#### [05-technical/](content/05-technical/) - Technical Documentation
- Development guides and conventions
- Architecture documentation
- Build and deployment processes

#### [06-integrations/](content/06-integrations/) - Third-Party Integrations
- TerminusDB integration and setup
- External service documentation
- API integrations and configurations

#### [07-open-source/](content/07-open-source/) - Open Source
- Community guidelines and contribution
- Open source project documentation
- Licensing and legal information

#### [08-project-mgmt/](content/08-project-mgmt/) - Project Management
- Planning documents and roadmaps
- Session notes and instructions
- Project coordination and tracking

## 🔍 Finding Information

### Search Strategies
1. **Browse by category** using the structure above
2. **Use navigation hubs** for guided exploration
3. **Search by tags** (see [Tag Index](ai-context/tag-index.md))
4. **Check component relationships** in [Component Index](content/03-fap-platform/components/Component%20Index.md)

### For AI Systems
- [AI Context Documentation](ai-context/CLAUDE.md) - Structured context for AI assistants
- [Component Index](ai-context/component-index.md) - Machine-readable component relationships
- [API Reference](ai-context/api-reference.md) - Structured API documentation

## 🛠️ Documentation Tools

### For Contributors
- [Documentation Guidelines](content/05-technical/conventions/Documentation%20Standards.md)
- [Template System](.logseq/templates/) - Consistent documentation templates
- [Migration Tools](tools/) - Scripts for maintaining documentation

### For Logseq Users
This documentation is enhanced for [Logseq](https://logseq.com/) users:
- Block-based structure with bi-directional linking
- Custom templates for consistent formatting
- Graph view for exploring relationships
- See [Logseq Setup Guide](content/01-getting-started/Logseq%20Setup.md)

## 📊 Documentation Status

- **Total Files**: 65+ markdown files
- **Migration Status**: See [Migration Status](MIGRATION_STATUS.md)
- **Last Updated**: 2025-01-24
- **Maintenance**: Automated validation and updates

## 🤝 Contributing

1. **Follow naming conventions**: `Title Case.md` for files, `lowercase-with-hyphens/` for directories
2. **Use templates**: Available in [.logseq/templates/](.logseq/templates/)
3. **Add cross-references**: Link to related documentation
4. **Update indexes**: Ensure new content is discoverable

## 🔧 Maintenance

This documentation system includes automated maintenance:
- Link validation on commits
- Auto-generated navigation indexes
- Duplicate detection and alerts
- Content freshness tracking

For maintenance procedures, see [Documentation Maintenance](tools/README.md).

---

**Need help?** Check the [Documentation Hub](content/00-overview/Documentation%20Hub.md) or create an issue.
