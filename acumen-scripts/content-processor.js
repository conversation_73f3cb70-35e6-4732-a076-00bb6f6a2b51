#!/usr/bin/env node

/**
 * Content Processing Pipeline
 * 
 * Processes markdown content for Logseq optimization:
 * - Converts to block-based structure
 * - Adds structured frontmatter
 * - Updates links and references
 * - Applies consistent formatting
 */

import fs from 'fs/promises';
import path from 'path';

class ContentProcessor {
  constructor() {
    this.linkMap = new Map(); // Track link updates
    this.tagMap = new Map();  // Track tag standardization
  }

  /**
   * Process a markdown file for Logseq compatibility
   */
  async processFile(fileInfo, targetPath) {
    try {
      let content = fileInfo.content;
      
      // Extract and process frontmatter
      const frontmatter = this.createLogseqFrontmatter(fileInfo);
      
      // Convert content to Logseq block structure
      const blockContent = this.convertToBlocks(content, fileInfo);
      
      // Update links to new structure
      const updatedContent = this.updateLinks(blockContent, fileInfo);
      
      // Combine frontmatter and content
      const finalContent = this.combineContent(frontmatter, updatedContent);
      
      return {
        content: finalContent,
        targetPath: targetPath,
        sourceInfo: fileInfo
      };
      
    } catch (error) {
      throw new Error(`Failed to process ${fileInfo.relativePath}: ${error.message}`);
    }
  }

  /**
   * Create Logseq-compatible frontmatter
   */
  createLogseqFrontmatter(fileInfo) {
    const category = this.mapCategoryToTag(fileInfo.category);
    const status = this.determineStatus(fileInfo);
    const audience = this.determineAudience(fileInfo);
    
    const frontmatter = {
      title: fileInfo.title,
      type: this.mapContentType(fileInfo.category),
      category: category,
      tags: this.standardizeTags(fileInfo.tags, category),
      status: status,
      audience: audience,
      created: new Date().toISOString().split('T')[0],
      updated: new Date().toISOString().split('T')[0],
      source: fileInfo.relativePath
    };

    // Add relationships if we can detect them
    const related = this.detectRelatedContent(fileInfo);
    if (related.length > 0) {
      frontmatter.related = related;
    }

    const dependencies = this.detectDependencies(fileInfo);
    if (dependencies.length > 0) {
      frontmatter.dependencies = dependencies;
    }

    return frontmatter;
  }

  /**
   * Convert markdown content to Logseq block structure
   */
  convertToBlocks(content, fileInfo) {
    // Remove existing frontmatter
    content = content.replace(/^---\n[\s\S]*?\n---\n/, '');
    
    // Split content into sections based on headers
    const sections = this.splitIntoSections(content);
    
    // Convert each section to block format
    const blocks = sections.map(section => this.convertSectionToBlock(section));
    
    // Create main title block
    const titleBlock = `- # ${fileInfo.title} ${this.formatTags(fileInfo)}`;
    
    // Combine all blocks
    return [titleBlock, ...blocks].join('\n');
  }

  /**
   * Split content into logical sections
   */
  splitIntoSections(content) {
    const sections = [];
    const lines = content.split('\n');
    let currentSection = [];
    let currentLevel = 0;

    for (const line of lines) {
      const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);
      
      if (headerMatch) {
        // Save previous section if it exists
        if (currentSection.length > 0) {
          sections.push({
            level: currentLevel,
            content: currentSection.join('\n').trim()
          });
        }
        
        // Start new section
        currentLevel = headerMatch[1].length;
        currentSection = [line];
      } else {
        currentSection.push(line);
      }
    }
    
    // Add final section
    if (currentSection.length > 0) {
      sections.push({
        level: currentLevel,
        content: currentSection.join('\n').trim()
      });
    }
    
    return sections;
  }

  /**
   * Convert a section to Logseq block format
   */
  convertSectionToBlock(section) {
    if (!section.content.trim()) return '';
    
    const lines = section.content.split('\n');
    const blocks = [];
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      
      // Handle headers
      const headerMatch = line.match(/^(#{1,6})\s+(.+)$/);
      if (headerMatch) {
        const level = headerMatch[1].length;
        const title = headerMatch[2];
        const indent = '  '.repeat(level - 1);
        blocks.push(`${indent}- ## ${title}`);
        continue;
      }
      
      // Handle list items
      if (line.match(/^\s*[-*+]\s+/)) {
        const indent = this.calculateIndent(line);
        const content = line.replace(/^\s*[-*+]\s+/, '');
        blocks.push(`${indent}- ${content}`);
        continue;
      }
      
      // Handle numbered lists
      if (line.match(/^\s*\d+\.\s+/)) {
        const indent = this.calculateIndent(line);
        const content = line.replace(/^\s*\d+\.\s+/, '');
        blocks.push(`${indent}- ${content}`);
        continue;
      }
      
      // Handle code blocks
      if (line.trim() === '```' || line.match(/^```\w+/)) {
        const codeBlock = this.extractCodeBlock(lines, i);
        blocks.push(`  - \`\`\`${codeBlock.language}\n${codeBlock.content}\n    \`\`\``);
        i = codeBlock.endIndex;
        continue;
      }
      
      // Handle regular paragraphs
      if (line.trim()) {
        // Group consecutive non-empty lines into paragraphs
        const paragraph = this.extractParagraph(lines, i);
        if (paragraph.content.trim()) {
          blocks.push(`  - ${paragraph.content}`);
        }
        i = paragraph.endIndex;
      }
    }
    
    return blocks.join('\n');
  }

  /**
   * Calculate proper indentation for nested items
   */
  calculateIndent(line) {
    const leadingSpaces = line.match(/^\s*/)[0].length;
    const indentLevel = Math.floor(leadingSpaces / 2) + 1;
    return '  '.repeat(indentLevel);
  }

  /**
   * Extract code block content
   */
  extractCodeBlock(lines, startIndex) {
    const startLine = lines[startIndex];
    const language = startLine.replace(/^```/, '') || '';
    const content = [];
    let endIndex = startIndex + 1;
    
    while (endIndex < lines.length && lines[endIndex].trim() !== '```') {
      content.push(`    ${lines[endIndex]}`);
      endIndex++;
    }
    
    return {
      language,
      content: content.join('\n'),
      endIndex
    };
  }

  /**
   * Extract paragraph content
   */
  extractParagraph(lines, startIndex) {
    const content = [];
    let endIndex = startIndex;
    
    while (endIndex < lines.length && lines[endIndex].trim()) {
      // Skip if this line starts a new structure
      if (lines[endIndex].match(/^#{1,6}\s+/) || 
          lines[endIndex].match(/^\s*[-*+]\s+/) ||
          lines[endIndex].match(/^\s*\d+\.\s+/) ||
          lines[endIndex].match(/^```/)) {
        break;
      }
      
      content.push(lines[endIndex]);
      endIndex++;
    }
    
    return {
      content: content.join(' ').trim(),
      endIndex: endIndex - 1
    };
  }

  /**
   * Update links to point to new structure
   */
  updateLinks(content, fileInfo) {
    // Update markdown links
    content = content.replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, text, url) => {
      const updatedUrl = this.mapLinkUrl(url, fileInfo);
      return `[${text}](${updatedUrl})`;
    });
    
    // Convert some markdown links to Logseq page references
    content = content.replace(/\[([^\]]+)\]\(([^)]+\.md)\)/g, (match, text, url) => {
      const pageName = this.urlToPageName(url);
      return `[[${pageName}]]`;
    });
    
    // Update existing wiki-style links
    content = content.replace(/\[\[([^\]]+)\]\]/g, (match, pageName) => {
      const updatedPageName = this.mapPageName(pageName);
      return `[[${updatedPageName}]]`;
    });
    
    return content;
  }

  /**
   * Map old URLs to new structure
   */
  mapLinkUrl(url, fileInfo) {
    // Handle relative links
    if (url.startsWith('./') || url.startsWith('../')) {
      // Try to resolve relative path
      const basePath = path.dirname(fileInfo.relativePath);
      const resolvedPath = path.resolve(basePath, url);
      return this.mapAbsolutePath(resolvedPath);
    }
    
    // Handle absolute paths within the repo
    if (url.startsWith('/') || !url.includes('://')) {
      return this.mapAbsolutePath(url);
    }
    
    // External URLs remain unchanged
    return url;
  }

  /**
   * Map absolute paths to new structure
   */
  mapAbsolutePath(originalPath) {
    // Remove leading slash and normalize
    const cleanPath = originalPath.replace(/^\/+/, '');
    
    // Map common paths
    const pathMappings = {
      'README.md': 'Project Overview',
      'docs/technical/pnpm-workspaces-for-dummies.md': 'pnpm Workspaces Guide',
      'docs/technical/architecture/data-system.md': 'Data System',
      'fap/README.md': 'Platform Overview',
      'landmax/README.md': 'LandMax Business'
    };
    
    if (pathMappings[cleanPath]) {
      return pathMappings[cleanPath];
    }
    
    // Default mapping based on path structure
    if (cleanPath.includes('fap/packages/')) {
      const packageName = cleanPath.match(/fap\/packages\/([^\/]+)/)?.[1];
      return packageName ? `FAP ${packageName}` : cleanPath;
    }
    
    return cleanPath;
  }

  /**
   * Convert URL to Logseq page name
   */
  urlToPageName(url) {
    const fileName = path.basename(url, '.md');
    return fileName.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

  /**
   * Map page names to new structure
   */
  mapPageName(pageName) {
    // Common page name mappings
    const nameMappings = {
      'Main README': 'Project Overview',
      'pnpm-workspaces-for-dummies': 'pnpm Workspaces Guide',
      'data-system': 'Data System'
    };
    
    return nameMappings[pageName] || pageName;
  }

  /**
   * Format tags for Logseq
   */
  formatTags(fileInfo) {
    const tags = this.standardizeTags(fileInfo.tags, fileInfo.category);
    const category = this.mapCategoryToTag(fileInfo.category);
    const status = this.determineStatus(fileInfo);
    
    const allTags = [category, `#status/${status}`, ...tags];
    return allTags.join(' ');
  }

  /**
   * Standardize tags for consistency
   */
  standardizeTags(originalTags, category) {
    const standardTags = [];
    
    for (const tag of originalTags) {
      const cleanTag = tag.replace(/^#/, '');
      
      // Skip tags that are now handled by category or status
      if (cleanTag.startsWith('status/') || cleanTag === category) {
        continue;
      }
      
      // Standardize common tags
      const tagMappings = {
        'fap': 'fap/platform',
        'components': 'fap/components',
        'business': 'business/processes',
        'corporate': 'corporate/structure',
        'technical': 'technical/architecture'
      };
      
      const mappedTag = tagMappings[cleanTag] || cleanTag;
      standardTags.push(`#${mappedTag}`);
    }
    
    return standardTags;
  }

  /**
   * Map category to primary tag
   */
  mapCategoryToTag(category) {
    const categoryMappings = {
      'root-readme': '#index',
      'project-management': '#project-management',
      'existing-docs': '#technical',
      'old-docs': '#archive',
      'fap-platform': '#fap/platform',
      'landmax-business': '#landmax',
      'technical-conventions': '#technical/conventions',
      'terminusdb-integration': '#technical/terminusdb',
      'component-docs': '#fap/components',
      'fap-components': '#fap/components',
      'business-processes': '#business/processes',
      'corporate-structure': '#corporate/structure',
      'uncategorized': '#uncategorized'
    };
    
    return categoryMappings[category] || '#uncategorized';
  }

  /**
   * Determine content type
   */
  mapContentType(category) {
    const typeMappings = {
      'root-readme': 'overview',
      'project-management': 'project-doc',
      'existing-docs': 'technical-doc',
      'old-docs': 'archive',
      'fap-platform': 'platform-doc',
      'landmax-business': 'business-doc',
      'technical-conventions': 'convention',
      'terminusdb-integration': 'integration-doc',
      'component-docs': 'component',
      'fap-components': 'component',
      'business-processes': 'business-process',
      'corporate-structure': 'corporate-doc',
      'uncategorized': 'document'
    };
    
    return typeMappings[category] || 'document';
  }

  /**
   * Determine document status
   */
  determineStatus(fileInfo) {
    const content = fileInfo.content.toLowerCase();
    
    if (content.includes('draft') || content.includes('wip') || content.includes('todo')) {
      return 'draft';
    }
    
    if (content.includes('deprecated') || content.includes('obsolete')) {
      return 'deprecated';
    }
    
    if (content.includes('review') || content.includes('pending')) {
      return 'review';
    }
    
    // Default to stable for existing documentation
    return 'stable';
  }

  /**
   * Determine target audience
   */
  determineAudience(fileInfo) {
    const content = fileInfo.content.toLowerCase();
    const audiences = [];
    
    if (content.includes('developer') || content.includes('code') || content.includes('api')) {
      audiences.push('developers');
    }
    
    if (content.includes('business') || content.includes('process') || content.includes('corporate')) {
      audiences.push('business-users');
    }
    
    if (content.includes('ai') || content.includes('mcp') || content.includes('agent')) {
      audiences.push('ai-agents');
    }
    
    return audiences.length > 0 ? audiences : ['general'];
  }

  /**
   * Detect related content
   */
  detectRelatedContent(fileInfo) {
    const related = [];
    
    // Extract page references from content
    const pageRefs = fileInfo.content.match(/\[\[([^\]]+)\]\]/g) || [];
    pageRefs.forEach(ref => {
      const pageName = ref.replace(/\[\[|\]\]/g, '');
      related.push(`[[${this.mapPageName(pageName)}]]`);
    });
    
    // Add category-based relationships
    if (fileInfo.category === 'fap-components') {
      related.push('[[FAP Core]]', '[[Platform Overview]]');
    }
    
    if (fileInfo.category === 'business-processes') {
      related.push('[[Corporate Map]]', '[[Business Map]]');
    }
    
    return [...new Set(related)]; // Remove duplicates
  }

  /**
   * Detect dependencies
   */
  detectDependencies(fileInfo) {
    const dependencies = [];
    
    // Look for package dependencies in content
    const packageRefs = fileInfo.content.match(/@[\w-]+\/[\w-]+/g) || [];
    packageRefs.forEach(pkg => {
      const componentName = pkg.replace('@acumen-desktop/', 'FAP ').replace('@fap/', 'FAP ');
      dependencies.push(`[[${componentName}]]`);
    });
    
    // Look for system dependencies
    if (fileInfo.content.includes('TerminusDB')) {
      dependencies.push('[[TerminusDB]]');
    }
    
    if (fileInfo.content.includes('pnpm')) {
      dependencies.push('[[pnpm Workspaces Guide]]');
    }
    
    return [...new Set(dependencies)]; // Remove duplicates
  }

  /**
   * Combine frontmatter and content
   */
  combineContent(frontmatter, content) {
    // Format frontmatter as YAML
    const yamlFrontmatter = this.formatFrontmatter(frontmatter);
    
    return `---\n${yamlFrontmatter}\n---\n\n${content}`;
  }

  /**
   * Format frontmatter as YAML
   */
  formatFrontmatter(frontmatter) {
    const lines = [];
    
    for (const [key, value] of Object.entries(frontmatter)) {
      if (Array.isArray(value)) {
        if (value.length > 0) {
          lines.push(`${key}:`);
          value.forEach(item => {
            lines.push(`  - "${item}"`);
          });
        }
      } else {
        lines.push(`${key}: "${value}"`);
      }
    }
    
    return lines.join('\n');
  }
}

export { ContentProcessor };