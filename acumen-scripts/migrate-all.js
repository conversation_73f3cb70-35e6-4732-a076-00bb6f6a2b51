#!/usr/bin/env node

/**
 * Automated Documentation Migration Tool
 * 
 * This script performs the complete migration of documentation from the current
 * scattered structure to the new Logseq-optimized organization.
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';
import { DocumentationMigrator } from './migrate-docs.js';
import { ContentProcessor } from './content-processor.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

class AutomatedMigrator {
  constructor() {
    this.migrator = new DocumentationMigrator();
    this.processor = new ContentProcessor();
    this.migrationResults = {
      processed: [],
      skipped: [],
      errors: [],
      duplicatesRemoved: []
    };
  }

  /**
   * Perform complete migration
   */
  async migrate() {
    console.log('🚀 Starting automated documentation migration...\n');
    
    try {
      // Step 1: Discover and analyze all files
      console.log('📊 Step 1: Analyzing existing documentation...');
      await this.migrator.discoverFiles();
      this.migrator.generateMigrationMap();
      
      const analysis = this.migrator.generateAnalysisReport();
      console.log(`Found ${analysis.summary.totalFiles} files, ${analysis.summary.duplicates} duplicates`);
      
      // Step 2: Create backup of existing structure
      console.log('\n💾 Step 2: Creating backup...');
      await this.createBackup();
      
      // Step 3: Ensure target directory structure exists
      console.log('\n📁 Step 3: Creating target directory structure...');
      await this.ensureDirectoryStructure();
      
      // Step 4: Process and migrate files
      console.log('\n🔄 Step 4: Processing and migrating files...');
      await this.processAllFiles();
      
      // Step 5: Create index and navigation files
      console.log('\n🗺️  Step 5: Creating navigation and index files...');
      await this.createNavigationFiles();
      
      // Step 6: Validate migration
      console.log('\n✅ Step 6: Validating migration...');
      const validation = await this.validateMigration();
      
      // Step 7: Generate migration report
      console.log('\n📋 Step 7: Generating migration report...');
      await this.generateMigrationReport(validation);
      
      console.log('\n🎉 Migration completed successfully!');
      this.printSummary();
      
    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      console.error('Stack trace:', error.stack);
      process.exit(1);
    }
  }

  /**
   * Create backup of existing documentation
   */
  async createBackup() {
    const backupDir = path.join(rootDir, '_migration-backup');
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(backupDir, `docs-backup-${timestamp}`);
    
    try {
      await fs.mkdir(backupPath, { recursive: true });
      
      // Copy existing docs directories
      const dirsToBackup = ['docs', 'docs-old'];
      
      for (const dir of dirsToBackup) {
        const sourcePath = path.join(rootDir, dir);
        const targetPath = path.join(backupPath, dir);
        
        try {
          await fs.access(sourcePath);
          await this.copyDirectory(sourcePath, targetPath);
          console.log(`  ✅ Backed up ${dir}/`);
        } catch (error) {
          console.log(`  ⚠️  Skipped ${dir}/ (not found)`);
        }
      }
      
      console.log(`📦 Backup created at: ${backupPath}`);
      
    } catch (error) {
      console.warn(`⚠️  Backup failed: ${error.message}`);
    }
  }

  /**
   * Copy directory recursively
   */
  async copyDirectory(source, target) {
    await fs.mkdir(target, { recursive: true });
    const entries = await fs.readdir(source, { withFileTypes: true });
    
    for (const entry of entries) {
      const sourcePath = path.join(source, entry.name);
      const targetPath = path.join(target, entry.name);
      
      if (entry.isDirectory()) {
        await this.copyDirectory(sourcePath, targetPath);
      } else {
        await fs.copyFile(sourcePath, targetPath);
      }
    }
  }

  /**
   * Ensure all target directories exist
   */
  async ensureDirectoryStructure() {
    const directories = [
      'docs-new/content/00-index',
      'docs-new/content/01-getting-started',
      'docs-new/content/02-corporate',
      'docs-new/content/03-fap-platform/components',
      'docs-new/content/03-fap-platform/architecture',
      'docs-new/content/03-fap-platform/tutorials',
      'docs-new/content/04-landmax/setup',
      'docs-new/content/04-landmax/user-guides',
      'docs-new/content/05-technical/architecture',
      'docs-new/content/05-technical/development',
      'docs-new/content/05-technical/third-party/terminusdb',
      'docs-new/content/05-technical/conventions',
      'docs-new/content/06-business/processes',
      'docs-new/content/06-business/policies',
      'docs-new/content/07-open-source/contributing',
      'docs-new/content/07-open-source/community',
      'docs-new/content/08-project-management',
      'docs-new/content/09-uncategorized',
      'docs-new/assets/images',
      'docs-new/assets/graphs',
      'docs-new/assets/exports',
      'docs-new/pages',
      'docs-new/journals'
    ];
    
    for (const dir of directories) {
      const fullPath = path.join(rootDir, dir);
      await fs.mkdir(fullPath, { recursive: true });
    }
    
    console.log(`📁 Created ${directories.length} directories`);
  }

  /**
   * Process all files according to migration map
   */
  async processAllFiles() {
    const migrationMap = this.migrator.migrationMap;
    let processed = 0;
    let skipped = 0;
    
    for (const [sourcePath, mapping] of migrationMap) {
      try {
        const action = mapping.action;
        const sourceFile = mapping.source;
        const targetPath = mapping.target;
        
        console.log(`  Processing: ${sourcePath} -> ${action}`);
        
        switch (action) {
          case 'migrate':
            await this.migrateFile(sourceFile, targetPath);
            this.migrationResults.processed.push({
              source: sourcePath,
              target: targetPath,
              category: sourceFile.category
            });
            processed++;
            break;
            
          case 'skip-duplicate':
            console.log(`    ⏭️  Skipping duplicate: ${sourcePath}`);
            this.migrationResults.skipped.push({
              source: sourcePath,
              reason: 'duplicate'
            });
            skipped++;
            break;
            
          case 'skip-already-migrated':
            console.log(`    ⏭️  Already migrated: ${sourcePath}`);
            this.migrationResults.skipped.push({
              source: sourcePath,
              reason: 'already-migrated'
            });
            skipped++;
            break;
            
          case 'archive':
            console.log(`    📦 Archiving: ${sourcePath}`);
            // Could implement archiving logic here
            this.migrationResults.skipped.push({
              source: sourcePath,
              reason: 'archived'
            });
            skipped++;
            break;
            
          default:
            console.log(`    ❓ Unknown action: ${action}`);
            this.migrationResults.skipped.push({
              source: sourcePath,
              reason: 'unknown-action'
            });
            skipped++;
        }
        
      } catch (error) {
        console.error(`    ❌ Error processing ${sourcePath}: ${error.message}`);
        this.migrationResults.errors.push({
          source: sourcePath,
          error: error.message
        });
      }
    }
    
    console.log(`📊 Processed: ${processed}, Skipped: ${skipped}, Errors: ${this.migrationResults.errors.length}`);
  }

  /**
   * Migrate a single file
   */
  async migrateFile(sourceFile, targetPath) {
    // Process content for Logseq
    const processed = await this.processor.processFile(sourceFile, targetPath);
    
    // Ensure target directory exists
    const targetDir = path.dirname(path.join(rootDir, targetPath));
    await fs.mkdir(targetDir, { recursive: true });
    
    // Write processed content
    const fullTargetPath = path.join(rootDir, targetPath);
    await fs.writeFile(fullTargetPath, processed.content, 'utf-8');
    
    console.log(`    ✅ Migrated to: ${targetPath}`);
  }

  /**
   * Create navigation and index files
   */
  async createNavigationFiles() {
    // Update the main Documentation Hub with actual content
    await this.updateDocumentationHub();
    
    // Create component index
    await this.createComponentIndex();
    
    // Create business process index
    await this.createBusinessIndex();
    
    // Create technical index
    await this.createTechnicalIndex();
    
    console.log('🗺️  Navigation files created');
  }

  /**
   * Update Documentation Hub with actual migrated content
   */
  async updateDocumentationHub() {
    const hubPath = path.join(rootDir, 'docs-new/content/00-index/Documentation Hub.md');
    
    // Count actual migrated files by category
    const stats = this.calculateMigrationStats();
    
    const hubContent = `---
title: "Documentation Hub"
type: "hub"
category: "#index"
tags:
  - "#navigation"
  - "#hub"
status: "stable"
audience:
  - "developers"
  - "business-users"
  - "ai-agents"
created: "${new Date().toISOString().split('T')[0]}"
updated: "${new Date().toISOString().split('T')[0]}"
---

- # Documentation Hub #index #navigation
  type:: hub
  created:: ${new Date().toISOString().split('T')[0]}
  - ## Welcome to the FAP Monorepo Knowledge Base
    - This is the central hub for all documentation related to the Freedom Application Platform (FAP) monorepo, Acumen Desktop Software Canada Inc., and related business operations.
    - **Navigation**: Use the links below to explore different areas of documentation
    - **Search**: Use Logseq's search (Ctrl/Cmd + K) to find specific information
    - **Graph**: Use the graph view to explore relationships between concepts
  - ## 🚀 Quick Start
    - [[Installation]] - Get started with development setup
    - [[Development Setup]] - Configure your development environment  
    - [[Logseq Setup]] - How to use this documentation system
    - [[pnpm Workspaces Guide]] - Understanding the monorepo structure
  - ## 🏢 Corporate Information
    - [[Corporate Map]] - Overview of corporate structure and relationships
    - [[Entity Structure]] - Acumen Desktop Software Canada Inc. details
    - [[Directors and Officers]] - Leadership and governance information
    - [[Legal and Compliance]] - Regulatory requirements and compliance
  - ## 🌟 FAP Platform
    - [[Technical Map]] - Technical architecture overview
    - [[Platform Overview]] - FAP philosophy and core concepts
    - [[Component Index]] - All FAP components and their relationships
    - [[Architecture Overview]] - System architecture and design decisions
  - ## 💼 Business Operations
    - [[Business Map]] - Business processes and operations overview
    - [[LandMax Business]] - LandMax real estate implementation
    - [[Client Relationships]] - Customer and partner information
    - [[Business Processes]] - Operational procedures and workflows
  - ## 🔧 Technical Documentation
    - [[Data System]] - Database architecture and strategy
    - [[TerminusDB Integration]] - Graph database implementation
    - [[Development Conventions]] - Coding standards and practices
    - [[Third Party Integrations]] - External system integrations
  - ## 📋 Project Management
    - [[Current Projects]] - Active development projects
    - [[Session Notes]] - Development session documentation
    - [[Implementation Plans]] - Project roadmaps and specifications
    - [[Migration Status]] - Documentation system migration progress
  - ## 📊 Migration Statistics
    - **Total Files Migrated**: ${stats.totalMigrated}
    - **FAP Platform Docs**: ${stats.fapPlatform}
    - **Technical Documentation**: ${stats.technical}
    - **Corporate Documentation**: ${stats.corporate}
    - **Business Documentation**: ${stats.business}
    - **Migration Date**: ${new Date().toISOString().split('T')[0]}
    - **System Version**: 1.0.0`;

    await fs.writeFile(hubPath, hubContent);
  }

  /**
   * Calculate migration statistics
   */
  calculateMigrationStats() {
    const stats = {
      totalMigrated: this.migrationResults.processed.length,
      fapPlatform: 0,
      technical: 0,
      corporate: 0,
      business: 0
    };

    for (const result of this.migrationResults.processed) {
      const category = result.category;
      
      if (category.includes('fap')) {
        stats.fapPlatform++;
      } else if (category.includes('technical') || category.includes('terminusdb')) {
        stats.technical++;
      } else if (category.includes('corporate')) {
        stats.corporate++;
      } else if (category.includes('business')) {
        stats.business++;
      }
    }

    return stats;
  }

  /**
   * Create component index
   */
  async createComponentIndex() {
    const componentFiles = this.migrationResults.processed.filter(
      result => result.category.includes('component') || result.category.includes('fap')
    );

    const indexContent = `---
title: "Component Index"
type: "index"
category: "#fap/components"
tags:
  - "#fap/platform"
  - "#components"
  - "#index"
status: "stable"
audience:
  - "developers"
created: "${new Date().toISOString().split('T')[0]}"
updated: "${new Date().toISOString().split('T')[0]}"
---

- # Component Index #fap/components #index
  - ## FAP Platform Components
    - Complete catalog of all FAP platform components and their relationships
  - ## Core Components
    ${componentFiles.map(file => `    - [[${path.basename(file.target, '.md')}]] - Component documentation`).join('\n')}
  - ## Component Relationships
    - All components depend on [[FAP Core]] for theming and utilities
    - Components can be used independently or combined in applications
  - ## Usage Examples
    - See individual component documentation for usage examples
    - Check [[Platform Overview]] for architectural guidance`;

    const indexPath = path.join(rootDir, 'docs-new/content/03-fap-platform/components/Component Index.md');
    await fs.writeFile(indexPath, indexContent);
  }

  /**
   * Create business index
   */
  async createBusinessIndex() {
    const businessContent = `---
title: "Business Processes"
type: "index"
category: "#business/processes"
tags:
  - "#business"
  - "#processes"
  - "#index"
status: "stable"
audience:
  - "business-users"
created: "${new Date().toISOString().split('T')[0]}"
updated: "${new Date().toISOString().split('T')[0]}"
---

- # Business Processes #business/processes #index
  - ## Overview
    - Index of all business processes and operational procedures
  - ## Process Categories
    - Software Development Processes
    - Client Management Processes
    - Corporate Governance Processes
  - ## Related Documentation
    - [[Business Map]] - Overall business operations overview
    - [[Corporate Map]] - Corporate structure and governance`;

    const indexPath = path.join(rootDir, 'docs-new/content/06-business/processes/Business Processes.md');
    await fs.writeFile(indexPath, businessContent);
  }

  /**
   * Create technical index
   */
  async createTechnicalIndex() {
    const technicalContent = `---
title: "Technical Documentation"
type: "index"
category: "#technical"
tags:
  - "#technical"
  - "#architecture"
  - "#index"
status: "stable"
audience:
  - "developers"
  - "ai-agents"
created: "${new Date().toISOString().split('T')[0]}"
updated: "${new Date().toISOString().split('T')[0]}"
---

- # Technical Documentation #technical #index
  - ## Architecture Documentation
    - System architecture and design decisions
  - ## Development Guides
    - Development setup and workflow documentation
  - ## Third-Party Integrations
    - External system integrations and configurations
  - ## Conventions and Standards
    - Coding standards and development conventions
  - ## Related Documentation
    - [[Technical Map]] - Technical architecture overview
    - [[Platform Overview]] - FAP platform technical philosophy`;

    const indexPath = path.join(rootDir, 'docs-new/content/05-technical/Technical Documentation.md');
    await fs.writeFile(indexPath, technicalContent);
  }

  /**
   * Validate migration results
   */
  async validateMigration() {
    console.log('🔍 Validating migration...');
    
    const validation = {
      success: true,
      issues: [],
      stats: {
        totalFiles: 0,
        validFiles: 0,
        brokenLinks: [],
        missingFiles: []
      }
    };

    // Check that target files exist and are readable
    for (const result of this.migrationResults.processed) {
      const targetPath = path.join(rootDir, result.target);
      
      try {
        await fs.access(targetPath);
        const content = await fs.readFile(targetPath, 'utf-8');
        
        if (content.length > 0) {
          validation.stats.validFiles++;
        } else {
          validation.issues.push(`Empty file: ${result.target}`);
        }
        
        validation.stats.totalFiles++;
        
      } catch (error) {
        validation.success = false;
        validation.issues.push(`Missing file: ${result.target}`);
        validation.stats.missingFiles.push(result.target);
      }
    }

    // Validate Logseq configuration
    const configPath = path.join(rootDir, 'docs-new/logseq/config.edn');
    try {
      await fs.access(configPath);
      console.log('  ✅ Logseq configuration exists');
    } catch (error) {
      validation.success = false;
      validation.issues.push('Missing Logseq configuration');
    }

    console.log(`📊 Validation: ${validation.stats.validFiles}/${validation.stats.totalFiles} files valid`);
    
    if (validation.issues.length > 0) {
      console.log('⚠️  Issues found:');
      validation.issues.forEach(issue => console.log(`    - ${issue}`));
    }

    return validation;
  }

  /**
   * Generate comprehensive migration report
   */
  async generateMigrationReport(validation) {
    const report = {
      migration: {
        timestamp: new Date().toISOString(),
        success: validation.success,
        summary: {
          totalSourceFiles: this.migrator.sourceFiles.length,
          duplicatesFound: this.migrator.duplicates.length,
          filesProcessed: this.migrationResults.processed.length,
          filesSkipped: this.migrationResults.skipped.length,
          errors: this.migrationResults.errors.length
        }
      },
      validation: validation,
      results: this.migrationResults,
      sourceAnalysis: this.migrator.generateAnalysisReport()
    };

    const reportPath = path.join(rootDir, 'migration-report.json');
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    
    console.log(`📋 Migration report saved to: migration-report.json`);
    return report;
  }

  /**
   * Print migration summary
   */
  printSummary() {
    console.log('\n📊 Migration Summary');
    console.log('===================');
    console.log(`✅ Files processed: ${this.migrationResults.processed.length}`);
    console.log(`⏭️  Files skipped: ${this.migrationResults.skipped.length}`);
    console.log(`❌ Errors: ${this.migrationResults.errors.length}`);
    
    if (this.migrationResults.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      this.migrationResults.errors.forEach(error => {
        console.log(`  - ${error.source}: ${error.error}`);
      });
    }
    
    console.log('\n🎯 Next Steps:');
    console.log('1. Review migration-report.json for detailed results');
    console.log('2. Open docs-new/ in Logseq to explore the knowledge base');
    console.log('3. Verify links and relationships in the graph view');
    console.log('4. Update any broken links or missing content');
    console.log('5. Replace old docs/ with docs-new/ when satisfied');
  }
}

// Main execution
async function main() {
  const migrator = new AutomatedMigrator();
  await migrator.migrate();
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { AutomatedMigrator };