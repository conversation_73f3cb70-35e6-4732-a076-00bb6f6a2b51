#!/usr/bin/env node

/**
 * Documentation Migration Tool
 * 
 * This script discovers, analyzes, and migrates all markdown files in the monorepo
 * to the new Logseq-optimized documentation structure.
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '..');

class DocumentationMigrator {
  constructor() {
    this.sourceFiles = [];
    this.duplicates = [];
    this.migrationMap = new Map();
    this.errors = [];
    this.warnings = [];
  }

  /**
   * Discover all markdown files in the repository
   */
  async discoverFiles() {
    console.log('🔍 Discovering markdown files...');
    
    const excludeDirs = [
      'node_modules',
      '.git',
      '.kiro',
      'docs-new', // Don't include our new structure
      'data/databases/terminusdb/dashboard/original-clone' // Exclude large third-party code
    ];

    await this.scanDirectory(rootDir, excludeDirs);
    
    console.log(`📄 Found ${this.sourceFiles.length} markdown files`);
    return this.sourceFiles;
  }

  /**
   * Recursively scan directory for markdown files
   */
  async scanDirectory(dir, excludeDirs = []) {
    try {
      const entries = await fs.readdir(dir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dir, entry.name);
        const relativePath = path.relative(rootDir, fullPath);
        
        // Skip excluded directories
        if (entry.isDirectory() && excludeDirs.some(exclude => 
          relativePath.startsWith(exclude) || entry.name === exclude
        )) {
          continue;
        }
        
        if (entry.isDirectory()) {
          await this.scanDirectory(fullPath, excludeDirs);
        } else if (entry.name.endsWith('.md')) {
          await this.analyzeFile(fullPath);
        }
      }
    } catch (error) {
      this.errors.push({
        type: 'scan_error',
        path: dir,
        error: error.message
      });
    }
  }

  /**
   * Analyze a markdown file for content and metadata
   */
  async analyzeFile(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf-8');
      const relativePath = path.relative(rootDir, filePath);
      const stats = await fs.stat(filePath);
      
      const fileInfo = {
        path: filePath,
        relativePath: relativePath,
        size: stats.size,
        modified: stats.mtime,
        content: content,
        title: this.extractTitle(content),
        links: this.extractLinks(content),
        tags: this.extractTags(content),
        frontmatter: this.extractFrontmatter(content),
        category: this.categorizeFile(relativePath, content),
        contentHash: this.hashContent(content)
      };
      
      this.sourceFiles.push(fileInfo);
      
      // Check for duplicates based on content hash
      const existing = this.sourceFiles.find(f => 
        f !== fileInfo && f.contentHash === fileInfo.contentHash
      );
      if (existing) {
        this.duplicates.push({
          original: existing.relativePath,
          duplicate: fileInfo.relativePath,
          hash: fileInfo.contentHash
        });
      }
      
    } catch (error) {
      this.errors.push({
        type: 'file_analysis_error',
        path: filePath,
        error: error.message
      });
    }
  }

  /**
   * Extract title from markdown content
   */
  extractTitle(content) {
    // Look for first H1 heading
    const h1Match = content.match(/^#\s+(.+)$/m);
    if (h1Match) return h1Match[1].trim();
    
    // Look for title in frontmatter
    const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
    if (frontmatterMatch) {
      const titleMatch = frontmatterMatch[1].match(/^title:\s*["']?([^"'\n]+)["']?$/m);
      if (titleMatch) return titleMatch[1].trim();
    }
    
    return 'Untitled';
  }

  /**
   * Extract markdown links from content
   */
  extractLinks(content) {
    const links = [];
    
    // Extract markdown links [text](url)
    const markdownLinks = content.match(/\[([^\]]+)\]\(([^)]+)\)/g) || [];
    markdownLinks.forEach(link => {
      const match = link.match(/\[([^\]]+)\]\(([^)]+)\)/);
      if (match) {
        links.push({
          type: 'markdown',
          text: match[1],
          url: match[2]
        });
      }
    });
    
    // Extract wiki-style links [[Page Name]]
    const wikiLinks = content.match(/\[\[([^\]]+)\]\]/g) || [];
    wikiLinks.forEach(link => {
      const match = link.match(/\[\[([^\]]+)\]\]/);
      if (match) {
        links.push({
          type: 'wiki',
          text: match[1],
          url: match[1]
        });
      }
    });
    
    return links;
  }

  /**
   * Extract tags from content (looking for #tag patterns)
   */
  extractTags(content) {
    const tags = [];
    
    // Look for hashtags in content
    const hashtagMatches = content.match(/#[\w-]+/g) || [];
    hashtagMatches.forEach(tag => {
      if (!tags.includes(tag)) {
        tags.push(tag);
      }
    });
    
    // Look for tags in frontmatter
    const frontmatterMatch = content.match(/^---\n([\s\S]*?)\n---/);
    if (frontmatterMatch) {
      const tagsMatch = frontmatterMatch[1].match(/^tags:\s*\[(.*?)\]$/m);
      if (tagsMatch) {
        const frontmatterTags = tagsMatch[1].split(',').map(t => t.trim().replace(/["']/g, ''));
        tags.push(...frontmatterTags);
      }
    }
    
    return [...new Set(tags)]; // Remove duplicates
  }

  /**
   * Extract frontmatter from markdown content
   */
  extractFrontmatter(content) {
    const match = content.match(/^---\n([\s\S]*?)\n---/);
    if (!match) return null;
    
    try {
      // Simple YAML parsing for basic frontmatter
      const frontmatter = {};
      const lines = match[1].split('\n');
      
      for (const line of lines) {
        const colonIndex = line.indexOf(':');
        if (colonIndex > 0) {
          const key = line.substring(0, colonIndex).trim();
          const value = line.substring(colonIndex + 1).trim().replace(/["']/g, '');
          frontmatter[key] = value;
        }
      }
      
      return frontmatter;
    } catch (error) {
      return null;
    }
  }

  /**
   * Categorize file based on path and content
   */
  categorizeFile(relativePath, content) {
    // Root level files
    if (!relativePath.includes('/')) {
      if (relativePath === 'README.md') return 'root-readme';
      if (relativePath.includes('INSTRUCTION')) return 'project-management';
      if (relativePath.includes('PLAN')) return 'project-management';
      return 'root-misc';
    }
    
    // Path-based categorization
    if (relativePath.startsWith('docs/')) return 'existing-docs';
    if (relativePath.startsWith('docs-old/')) return 'old-docs';
    if (relativePath.startsWith('fap/')) return 'fap-platform';
    if (relativePath.startsWith('landmax/')) return 'landmax-business';
    if (relativePath.includes('conventions/')) return 'technical-conventions';
    if (relativePath.includes('terminusdb/')) return 'terminusdb-integration';
    if (relativePath.includes('packages/')) return 'component-docs';
    
    // Content-based categorization
    if (content.includes('#fap/components')) return 'fap-components';
    if (content.includes('#business/')) return 'business-processes';
    if (content.includes('#corporate/')) return 'corporate-structure';
    if (content.includes('Corporation Number')) return 'corporate-structure';
    if (content.includes('TerminusDB')) return 'terminusdb-integration';
    
    return 'uncategorized';
  }

  /**
   * Create a simple hash of content for duplicate detection
   */
  hashContent(content) {
    // Remove whitespace variations and normalize for comparison
    const normalized = content
      .replace(/\r\n/g, '\n')
      .replace(/\s+/g, ' ')
      .trim()
      .toLowerCase();
    
    // Simple hash function
    let hash = 0;
    for (let i = 0; i < normalized.length; i++) {
      const char = normalized.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  /**
   * Generate migration mapping from source to target locations
   */
  generateMigrationMap() {
    console.log('🗺️  Generating migration map...');
    
    for (const file of this.sourceFiles) {
      const targetPath = this.determineTargetPath(file);
      this.migrationMap.set(file.relativePath, {
        source: file,
        target: targetPath,
        action: this.determineMigrationAction(file)
      });
    }
    
    console.log(`📋 Generated migration plan for ${this.migrationMap.size} files`);
    return this.migrationMap;
  }

  /**
   * Determine target path for a file in the unified docs structure
   */
  determineTargetPath(file) {
    const category = file.category;
    const baseName = path.basename(file.relativePath);

    switch (category) {
      case 'root-readme':
        return 'docs/content/00-overview/Project Overview.md';

      case 'project-management':
        return `docs/content/08-project-mgmt/${this.sanitizeFileName(file.title)}.md`;

      case 'existing-docs':
      case 'old-docs':
        return this.mapExistingDocsPath(file);

      case 'fap-platform':
        return this.mapFAPPlatformPath(file);

      case 'landmax-business':
        return `docs/content/04-landmax/${this.sanitizeFileName(file.title)}.md`;

      case 'technical-conventions':
        return `docs/content/05-technical/conventions/${this.sanitizeFileName(file.title)}.md`;

      case 'terminusdb-integration':
        return `docs/content/06-integrations/terminusdb/${this.sanitizeFileName(file.title)}.md`;

      case 'component-docs':
        return `docs/content/03-fap-platform/components/${this.sanitizeFileName(file.title)}.md`;

      case 'fap-components':
        return `docs/content/03-fap-platform/components/${this.sanitizeFileName(file.title)}.md`;

      case 'business-processes':
        return `docs/content/02-corporate/processes/${this.sanitizeFileName(file.title)}.md`;

      case 'corporate-structure':
        return `docs/content/02-corporate/${this.sanitizeFileName(file.title)}.md`;

      default:
        return `docs/content/09-uncategorized/${baseName}`;
    }
  }

  /**
   * Map existing docs to unified structure
   */
  mapExistingDocsPath(file) {
    const relativePath = file.relativePath;

    if (relativePath.includes('pnpm-workspaces')) {
      return 'docs/content/01-getting-started/pnpm Workspaces Guide.md';
    }

    if (relativePath.includes('third-party')) {
      const fileName = this.sanitizeFileName(file.title);
      return `docs/content/06-integrations/${fileName}.md`;
    }

    if (relativePath.includes('architecture')) {
      const fileName = this.sanitizeFileName(file.title);
      return `docs/content/05-technical/architecture/${fileName}.md`;
    }

    if (relativePath.includes('github-packages') || relativePath.includes('publishing')) {
      const fileName = this.sanitizeFileName(file.title);
      return `docs/content/05-technical/development/${fileName}.md`;
    }

    // Default technical documentation
    const fileName = this.sanitizeFileName(file.title);
    return `docs/content/05-technical/${fileName}.md`;
  }

  /**
   * Map FAP platform files to unified structure
   */
  mapFAPPlatformPath(file) {
    const relativePath = file.relativePath;

    if (relativePath.includes('packages/') && relativePath.includes('README.md')) {
      const packageName = this.extractPackageName(relativePath);
      return `docs/content/03-fap-platform/components/${packageName}.md`;
    }

    if (relativePath.includes('conventions/')) {
      const fileName = this.sanitizeFileName(file.title);
      return `docs/content/05-technical/conventions/${fileName}.md`;
    }

    if (relativePath === 'acumen-fap/README.md') {
      return 'docs/content/03-fap-platform/Platform Overview.md';
    }

    // Default FAP platform documentation
    const fileName = this.sanitizeFileName(file.title);
    return `docs/content/03-fap-platform/${fileName}.md`;
  }

  /**
   * Extract package name from path
   */
  extractPackageName(filePath) {
    const match = filePath.match(/packages\/([^\/]+)/);
    return match ? match[1] : 'unknown-package';
  }

  /**
   * Sanitize filename for filesystem compatibility
   */
  sanitizeFileName(title) {
    return title
      .replace(/[<>:"/\\|?*]/g, '') // Remove invalid characters
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
  }

  /**
   * Determine what action to take for each file
   */
  determineMigrationAction(file) {
    // Check if this is a duplicate
    const isDuplicate = this.duplicates.some(dup => 
      dup.duplicate === file.relativePath
    );
    
    if (isDuplicate) return 'skip-duplicate';
    
    // Check if file is already in new structure
    if (file.relativePath.startsWith('docs-new/')) return 'skip-already-migrated';
    
    // Check if file should be archived instead of migrated
    if (file.category === 'old-docs') return 'archive';
    
    return 'migrate';
  }

  /**
   * Generate comprehensive analysis report
   */
  generateAnalysisReport() {
    const report = {
      summary: {
        totalFiles: this.sourceFiles.length,
        duplicates: this.duplicates.length,
        errors: this.errors.length,
        warnings: this.warnings.length,
        categories: this.getCategorySummary()
      },
      files: this.sourceFiles,
      duplicates: this.duplicates,
      migrationMap: Array.from(this.migrationMap.entries()).map(([source, mapping]) => ({
        source,
        target: mapping.target,
        action: mapping.action,
        category: mapping.source.category
      })),
      errors: this.errors,
      warnings: this.warnings
    };
    
    return report;
  }

  /**
   * Get summary of files by category
   */
  getCategorySummary() {
    const categories = {};
    for (const file of this.sourceFiles) {
      categories[file.category] = (categories[file.category] || 0) + 1;
    }
    return categories;
  }

  /**
   * Save analysis report to file
   */
  async saveReport(report, outputPath = 'migration-analysis.json') {
    try {
      await fs.writeFile(outputPath, JSON.stringify(report, null, 2));
      console.log(`📊 Analysis report saved to ${outputPath}`);
    } catch (error) {
      console.error('❌ Failed to save report:', error.message);
    }
  }

  /**
   * Print summary to console
   */
  printSummary(report) {
    console.log('\n📊 Migration Analysis Summary');
    console.log('================================');
    console.log(`Total files found: ${report.summary.totalFiles}`);
    console.log(`Duplicates detected: ${report.summary.duplicates}`);
    console.log(`Errors encountered: ${report.summary.errors}`);
    console.log(`Warnings generated: ${report.summary.warnings}`);
    
    console.log('\n📁 Files by Category:');
    for (const [category, count] of Object.entries(report.summary.categories)) {
      console.log(`  ${category}: ${count}`);
    }
    
    if (report.duplicates.length > 0) {
      console.log('\n🔄 Duplicate Files:');
      for (const dup of report.duplicates) {
        console.log(`  ${dup.original} ↔️ ${dup.duplicate}`);
      }
    }
    
    if (report.errors.length > 0) {
      console.log('\n❌ Errors:');
      for (const error of report.errors) {
        console.log(`  ${error.type}: ${error.path} - ${error.error}`);
      }
    }
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting documentation migration analysis...\n');
  
  const migrator = new DocumentationMigrator();
  
  try {
    // Discover all markdown files
    await migrator.discoverFiles();
    
    // Generate migration mapping
    migrator.generateMigrationMap();
    
    // Generate comprehensive report
    const report = migrator.generateAnalysisReport();
    
    // Print summary
    migrator.printSummary(report);
    
    // Save detailed report
    await migrator.saveReport(report);
    
    console.log('\n✅ Analysis complete! Review migration-analysis.json for details.');
    
  } catch (error) {
    console.error('❌ Migration analysis failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { DocumentationMigrator };