{"metadata": {"generated": "2025-07-29T20:19:10.950Z", "total": 282, "maxDepth": 5}, "files": [".changeset/CLAUDE.md", ".changeset/README.md", ".changeset/config.json", ".claude/.mcp copy.json", ".claude/CLAUDE.md", ".claude/agents/CLAUDE.md", ".claude/agents/first-project-agent.md", ".claude/commands/CLAUDE.md", ".claude/settings.local.json", ".giti<PERSON>re", ".npmrc", "CLAUDE.md", "README.md", "acumen-corporate/CLAUDE.md", "acumen-corporate/ai-domains-hosting-saas-tech/CLAUDE.md", "acumen-corporate/contracts/CLAUDE.md", "acumen-corporate/finance/CLAUDE.md", "acumen-corporate/finance/taxes/CLAUDE.md", "acumen-corporate/legal/CLAUDE.md", "acumen-corporate/legal/incorporation/CLAUDE.md", "acumen-corporate/operations/CLAUDE.md", "acumen-customer_websites/CLAUDE.md", "acumen-customer_websites/landmax/CLAUDE.md", "acumen-customer_websites/landmax/README.md", "acumen-customer_websites/landmax/apps-desktop/CLAUDE.md", "acumen-customer_websites/landmax/apps-p2p/CLAUDE.md", "acumen-customer_websites/landmax/apps-web/CLAUDE.md", "acumen-customer_websites/landmax/data-private/CLAUDE.md", "acumen-customer_websites/landmax/data-private/clients/CLAUDE.md", "acumen-customer_websites/landmax/data-private/legal/CLAUDE.md", "acumen-customer_websites/landmax/data-private/processes/CLAUDE.md", "acumen-customer_websites/landmax/data-private/properties/CLAUDE.md", "acumen-customer_websites/landmax/data-private/transactions/CLAUDE.md", "acumen-customer_websites/santa_fd/CLAUDE.md", "acumen-customer_websites/swansburg_family/CLAUDE.md", "acumen-customer_websites/true_health/CLAUDE.md", "acumen-customer_websites/urban_stix/CLAUDE.md", "acumen-customer_websites/wasa_lakeside/CLAUDE.md", "acumen-fap/CLAUDE.md", "acumen-fap/README.md", "acumen-fap/ai/CLAUDE.md", "acumen-fap/ai/claude/CLAUDE.md", "acumen-fap/ai/claude/_notes/CLAUDE.md", "acumen-fap/ai/claude/_notes/claude-memory-overview.md", "acumen-fap/ai/claude/_notes/setup-journey-2025-07-28.md", "acumen-fap/ai/claude/agents/CLAUDE.md", "acumen-fap/ai/claude/agents/agents-creation-guidelines.md", "acumen-fap/ai/claude/agents/agents-index.md", "acumen-fap/ai/claude/agents/agents-management-instructions.md", "acumen-fap/ai/claude/agents/mcp-installer-superpowers.md", "acumen-fap/ai/claude/agents/mcp-installer.md", "acumen-fap/ai/claude/mcp_servers/CLAUDE.md", "acumen-fap/ai/claude/mcp_servers/final-mcp-process.md", "acumen-fap/ai/claude/mcp_servers/mcp-installation-log.md", "acumen-fap/ai/claude/mcp_servers/mcp-manager-agent.md", "acumen-fap/ai/claude/mcp_servers/mcp-servers-creation-guidelines.md", "acumen-fap/ai/claude/mcp_servers/mcp-servers-index.md", "acumen-fap/ai/claude/mcp_servers/mcp-servers-management.md", "acumen-fap/ai/claude/mcp_servers/mcp_terminal_logs.md", "acumen-fap/ai/claude/mcp_servers/session-summary-2025-07-28.md", "acumen-fap/ai/claude/mcp_servers/simple-mcp-setup.md", "acumen-fap/ai/claude/slash_commands/CLAUDE.md", "acumen-fap/ai/claude/slash_commands/slash-commands-creation-guidelines.md", "acumen-fap/ai/claude/slash_commands/slash-commands-index.md", "acumen-fap/ai/claude/slash_commands/slash-commands-management-instructions.md", "acumen-fap/apps-desktop/CLAUDE.md", "acumen-fap/apps-p2p/CLAUDE.md", "acumen-fap/apps-web/CLAUDE.md", "acumen-fap/apps-web/chat-simple/CHANGELOG.md", "acumen-fap/apps-web/chat-simple/CLAUDE.md", "acumen-fap/apps-web/chat-simple/demo.js", "acumen-fap/apps-web/chat-simple/example-plugin.js", "acumen-fap/apps-web/chat-simple/index.html", "acumen-fap/apps-web/chat-simple/package.json", "acumen-fap/apps-web/chat-simple/search-plugin.js", "acumen-fap/apps-web/github-demo/CLAUDE.md", "acumen-fap/apps-web/github-demo/github-demo.js", "acumen-fap/apps-web/github-demo/index.html", "acumen-fap/apps-web/github-demo/package.json", "acumen-fap/assets/CLAUDE.md", "acumen-fap/assets/README.md", "acumen-fap/assets/audio/CLAUDE.md", "acumen-fap/assets/branding/CLAUDE.md", "acumen-fap/assets/fonts/CLAUDE.md", "acumen-fap/assets/icons/CLAUDE.md", "acumen-fap/assets/icons/common/CLAUDE.md", "acumen-fap/assets/icons/fap/CLAUDE.md", "acumen-fap/assets/icons/landmax/CLAUDE.md", "acumen-fap/assets/images/CLAUDE.md", "acumen-fap/assets/images/graphics/CLAUDE.md", "acumen-fap/assets/images/logos/CLAUDE.md", "acumen-fap/assets/images/screenshots/CLAUDE.md", "acumen-fap/assets/video/CLAUDE.md", "acumen-fap/data/.gitignore", "acumen-fap/data/CLAUDE.md", "acumen-fap/data/README.md", "acumen-fap/data/backups/CLAUDE.md", "acumen-fap/data/backups/README.md", "acumen-fap/data/backups/daily/CLAUDE.md", "acumen-fap/data/backups/monthly/CLAUDE.md", "acumen-fap/data/backups/weekly/CLAUDE.md", "acumen-fap/data/databases/CLAUDE.md", "acumen-fap/data/databases/sqlite/CLAUDE.md", "acumen-fap/data/databases/sqlite/README.md", "acumen-fap/data/databases/terminusdb/CLAUDE.md", "acumen-fap/data/databases/terminusdb/README.md", "acumen-fap/data/databases/terminusdb/TERMINUSDB_CHEATSHEET.md", "acumen-fap/data/databases/terminusdb/test-corporate-data.json", "acumen-fap/data/databases/terminusdb/test-corporate-insert.json", "acumen-fap/data/databases/terminusdb/test-insert-with-author.json", "acumen-fap/data/databases/terminusdb/test-insert.json", "acumen-fap/data/databases/vector/CLAUDE.md", "acumen-fap/data/databases/vector/README.md", "acumen-fap/data/exports/CLAUDE.md", "acumen-fap/data/exports/README.md", "acumen-fap/data/exports/analytics/CLAUDE.md", "acumen-fap/data/exports/api-feeds/CLAUDE.md", "acumen-fap/data/exports/reports/CLAUDE.md", "acumen-fap/data/imports/CLAUDE.md", "acumen-fap/data/imports/README.md", "acumen-fap/data/imports/clients/CLAUDE.md", "acumen-fap/data/imports/corporate/CLAUDE.md", "acumen-fap/data/imports/documents/CLAUDE.md", "acumen-fap/packages/CLAUDE.md", "acumen-fap/packages/fap-canvas/CLAUDE.md", "acumen-fap/packages/fap-canvas/chatGPT/CLAUDE.md", "acumen-fap/packages/fap-canvas/chatGPT/fap-canvas-core.js", "acumen-fap/packages/fap-canvas/chatGPT/fap_infinite_canvas_guide.md", "acumen-fap/packages/fap-chat/CHANGELOG.md", "acumen-fap/packages/fap-chat/CLAUDE.md", "acumen-fap/packages/fap-chat/README.md", "acumen-fap/packages/fap-chat/css/CLAUDE.md", "acumen-fap/packages/fap-chat/css/fap-chat.css", "acumen-fap/packages/fap-chat/css/fap-message.css", "acumen-fap/packages/fap-chat/js/CLAUDE.md", "acumen-fap/packages/fap-chat/js/fap-chat-api.js", "acumen-fap/packages/fap-chat/js/fap-chat-config.js", "acumen-fap/packages/fap-chat/js/fap-chat-display.js", "acumen-fap/packages/fap-chat/js/fap-chat-events.js", "acumen-fap/packages/fap-chat/js/fap-chat-input.js", "acumen-fap/packages/fap-chat/js/fap-chat-plugins.js", "acumen-fap/packages/fap-chat/js/fap-chat-state.js", "acumen-fap/packages/fap-chat/js/fap-chat-storage.js", "acumen-fap/packages/fap-chat/package.json", "acumen-fap/packages/fap-core/CHANGELOG.md", "acumen-fap/packages/fap-core/CLAUDE.md", "acumen-fap/packages/fap-core/README.md", "acumen-fap/packages/fap-core/css/CLAUDE.md", "acumen-fap/packages/fap-core/css/fap-theme.css", "acumen-fap/packages/fap-core/css/fap-utils.css", "acumen-fap/packages/fap-core/package.json", "acumen-fap/packages/fap-toast/CLAUDE.md", "acumen-fap/packages/fap-tooltip/CLAUDE.md", "acumen-fap/packages/fap-tooltip/fap-tooltip.css", "acumen-fap/packages/fap-tooltip/fap-tooltip.js", "acumen-fap/packages/fap-tooltip/package.json", "acumen-fap/tools/CLAUDE.md", "acumen-fap/tools/dev-server/CLAUDE.md", "acumen-fap/tools/dev-server/package.json", "acumen-fap/tools/repo_management/CLAUDE.md", "acumen-fap/tools/repo_management/architecture/CLAUDE.md", "acumen-fap/tools/repo_management/conventions/CLAUDE.md", "acumen-fap/tools/repo_management/conventions/access-control-strategy.md", "acumen-fap/tools/repo_management/conventions/company-wide-monorepo-vision.md", "acumen-fap/tools/repo_management/conventions/database-strategy.md", "acumen-fap/tools/repo_management/conventions/graph-database-evaluation.md", "acumen-fap/tools/repo_management/conventions/naming-conventions.md", "docs-new/CLAUDE.md", "docs-new/MIGRATION_STATUS.md", "docs-new/P2P/CLAUDE.md", "docs-new/P2P/Pear/CLAUDE.md", "docs-new/P2P/Pear/README.md", "docs-new/P2P/Pear/pear-api-methods.md", "docs-new/anthropic/CLAUDE.md", "docs-new/anthropic/cc-sdk.md", "docs-new/anthropic/hookd-reference.md", "docs-new/anthropic/hooks.md", "docs-new/anthropic/subAgents.md", "docs-new/content/00-index/Business Map.md", "docs-new/content/00-index/CLAUDE.md", "docs-new/content/00-index/Corporate Map.md", "docs-new/content/00-index/Documentation Hub.md", "docs-new/content/00-index/Project Overview.md", "docs-new/content/00-index/Technical Map.md", "docs-new/content/01-getting-started/CLAUDE.md", "docs-new/content/01-getting-started/Installation.md", "docs-new/content/01-getting-started/Logseq Setup.md", "docs-new/content/01-getting-started/pnpm Workspaces Guide.md", "docs-new/content/03-fap-platform/CLAUDE.md", "docs-new/content/03-fap-platform/Platform Overview.md", "docs-new/content/05-technical/CLAUDE.md", "docs-new/content/05-technical/architecture/CLAUDE.md", "docs-new/content/05-technical/architecture/Data System.md", "docs-new/content/05-technical/conventions/CLAUDE.md", "docs-new/content/05-technical/conventions/Database Strategy.md", "docs-new/content/05-technical/conventions/Naming Conventions.md", "docs-new/content/05-technical/development/CLAUDE.md", "docs-new/content/05-technical/development/GitHub Packages Setup.md", "docs-new/content/05-technical/development/Redis Setup and Management.md", "docs-new/content/08-project-management/CLAUDE.md", "docs-new/content/08-project-management/Current Session Summary.md", "docs-new/content/08-project-management/External Test Instructions.md", "docs-new/content/08-project-management/Future Development Roadmap.md", "docs-new/content/08-project-management/Monorepo Restructure Plan.md", "docs-new/content/08-project-management/Session Instructions.md", "docs-new/content/CLAUDE.md", "docs-new/fap_guides/CLAUDE.md", "docs-new/fap_guides/fap-html-template.html", "docs-new/fap_guides/fap_best_practices.md", "docs-new/fap_guides/fap_css_guide.md", "docs-new/fap_guides/fap_infinite_canvas_guide.md", "docs-new/logseq/CLAUDE.md", "docs-new/logseq/config.edn", "docs-new/logseq/custom.css", "docs-new/plans/CLAUDE.md", "docs-new/plans/file tree management/CLAUDE.md", "docs-new/plans/file tree management/FINAL-IMPLEMENTATION-SUMMARY.md", "docs-new/plans/file tree management/README.md", "docs-new/plans/file tree management/claude-plan-file-tree-testing-results.md", "docs-new/plans/file tree management/claude-plan-live-integration-results.md", "docs-new/plans/file tree management/claude-plan-safe-deployment-procedure.md", "docs-new/plans/file tree management/claude-plan-safe-file-tree-testing.md", "docs-new/plans/memory-management/CLAUDE.md", "docs-new/plans/memory-management/claude-md-hierarchy-optimization.md", "docs-new/plans/memory-management/claude_memory_management.md", "docs-new/plans/session-branch-commits/CLAUDE.md", "docs-new/plans/session-branch-commits/ClaudePlan-2025-07-29:11-20-session-branch-workflow.md", "docs-new/templates/CLAUDE.md", "docs-new/templates/business-process.md", "docs-new/templates/component.md", "docs-new/templates/journal.md", "docs-new/templates/page.md", "docs-new/templates/technical-spec.md", "docs/CLAUDE.md", "docs/EXTERNAL-TEST-SUMMARY.md", "docs/MONOREPO_RESTRUCTURE_PLAN.md", "docs/NEXT_SESSION_INSTRUCTIONS.md", "docs/business/CLAUDE.md", "docs/business/policies/CLAUDE.md", "docs/business/processes/CLAUDE.md", "docs/fap/CLAUDE.md", "docs/fap/api/CLAUDE.md", "docs/fap/architecture/CLAUDE.md", "docs/fap/tutorials/CLAUDE.md", "docs/instructions.md", "docs/landmax/CLAUDE.md", "docs/landmax/setup/CLAUDE.md", "docs/landmax/user-guides/CLAUDE.md", "docs/open-source/CLAUDE.md", "docs/open-source/community/CLAUDE.md", "docs/open-source/contributing/CLAUDE.md", "docs/technical/CLAUDE.md", "docs/technical/README.md", "docs/technical/architecture/CLAUDE.md", "docs/technical/architecture/data-system.md", "docs/technical/development/CLAUDE.md", "docs/technical/github-packages-setup.md", "docs/technical/github-pages-setup.md", "docs/technical/pnpm-workspaces-for-dummies.md", "docs/technical/publishing-strategies.md", "docs/technical/secure-credential-setup.md", "docs/technical/third-party/CLAUDE.md", "docs/technical/third-party/README.md", "docs/technical/third-party/terminusdb/CLAUDE.md", "docs/technical/third-party/terminusdb/README.md", "docs/technical/third-party/terminusdb/build-native.sh", "docs/technical/third-party/terminusdb/installation.md", "migration-analysis.json", "package.json", "pnpm-lock.yaml", "pnpm-workspace.yaml", "scripts/CLAUDE.md", "scripts/claude-file-trees/.folder-ignore", "scripts/claude-file-trees/CLAUDE.md", "scripts/claude-file-trees/README.md", "scripts/claude-file-trees/generate_claude_file_trees.cjs", "scripts/claude-file-trees/master-file-list.json", "scripts/content-processor.js", "scripts/migrate-all.js", "scripts/migrate-docs.js", "swansburg-family/CLAUDE.md", "test.db"]}