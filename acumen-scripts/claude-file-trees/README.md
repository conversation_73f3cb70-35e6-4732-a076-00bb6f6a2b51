# CLAUDE.md File Tree Management

This directory contains tools for maintaining file tree sections in CLAUDE.md files throughout the monorepo, providing immediate spatial context for AI navigation.

## Quick Start

```bash
# Generate file trees for entire repository
node scripts/claude-file-trees/generate_claude_file_trees.cjs

# Preview changes without making them
node scripts/claude-file-trees/generate_claude_file_trees.cjs --dry-run

# Target specific directory
node scripts/claude-file-trees/generate_claude_file_trees.cjs ./acumen-fap
```

## What It Does

The script adds `## Local File Tree` sections to CLAUDE.md files showing immediate directory contents:

```markdown
## Local File Tree

```
acumen-fap/packages/
├── fap-canvas/
├── fap-chat/
├── fap-core/
├── fap-toast/
├── fap-tooltip/
```
```

## Files

- **`generate_claude_file_trees.cjs`** - Main script for generating/updating file trees
- **`folder-ignore`** - Configuration file listing directories to exclude
- **`README-file-trees.md`** - This documentation

## Configuration

Edit `.folder-ignore` to customize which directories are excluded:

```
# Archive and experimental directories
_archive
_clones
_temp
_research

# System directories
.git
node_modules
dist
build
```

## Maintenance Schedule

**Recommended**: Run monthly or after significant directory structure changes.

```bash
# Add to your monthly maintenance routine
node scripts/claude-file-trees/generate_claude_file_trees.cjs
git add -A
git commit -m "Update CLAUDE.md file trees"
```

## Features

- ✅ **Safe**: Preserves all existing CLAUDE.md content
- ✅ **Smart filtering**: Ignores unimportant directories automatically  
- ✅ **Configurable**: Easy to customize ignore patterns
- ✅ **Fast**: Processes entire monorepo in seconds
- ✅ **Dry-run mode**: Preview changes before applying

## Impact

- **131 CLAUDE.md files** maintained across important directories
- **Immediate context** for AI navigation (no more ls/glob tool usage)
- **Reduced errors** from missing or incorrect directory assumptions
- **Focused scope** by filtering out archived and temporary directories

For detailed implementation information, see `docs-new/plans/file tree management/FINAL-IMPLEMENTATION-SUMMARY.md`