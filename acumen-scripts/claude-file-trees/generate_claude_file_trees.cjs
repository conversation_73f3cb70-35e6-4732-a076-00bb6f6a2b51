/**
 * CLAUDE.md File Tree Generator
 *
 * Generates and maintains file tree sections in CLAUDE.md files throughout the monorepo.
 * Provides immediate spatial context for AI navigation by showing directory contents
 * directly within each CLAUDE.md file.
 *
 * Features:
 * - Updates existing CLAUDE.md files with ## Local File Tree sections
 * - Creates CLAUDE.md files where missing (up to 5 levels deep)
 * - Uses configurable folder-ignore patterns to focus on important directories
 * - Preserves all existing CLAUDE.md content
 * - Generates JSON master file list as backup for AI file search
 *
 * Usage:
 *   node scripts/claude-file-trees/generate_claude_file_trees.cjs              # Generate for entire repo
 *   node scripts/claude-file-trees/generate_claude_file_trees.cjs --dry-run    # Preview changes
 *   node scripts/claude-file-trees/generate_claude_file_trees.cjs ./acumen-fap # Target specific directory
 *
 * Configuration:
 *   Edit scripts/claude-file-trees/.folder-ignore to customize which directories to exclude
 *
 * Output:
 *   - CLAUDE.md files with embedded file trees
 *   - scripts/claude-file-trees/master-file-list.json: Comprehensive searchable file index for AI tools
 *
 * <AUTHOR> <PERSON> AI Assistant
 * @version 1.1
 * @date 2025-07-29
 */

const fs = require("fs");
const path = require("path");

const args = process.argv.slice(2);
const DRY_RUN = args.includes("--dry-run");
const ROOT_DIR = args.find((arg) => !arg.startsWith("--")) || ".";
const MAX_DEPTH = 5;
const IGNORE_FILE = path.join(__dirname, ".folder-ignore");

/**
 * Load ignore patterns from .folder-ignore file
 */
function loadIgnorePatterns() {
  const defaultPatterns = [
    "node_modules",
    ".git",
    "dist",
    "build",
    ".DS_Store",
    "Thumbs.db",
  ];

  try {
    if (fs.existsSync(IGNORE_FILE)) {
      const content = fs.readFileSync(IGNORE_FILE, "utf8");
      const filePatterns = content
        .split("\n")
        .map((line) => line.trim())
        .filter((line) => line && !line.startsWith("#"));

      return [...new Set([...defaultPatterns, ...filePatterns])];
    }
  } catch (error) {
    console.warn(`Warning: Could not read ${IGNORE_FILE}: ${error.message}`);
  }

  return defaultPatterns;
}

const IGNORE_PATTERNS = loadIgnorePatterns();

// Global file list for JSON master file generation (files only)
let masterFileList = [];

/**
 * Check if directory should be ignored
 */
function shouldIgnore(name) {
  return IGNORE_PATTERNS.some(
    (pattern) => name === pattern || name.startsWith(pattern + ".")
  );
}

/**
 * Get immediate children of a directory (files and folders)
 */
function getDirectoryChildren(dirPath) {
  try {
    return fs
      .readdirSync(dirPath, { withFileTypes: true })
      .filter((entry) => !shouldIgnore(entry.name))
      .map((entry) => ({
        name: entry.name,
        isDirectory: entry.isDirectory(),
        fullPath: path.join(dirPath, entry.name),
      }))
      .sort((a, b) => {
        // Directories first, then files, both alphabetically
        if (a.isDirectory && !b.isDirectory) return -1;
        if (!a.isDirectory && b.isDirectory) return 1;
        return a.name.localeCompare(b.name);
      });
  } catch (error) {
    console.warn(`Warning: Cannot read directory ${dirPath}: ${error.message}`);
    return [];
  }
}

/**
 * Collect file paths for master file list (files only, optimized)
 */
function collectFileInfo(dirPath) {
  const children = getDirectoryChildren(dirPath);
  
  // Add only files (not directories) to the master list
  children
    .filter(child => !child.isDirectory)
    .forEach(child => {
      const filePath = path.relative(ROOT_DIR, child.fullPath);
      masterFileList.push(filePath);
    });
}

/**
 * Generate optimized master file list JSON
 */
function generateMasterFileListJson() {
  const uniqueFiles = [...new Set(masterFileList)].sort();
  
  const metadata = {
    generated: new Date().toISOString(),
    total: uniqueFiles.length,
    maxDepth: MAX_DEPTH
  };
  
  return {
    metadata,
    files: uniqueFiles
  };
}

/**
 * Save master file list JSON
 */
function saveMasterFileList() {
  const jsonData = generateMasterFileListJson();
  const outputPath = path.join(__dirname, "master-file-list.json");
  
  if (DRY_RUN) {
    console.log(`WOULD CREATE: ${outputPath}`);
    console.log(`Total files: ${jsonData.files.length}`);
    console.log(`Sample files:`);
    jsonData.files.slice(0, 5).forEach(filePath => {
      console.log(`  ${filePath}`);
    });
  } else {
    fs.writeFileSync(outputPath, JSON.stringify(jsonData, null, 2));
    console.log(`Created master file list: ${outputPath}`);
    console.log(`Total files: ${jsonData.files.length}`);
  }
}

/**
 * Generate tree markdown for a directory
 */
function generateTreeMarkdown(dirPath) {
  const children = getDirectoryChildren(dirPath);
  const relativePath = path.relative(ROOT_DIR, dirPath) || "./";

  const lines = [
    "## Local File Tree",
    "",
    "```",
    relativePath + (relativePath === "./" ? "" : "/"),
    ...children.map(
      (child) => `├── ${child.name}${child.isDirectory ? "/" : ""}`
    ),
    "```",
    "",
  ];

  return lines.join("\n");
}

/**
 * Update or create CLAUDE.md file with file tree section
 */
function updateClaudeMdFile(dirPath) {
  const claudeMdPath = path.join(dirPath, "CLAUDE.md");
  const treeMarkdown = generateTreeMarkdown(dirPath);

  let content = "";
  let hasExistingFile = false;

  // Read existing file if it exists
  if (fs.existsSync(claudeMdPath)) {
    hasExistingFile = true;
    content = fs.readFileSync(claudeMdPath, "utf8");

    // Remove existing Local File Tree section
    content = content
      .replace(/## Local File Tree\n[\s\S]*?(?=\n## |\n# |$)/g, "")
      .trim();
  } else {
    // Create basic content for new file
    const dirName = path.basename(dirPath);
    content = `# ${dirName}\n\nLocal directory information and context.`;
  }

  // Add file tree section at the end
  const updatedContent = content + "\n\n" + treeMarkdown;

  if (DRY_RUN) {
    console.log(`${hasExistingFile ? "UPDATE" : "CREATE"}: ${claudeMdPath}`);
    console.log("Tree content:");
    console.log(treeMarkdown);
    console.log("---");
  } else {
    fs.writeFileSync(claudeMdPath, updatedContent);
    console.log(`${hasExistingFile ? "Updated" : "Created"}: ${claudeMdPath}`);
  }
}

/**
 * Recursively process directories up to MAX_DEPTH
 */
function processDirectory(dirPath, depth = 0) {
  if (depth >= MAX_DEPTH) return;

  // Skip if directory should be ignored
  if (shouldIgnore(path.basename(dirPath))) return;

  // Collect file information for master list
  collectFileInfo(dirPath);

  // Update CLAUDE.md for current directory
  updateClaudeMdFile(dirPath);

  // Process subdirectories
  const children = getDirectoryChildren(dirPath);
  children
    .filter((child) => child.isDirectory)
    .forEach((child) => {
      processDirectory(child.fullPath, depth + 1);
    });
}

// Main execution
console.log(
  `${DRY_RUN ? "DRY RUN: " : ""}Starting CLAUDE.md file tree integration...`
);
console.log(`Root directory: ${ROOT_DIR}`);
console.log(`Max depth: ${MAX_DEPTH}`);
console.log(`Ignore patterns: ${IGNORE_PATTERNS.join(", ")}`);
console.log("---");

try {
  processDirectory(path.resolve(ROOT_DIR));
  console.log("---");
  
  // Generate master file list JSON
  saveMasterFileList();
  
  console.log("---");
  console.log(`${DRY_RUN ? "DRY RUN " : ""}Integration complete!`);
} catch (error) {
  console.error("Error during processing:", error.message);
  process.exit(1);
}
