# Development - "How do I contribute/develop?"

**Everything you need to contribute to the FAP monorepo**

This section contains all the information for developers who want to contribute to the project, whether you're adding features, fixing bugs, or improving documentation.

## 🚀 **Getting Started as a Contributor**

### Prerequisites
- Complete [Setup](../setup/README.md) first
- Read [FAP Platform Overview](../fap/README.md)
- Understand the [project philosophy](../fap/architecture/semantic-elements.md)

### First Contribution
1. **Pick an area**: Components, documentation, tools, or integrations
2. **Follow conventions**: See [conventions/](conventions/) for standards
3. **Use the tools**: Leverage [tools/](tools/) for development
4. **Publish properly**: Follow [publishing/](publishing/) guidelines

## 📁 **Development Documentation**

### [conventions/](conventions/) - "What are the standards?"
Coding and documentation standards:
- [Naming Conventions](conventions/naming-conventions.md) - File, variable, and component naming
- [File Organization](conventions/file-organization.md) - Where to put different types of files
- [Coding Standards](conventions/coding-standards.md) - JavaScript, CSS, and HTML standards
- [Documentation Standards](conventions/documentation-standards.md) - How to write good docs

### [tools/](tools/) - "What tools are available?"
Development tools and utilities:
- [pnpm Workspaces](tools/pnpm-workspaces.md) - Monorepo management
- [Testing](tools/testing.md) - Testing strategies and tools
- [Debugging](tools/debugging.md) - Debugging techniques and tools
- [Development Server](tools/development-server.md) - Local development setup

### [publishing/](publishing/) - "How do I publish/release?"
Package publishing and release management:
- [GitHub Packages](publishing/github-packages.md) - Publishing to GitHub Packages
- [Versioning](publishing/versioning.md) - Semantic versioning and changesets
- [Release Process](publishing/release-process.md) - Step-by-step release guide

## 🛠️ **Development Workflow**

### Daily Development
```bash
# 1. Start development server
pnpm --filter component-name dev

# 2. Make changes
# Edit files following conventions

# 3. Test changes
pnpm test

# 4. Validate documentation
node docs/_internal/tools/validate-docs.js
```

### Adding New Components
```bash
# 1. Create component structure
mkdir -p fap/packages/fap-new-component/{css,js,examples}

# 2. Follow naming conventions
# See conventions/naming-conventions.md

# 3. Document the component
# Use templates in docs/_internal/templates/

# 4. Add to component index
# Update fap/components/README.md
```

### Contributing Documentation
```bash
# 1. Determine correct location
# Use the question-based structure

# 2. Follow documentation standards
# See conventions/documentation-standards.md

# 3. Use templates
# Available in docs/_internal/templates/

# 4. Validate and cross-reference
# Ensure links work and content is discoverable
```

## 📋 **Development Standards**

### Code Quality
- **Pure Vanilla**: No frameworks, TypeScript, or build tools
- **Semantic HTML**: Use custom elements for structure
- **Alphabetical CSS**: Organize by element families
- **Clean JavaScript**: Follow vanilla JS patterns

### Documentation Quality
- **Clear Structure**: Use question-based organization
- **Cross-References**: Link to related documentation
- **Examples**: Include practical examples
- **Maintenance**: Keep documentation current

### Testing Standards
- **Component Tests**: Test all public APIs
- **Integration Tests**: Test component interactions
- **Documentation Tests**: Validate links and examples
- **Performance Tests**: Ensure zero framework overhead

## 🔧 **Development Tools**

### Available Tools
- **Documentation Validator**: `docs/_internal/tools/validate-docs.js`
- **Migration Tools**: `docs/_internal/tools/migrate-docs.js`
- **Development Server**: `@acumen-desktop/dev-server`
- **Testing Framework**: Built-in vanilla JS testing

### IDE Setup
- **VS Code**: Recommended with extensions
- **Settings**: Consistent formatting and linting
- **Snippets**: FAP-specific code snippets
- **Debugging**: Browser and Node.js debugging

## 🎯 **Contribution Areas**

### Components
- **New Components**: Build reusable FAP components
- **Component Improvements**: Enhance existing components
- **API Design**: Improve component APIs
- **Performance**: Optimize component performance

### Documentation
- **Content**: Write guides, tutorials, and references
- **Structure**: Improve documentation organization
- **Examples**: Add practical examples and demos
- **Maintenance**: Keep documentation current

### Tools
- **Development Tools**: Improve developer experience
- **Build Tools**: Enhance build and publishing
- **Testing Tools**: Better testing infrastructure
- **Documentation Tools**: Automated documentation

### Integrations
- **Third-Party**: Integrate external services
- **APIs**: Build API integrations
- **Deployment**: Improve deployment processes
- **Monitoring**: Add monitoring and analytics

## 🤝 **Getting Help**

### Resources
- **Troubleshooting**: [Common Issues](../reference/troubleshooting.md)
- **API Reference**: [Complete API Docs](../reference/api-reference.md)
- **Architecture**: [How FAP Works](../fap/architecture/README.md)
- **Examples**: [Component Examples](../fap/components/README.md)

### Community
- **Issues**: Report bugs and request features
- **Discussions**: Ask questions and share ideas
- **Pull Requests**: Contribute code and documentation
- **Reviews**: Help review contributions

---

**Ready to contribute?** Start with [conventions](conventions/README.md) and pick an area that interests you!
