# FAP Platform - "Where's the FAP platform documentation?"

**Complete documentation for the Freedom Application Platform**

The Freedom Application Platform (FAP) is a revolutionary approach to web development that proves modern applications can be built with pure vanilla HTML, CSS, and JavaScript while maintaining professional development practices.

## 🎯 **What is FAP?**

FAP demonstrates that web development can be:
- **Simple** - No frameworks, no build steps, no complexity
- **Semantic** - Self-documenting HTML with custom elements
- **Maintainable** - Clear separation of concerns and predictable structure
- **Performant** - Zero framework overhead, pure vanilla technologies

## 📚 **Documentation Sections**

### [components/](components/) - "Where are component docs?"
Individual component documentation:
- [FAP Core](components/fap-core.md) - Theme system and utilities
- [FAP Chat](components/fap-chat.md) - Interactive chat terminal
- [FAP Tooltip](components/fap-tooltip.md) - Flexible tooltip system
- [Component Index](components/README.md) - All components overview

### [guides/](guides/) - "How do I build with FAP?"
Step-by-step guides for building with FAP:
- [Creating Components](guides/creating-components.md) - Build new FAP components
- [Building Applications](guides/building-applications.md) - Combine components into apps
- [Styling Guide](guides/styling-guide.md) - FAP CSS conventions and patterns
- [Publishing Packages](guides/publishing-packages.md) - Share your components

### [architecture/](architecture/) - "How does FAP work?"
Deep dive into FAP's design and implementation:
- [Semantic Elements](architecture/semantic-elements.md) - Custom element philosophy
- [CSS Organization](architecture/css-organization.md) - Alphabetical CSS structure
- [Vanilla JS Patterns](architecture/vanilla-js-patterns.md) - JavaScript without frameworks
- [Global Namespace](architecture/global-namespace.md) - `window.fap.*` conventions

### [api/](api/) - "What methods can I call?"
Complete API reference for all FAP components:
- [Core API](api/core-api.md) - Theme system and utility methods
- [Chat API](api/chat-api.md) - Chat component methods and events
- [Tooltip API](api/tooltip-api.md) - Tooltip system API
- [Complete API Reference](api/README.md) - All APIs in one place

## 🚀 **Quick Start with FAP**

### 1. **Understand the Philosophy**
```html
<!-- ❌ Traditional approach -->
<div class="chat-terminal">
  <div class="chat-display">...</div>
  <div class="chat-input">...</div>
</div>

<!-- ✅ FAP approach - self-documenting -->
<chat-terminal>
  <chat-display>...</chat-display>
  <chat-input>...</chat-input>
</chat-terminal>
```

### 2. **Learn the Patterns**
- **Semantic Custom Elements**: Use `<chat-terminal>` instead of `<div class="chat-terminal">`
- **Alphabetical CSS**: Element names determine file organization
- **Classes for State Only**: `.hidden`, `.loading`, `.error` (not structural styling)
- **Global Namespace**: All JavaScript APIs under `window.fap.*`

### 3. **Start Building**
```javascript
// FAP components expose clean APIs
window.fap.chat.api.addMessage({
  type: 'info', 
  content: 'Hello FAP!'
});

window.fap.tooltip.show(element, 'Helpful tooltip');
```

## 🏗️ **FAP Architecture**

### Core Principles
1. **Plain Vanilla Only**: Pure HTML, CSS, and JavaScript
2. **Semantic Structure**: Custom elements for self-documenting markup
3. **Alphabetical Organization**: Predictable file and CSS organization
4. **Zero Dependencies**: No frameworks, no build tools, no complexity

### Component Structure
```
fap-component/
├── css/
│   ├── fap-component.css      # Main component styles
│   └── component-*.css        # Element family styles
├── js/
│   ├── fap-component-api.js   # Public API
│   ├── fap-component-core.js  # Core functionality
│   └── fap-component-*.js     # Feature modules
├── examples/
│   └── demo.html              # Interactive demo
└── README.md                  # Component documentation
```

## 📦 **Published Packages**

All FAP components are published to GitHub Packages:
- **[@acumen-desktop/core](https://github.com/Acumen-Desktop?tab=packages)** - Theme system and utilities
- **[@acumen-desktop/chat](https://github.com/Acumen-Desktop?tab=packages)** - Chat terminal component
- **[@acumen-desktop/tooltip](https://github.com/Acumen-Desktop?tab=packages)** - Tooltip component

## 🎓 **Learning Path**

### For New FAP Users
1. **Start**: [FAP Philosophy](architecture/semantic-elements.md)
2. **Learn**: [Creating Components](guides/creating-components.md)
3. **Practice**: [Building Applications](guides/building-applications.md)
4. **Reference**: [API Documentation](api/README.md)

### For Experienced Developers
1. **Architecture**: [Vanilla JS Patterns](architecture/vanilla-js-patterns.md)
2. **Advanced**: [CSS Organization](architecture/css-organization.md)
3. **Integration**: [Global Namespace](architecture/global-namespace.md)
4. **Publishing**: [Publishing Packages](guides/publishing-packages.md)

## 🤝 **Contributing to FAP**

- **New Components**: Follow [Creating Components](guides/creating-components.md)
- **Documentation**: See [Documentation Standards](../development/conventions/documentation-standards.md)
- **Bug Reports**: Use [Troubleshooting Guide](../reference/troubleshooting.md)
- **Feature Requests**: Check [Planning](../planning/roadmap.md)

---

**Ready to build?** Start with [Creating Components](guides/creating-components.md) or explore [existing components](components/README.md).
