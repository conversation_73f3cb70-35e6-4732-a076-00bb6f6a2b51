# FAP Monorepo Documentation

**Clear, intuitive documentation for the Freedom Application Platform**

Welcome! This documentation is organized to answer the questions you actually have. Each directory name tells you exactly what's inside.

## 🚀 **Quick Start**

**New to the project?** → [Setup Guide](setup/installation.md)  
**Looking for FAP docs?** → [FAP Platform](fap/README.md)  
**Need to integrate something?** → [Integrations](integrations/README.md)  
**Want to contribute?** → [Development Guide](development/README.md)

## 📁 **What's Where**

### [setup/](setup/) - "How do I get started?"
Everything you need to get up and running:
- [Installation](setup/installation.md) - Install dependencies and tools
- [Development Environment](setup/development-environment.md) - Configure your workspace
- [First Time Setup](setup/first-time-setup.md) - Complete setup checklist
- [Logseq Setup](setup/logseq-setup.md) - Optional enhanced documentation experience

### [fap/](fap/) - "Where's the FAP platform documentation?"
Complete FAP platform documentation:
- [Platform Overview](fap/README.md) - What is FAP and why use it?
- [components/](fap/components/) - Individual component documentation
- [guides/](fap/guides/) - How to build with FAP
- [architecture/](fap/architecture/) - How FAP works internally
- [api/](fap/api/) - Method and API reference

### [landmax/](landmax/) - "Where's LandMax documentation?"
LandMax project-specific documentation:
- [Business Overview](landmax/README.md) - What is LandMax?
- [user-guides/](landmax/user-guides/) - How to use LandMax
- [setup/](landmax/setup/) - LandMax installation and configuration

### [corporate/](corporate/) - "Where's business information?"
Acumen Desktop Software Canada Inc. business documentation:
- [Corporate Overview](corporate/README.md) - Company structure and overview
- [legal/](corporate/legal/) - Legal documents, compliance, contracts
- [processes/](corporate/processes/) - Business processes and workflows
- [finance/](corporate/finance/) - Financial information and procedures

### [integrations/](integrations/) - "How do I integrate X?"
Third-party integrations and external services:
- [Integration Overview](integrations/README.md) - Available integrations
- [terminusdb/](integrations/terminusdb/) - TerminusDB database integration
- [github-packages/](integrations/github-packages/) - GitHub Packages publishing
- [deployment/](integrations/deployment/) - Deployment and hosting

### [development/](development/) - "How do I contribute/develop?"
Everything for contributors and developers:
- [Development Guide](development/README.md) - How to contribute
- [conventions/](development/conventions/) - Coding and documentation standards
- [tools/](development/tools/) - Development tools and utilities
- [publishing/](development/publishing/) - Package publishing and releases

### [reference/](reference/) - "Where can I look things up?"
Quick reference and lookup information:
- [API Reference](reference/api-reference.md) - Complete API documentation
- [Component Index](reference/component-index.md) - All components at a glance
- [Troubleshooting](reference/troubleshooting.md) - Common issues and solutions
- [Glossary](reference/glossary.md) - Terms and definitions

### [planning/](planning/) - "What's the roadmap/status?"
Project planning and coordination:
- [Planning Overview](planning/README.md) - Current plans and status
- [Roadmap](planning/roadmap.md) - Future development plans
- [session-notes/](planning/session-notes/) - Development session notes
- [architecture-decisions/](planning/architecture-decisions/) - Design decisions

### [_internal/](_internal/) - "System files (ignore unless maintaining docs)"
Documentation system internals:
- [ai-context/](_internal/ai-context/) - AI assistant context and metadata
- [templates/](_internal/templates/) - Documentation templates
- [tools/](_internal/tools/) - Documentation maintenance tools

## 🧭 **Finding Information**

### By Question Type
- **"How do I...?"** → [setup/](setup/), [fap/guides/](fap/guides/), [development/](development/)
- **"What is...?"** → [fap/README.md](fap/README.md), [reference/glossary.md](reference/glossary.md)
- **"Where is...?"** → [reference/component-index.md](reference/component-index.md)
- **"Why does...?"** → [fap/architecture/](fap/architecture/), [planning/architecture-decisions/](planning/architecture-decisions/)

### By Role
- **New Developer**: [setup/](setup/) → [development/](development/) → [fap/guides/](fap/guides/)
- **FAP User**: [fap/README.md](fap/README.md) → [fap/components/](fap/components/) → [fap/api/](fap/api/)
- **Business User**: [corporate/README.md](corporate/README.md) → [landmax/README.md](landmax/README.md)
- **Integrator**: [integrations/README.md](integrations/README.md) → specific integration docs

### By Content Type
- **Tutorials**: [setup/](setup/), [fap/guides/](fap/guides/)
- **Reference**: [fap/api/](fap/api/), [reference/](reference/)
- **Explanations**: [fap/architecture/](fap/architecture/), [planning/architecture-decisions/](planning/architecture-decisions/)
- **How-to Guides**: [development/](development/), [integrations/](integrations/)

## 📝 **Contributing to Documentation**

### Where to Put New Documentation
- **FAP component docs** → `fap/components/component-name.md`
- **How-to guides** → `fap/guides/` or `development/`
- **Integration docs** → `integrations/service-name/`
- **Business processes** → `corporate/processes/`
- **API documentation** → `fap/api/` or `reference/api-reference.md`

### Documentation Standards
See [Documentation Standards](development/conventions/documentation-standards.md) for:
- File naming conventions
- Content structure guidelines
- Cross-reference standards
- Template usage

## 🔧 **Maintenance**

This documentation structure is designed to be self-maintaining:
- **Clear categories** reduce misplaced content
- **Question-based organization** makes placement obvious
- **Consistent patterns** across all sections
- **Automated tools** in `_internal/tools/` for validation

---

**Questions?** Check the [reference section](reference/) or [create an issue](https://github.com/your-repo/issues).
