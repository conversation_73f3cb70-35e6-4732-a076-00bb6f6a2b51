#!/usr/bin/env node

/**
 * Migration Tool: Current Structure → Intuitive Structure
 * 
 * Migrates documentation from the current numbered hierarchy
 * to the new question-based intuitive structure
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.resolve(__dirname, '../../..');

class IntuitiveStructureMigrator {
  constructor() {
    this.migrationMap = new Map();
    this.migrated = [];
    this.skipped = [];
    this.errors = [];
    
    this.setupMigrationMap();
  }

  /**
   * Setup migration mapping from current to intuitive structure
   */
  setupMigrationMap() {
    // Current docs/ structure → Intuitive structure
    this.migrationMap.set('docs/README.md', 'docs-intuitive/README.md');
    
    // Setup/Getting Started
    this.migrationMap.set('docs/content/01-getting-started/Installation.md', 'docs-intuitive/setup/installation.md');
    this.migrationMap.set('docs/content/01-getting-started/Logseq Setup.md', 'docs-intuitive/setup/logseq-setup.md');
    this.migrationMap.set('docs/technical/pnpm-workspaces-for-dummies.md', 'docs-intuitive/setup/development-environment.md');
    
    // FAP Platform
    this.migrationMap.set('docs/content/03-fap-platform/Platform Overview.md', 'docs-intuitive/fap/README.md');
    this.migrationMap.set('acumen-fap/README.md', 'docs-intuitive/fap/README.md');
    this.migrationMap.set('docs/content/03-fap-platform/components/', 'docs-intuitive/fap/components/');
    
    // Corporate/Business
    this.migrationMap.set('docs/content/02-corporate/', 'docs-intuitive/corporate/');
    this.migrationMap.set('docs/business/', 'docs-intuitive/corporate/processes/');
    
    // LandMax
    this.migrationMap.set('docs/content/04-landmax/', 'docs-intuitive/landmax/');
    this.migrationMap.set('docs/landmax/', 'docs-intuitive/landmax/');
    
    // Technical → Development
    this.migrationMap.set('docs/content/05-technical/conventions/', 'docs-intuitive/development/conventions/');
    this.migrationMap.set('docs/content/05-technical/development/', 'docs-intuitive/development/tools/');
    this.migrationMap.set('docs/technical/github-packages-setup.md', 'docs-intuitive/development/publishing/github-packages.md');
    this.migrationMap.set('docs/technical/publishing-strategies.md', 'docs-intuitive/development/publishing/versioning.md');
    
    // Integrations
    this.migrationMap.set('docs/content/06-integrations/', 'docs-intuitive/integrations/');
    this.migrationMap.set('docs/content/05-technical/third-party/', 'docs-intuitive/integrations/');
    this.migrationMap.set('docs/technical/third-party/', 'docs-intuitive/integrations/');
    
    // Planning/Project Management
    this.migrationMap.set('docs/content/08-project-mgmt/', 'docs-intuitive/planning/');
    this.migrationMap.set('docs/MONOREPO_RESTRUCTURE_PLAN.md', 'docs-intuitive/planning/architecture-decisions/monorepo-restructure.md');
    this.migrationMap.set('docs/NEXT_SESSION_INSTRUCTIONS.md', 'docs-intuitive/planning/session-notes/current-session.md');
    
    // AI Context → Internal
    this.migrationMap.set('docs/ai-context/', 'docs-intuitive/_internal/ai-context/');
    this.migrationMap.set('docs/.logseq/templates/', 'docs-intuitive/_internal/templates/');
    this.migrationMap.set('docs/tools/', 'docs-intuitive/_internal/tools/');
    
    // docs-new content
    this.migrationMap.set('docs-new/content/', 'docs-intuitive/');
    this.migrationMap.set('docs-new/templates/', 'docs-intuitive/_internal/templates/');
    this.migrationMap.set('docs-new/logseq/', 'docs-intuitive/_internal/logseq/');
  }

  /**
   * Execute the migration
   */
  async migrate() {
    console.log('🚀 Starting migration to intuitive structure...\n');
    
    try {
      await this.createBackup();
      await this.executeMigration();
      this.generateReport();
      
      console.log('\n✅ Migration to intuitive structure complete!');
      return true;
      
    } catch (error) {
      console.error('❌ Migration failed:', error.message);
      return false;
    }
  }

  /**
   * Create backup of existing documentation
   */
  async createBackup() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupDir = path.resolve(rootDir, `_backup/docs-before-intuitive-${timestamp}`);
    
    console.log(`📦 Creating backup at ${backupDir}...`);
    
    try {
      await fs.mkdir(backupDir, { recursive: true });
      
      // Backup existing docs
      const docsDir = path.resolve(rootDir, 'docs');
      try {
        await fs.access(docsDir);
        await this.copyDirectory(docsDir, path.join(backupDir, 'docs'));
      } catch {
        console.log('No existing docs/ directory to backup');
      }
      
      // Backup docs-new
      const docsNewDir = path.resolve(rootDir, 'docs-new');
      try {
        await fs.access(docsNewDir);
        await this.copyDirectory(docsNewDir, path.join(backupDir, 'docs-new'));
      } catch {
        console.log('No docs-new/ directory to backup');
      }
      
      console.log('✅ Backup created successfully\n');
      
    } catch (error) {
      console.warn('⚠️ Backup creation failed:', error.message);
    }
  }

  /**
   * Execute the migration mapping
   */
  async executeMigration() {
    console.log('📁 Executing migration mapping...');
    
    for (const [source, target] of this.migrationMap) {
      try {
        await this.migrateItem(source, target);
      } catch (error) {
        this.errors.push({
          source,
          target,
          error: error.message
        });
      }
    }
    
    console.log(`Processed ${this.migrationMap.size} migration mappings`);
  }

  /**
   * Migrate a single item (file or directory)
   */
  async migrateItem(source, target) {
    const sourcePath = path.resolve(rootDir, source);
    const targetPath = path.resolve(rootDir, target);
    
    try {
      // Check if source exists
      await fs.access(sourcePath);
      
      // Check if it's a file or directory
      const stats = await fs.stat(sourcePath);
      
      if (stats.isDirectory()) {
        await this.migrateDirectory(sourcePath, targetPath, source, target);
      } else {
        await this.migrateFile(sourcePath, targetPath, source, target);
      }
      
    } catch (error) {
      if (error.code === 'ENOENT') {
        this.skipped.push({
          source,
          target,
          reason: 'source does not exist'
        });
      } else {
        throw error;
      }
    }
  }

  /**
   * Migrate a single file
   */
  async migrateFile(sourcePath, targetPath, source, target) {
    // Ensure target directory exists
    const targetDir = path.dirname(targetPath);
    await fs.mkdir(targetDir, { recursive: true });
    
    // Check if target already exists
    try {
      await fs.access(targetPath);
      this.skipped.push({
        source,
        target,
        reason: 'target already exists'
      });
      return;
    } catch {
      // Target doesn't exist, proceed
    }
    
    // Copy file
    const content = await fs.readFile(sourcePath, 'utf-8');
    await fs.writeFile(targetPath, content);
    
    this.migrated.push({ source, target, type: 'file' });
    console.log(`✅ Migrated file: ${source} → ${target}`);
  }

  /**
   * Migrate a directory
   */
  async migrateDirectory(sourcePath, targetPath, source, target) {
    // Ensure target directory exists
    await fs.mkdir(targetPath, { recursive: true });
    
    // Copy all files in directory
    const entries = await fs.readdir(sourcePath, { withFileTypes: true });
    
    for (const entry of entries) {
      const entrySourcePath = path.join(sourcePath, entry.name);
      const entryTargetPath = path.join(targetPath, entry.name);
      const entrySource = path.join(source, entry.name);
      const entryTarget = path.join(target, entry.name);
      
      if (entry.isDirectory()) {
        await this.migrateDirectory(entrySourcePath, entryTargetPath, entrySource, entryTarget);
      } else {
        await this.migrateFile(entrySourcePath, entryTargetPath, entrySource, entryTarget);
      }
    }
    
    this.migrated.push({ source, target, type: 'directory' });
    console.log(`✅ Migrated directory: ${source} → ${target}`);
  }

  /**
   * Copy directory recursively
   */
  async copyDirectory(src, dest) {
    await fs.mkdir(dest, { recursive: true });
    const entries = await fs.readdir(src, { withFileTypes: true });
    
    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);
      
      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }

  /**
   * Generate migration report
   */
  generateReport() {
    console.log('\n📊 Migration Report');
    console.log('==================');
    console.log(`Items migrated: ${this.migrated.length}`);
    console.log(`Items skipped: ${this.skipped.length}`);
    console.log(`Errors: ${this.errors.length}`);
    
    if (this.migrated.length > 0) {
      console.log('\n✅ Successfully Migrated:');
      this.migrated.forEach(item => {
        console.log(`  ${item.source} → ${item.target} (${item.type})`);
      });
    }
    
    if (this.skipped.length > 0) {
      console.log('\n⏭️ Skipped Items:');
      this.skipped.forEach(item => {
        console.log(`  ${item.source} (${item.reason})`);
      });
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ Errors:');
      this.errors.forEach(error => {
        console.log(`  ${error.source} → ${error.target}: ${error.error}`);
      });
    }
  }
}

// Run migration if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  const migrator = new IntuitiveStructureMigrator();
  migrator.migrate()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Migration failed:', error.message);
      process.exit(1);
    });
}

export { IntuitiveStructureMigrator };
