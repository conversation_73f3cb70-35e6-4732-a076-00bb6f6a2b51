# Setup - "How do I get started?"

**Everything you need to get the FAP monorepo running on your machine**

This section contains all the setup and installation documentation. If you're new to the project, start here.

## 📋 **Setup Checklist**

### 1. [Installation](installation.md)
Install all required dependencies and tools:
- Node.js 18+
- pnpm 8+
- Git
- Optional: Logseq for enhanced documentation

### 2. [Development Environment](development-environment.md)
Configure your development workspace:
- IDE setup and extensions
- Environment variables
- Local configuration

### 3. [First Time Setup](first-time-setup.md)
Complete setup verification:
- Clone and install dependencies
- Run tests to verify setup
- Build and run demo applications

### 4. [Logseq Setup](logseq-setup.md) *(Optional)*
Enhanced documentation experience:
- Install and configure Logseq
- Import documentation graph
- Use advanced features like graph view

## 🚀 **Quick Start**

```bash
# 1. Clone the repository
git clone <repository-url>
cd monorepo_july_2025

# 2. Install dependencies
pnpm install

# 3. Verify setup
pnpm test

# 4. Run demo
pnpm --filter fap-chat dev
```

## 🔧 **System Requirements**

### Required
- **Node.js**: 18.0.0 or higher
- **pnpm**: 8.0.0 or higher
- **Git**: Any recent version
- **Operating System**: macOS, Linux, or Windows

### Recommended
- **RAM**: 8GB or more
- **Storage**: 2GB free space
- **IDE**: VS Code with recommended extensions

## 🆘 **Troubleshooting Setup**

### Common Issues
- **pnpm not found**: Install pnpm globally with `npm install -g pnpm`
- **Node version too old**: Use nvm to install Node.js 18+
- **Permission errors**: Check file permissions and user access

### Getting Help
- Check [Troubleshooting Guide](../reference/troubleshooting.md)
- Review [Development Tools](../development/tools/README.md)
- Create an issue if problems persist

## ➡️ **What's Next?**

After completing setup:
1. **Learn FAP**: Read [FAP Platform Overview](../fap/README.md)
2. **Start Developing**: Check [Development Guide](../development/README.md)
3. **Build Something**: Follow [Creating Components](../fap/guides/creating-components.md)

---

**Need help?** All setup documentation is designed to be self-contained and step-by-step.
