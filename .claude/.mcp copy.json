{"mcpServers": {"github": {"serverUrl": "https://api.githubcopilot.com/mcp/", "headers": {"Authorization": "Bearer YOUR_GITHUB_PAT"}}, "git": {"command": "uvx", "args": ["mcp-server-git", "--repository", "/Volumes/Data/Software/2025/Acumen_Desktop/monorepo_july_2025"]}, "filesystem": {"command": "uvx", "args": ["mcp-server-filesystem", "/Volumes/Data/Software/2025/Acumen_Desktop/monorepo_july_2025"]}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"]}}}