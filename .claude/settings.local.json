{"permissions": {"allow": ["WebFetch(domain:docs.pears.com)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(claude mcp:*)", "WebFetch(domain:docs.anthropic.com)", "mcp__Ref__ref_search_documentation", "mcp__Ref__ref_read_url", "Bash(find:*)", "Bash(tree:*)", "mcp__playwright__browser_snapshot", "<PERSON><PERSON>(mv:*)", "Bash(echo $PATH)", "Bash(ls:*)", "Bash(npx:*)", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp list)", "<PERSON><PERSON>(alias claude=\"/Users/<USER>/.claude/local/node_modules/.bin/claude\")", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp add --user filesystem npx @modelcontextprotocol/server-filesystem /Volumes/Data/Software/2025/Acumen_Desktop)", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp --help)", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp add --help)", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp add --scope user filesystem npx @modelcontextprotocol/server-filesystem /Volumes/Data/Software/2025/Acumen_Desktop)", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp add --scope user git npx @modelcontextprotocol/server-git)", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp remove github-server)", "Bash(rm:*)", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp remove git)", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp add --scope user git npx @modelcontextprotocol/server-git /Volumes/Data/Software/2025/Acumen_Desktop/monorepo_july_2025)", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp get Ref)", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp add --scope user git uvx mcp-server-git)", "WebFetch(domain:ref.tools)", "mcp__playwright__browser_navigate", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp remove Ref)", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp add --scope user Ref npx mcp-remote@0.1.0-0 https://api.ref.tools/mcp --header x-ref-api-key:ref-aae813addce496eb4935)", "Bash(brew:*)", "<PERSON>sh(redis-server:*)", "Bash(redis-cli:*)", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp add --scope user sqlite uvx mcp-server-sqlite --db-path ~/test.db)", "Bash(/Users/<USER>/.claude/local/node_modules/.bin/claude mcp add-json --scope user sqlite '{\"\"command\"\": \"\"uvx\"\", \"\"args\"\": [\"\"mcp-server-sqlite\"\", \"\"--db-path\"\", \"\"/Volumes/Data/Software/2025/Acumen_Desktop/monorepo_july_2025/test.db\"\"]}')", "<PERSON><PERSON>(env)", "mcp__filesystem__directory_tree", "mcp__filesystem__list_directory", "mcp__filesystem__read_file", "mcp__filesystem__read_multiple_files", "mcp__filesystem__create_directory", "mcp__filesystem__write_file", "Bash(node:*)", "mcp__filesystem__move_file", "mcp__git__git_status", "<PERSON><PERSON>(true)", "Bash(cp:*)", "mcp__git__git_branch", "mcp__filesystem__edit_file", "mcp__filesystem__search_files"], "deny": []}, "enableAllProjectMcpServers": false}