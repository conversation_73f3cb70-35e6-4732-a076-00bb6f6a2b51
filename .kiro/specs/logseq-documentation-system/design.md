# Design Document - Logseq Documentation System

## Overview

This design document outlines the architecture and implementation approach for reorganizing the FAP monorepo documentation system using Logseq as the primary knowledge management platform. The system will consolidate scattered documentation, eliminate duplicates, and create a unified knowledge graph optimized for human navigation, AI processing, and Logseq's block-based architecture.

The design leverages Logseq's strengths in bi-directional linking, graph visualization, and block-based content organization while maintaining compatibility with traditional markdown workflows and AI/MCP server integration.

## Architecture

### High-Level System Architecture

```mermaid
graph TB
    subgraph "Documentation Sources"
        A[Root README.md]
        B[docs/ folder]
        C[docs-old/ folder]
        D[Package READMEs]
        E[Convention docs]
        F[Planning docs]
    end
    
    subgraph "Logseq Knowledge Base"
        G[Consolidated docs/]
        H[Logseq Config]
        I[Custom Plugins]
        J[Templates]
    end
    
    subgraph "Access Interfaces"
        K[Logseq Desktop]
        L[Logseq Web]
        M[Traditional Markdown]
        N[AI/MCP Servers]
    end
    
    A --> G
    B --> G
    C --> G
    D --> G
    E --> G
    F --> G
    
    G --> K
    G --> L
    G --> M
    G --> N
    
    H --> K
    H --> L
    I --> K
    I --> L
    J --> G
```

### Logseq-Optimized Directory Structure

```
docs/                                    # Single source of truth
├── logseq/                             # Logseq configuration
│   ├── config.edn                     # Logseq settings
│   ├── custom.css                     # FAP-themed styling
│   ├── plugins/                       # Custom plugins
│   │   ├── fap-monorepo/              # Monorepo-specific plugin
│   │   └── terminus-sync/             # TerminusDB integration
│   └── templates/                     # Block templates
│       ├── component.md               # Component documentation template
│       ├── business-process.md        # Business process template
│       └── technical-spec.md          # Technical specification template
├── pages/                             # Logseq pages (auto-generated)
├── journals/                          # Daily development logs
├── assets/                            # Documentation assets
│   ├── images/                        # Screenshots, diagrams
│   ├── graphs/                        # Exported graph views
│   └── exports/                       # Generated documentation
└── content/                           # Organized content hierarchy
    ├── 00-index/                      # Navigation and overview
    │   ├── Documentation Hub.md       # Main entry point
    │   ├── Corporate Map.md           # Corporate structure overview
    │   ├── Technical Map.md           # Technical architecture overview
    │   └── Business Map.md            # Business process overview
    ├── 01-getting-started/            # Quick start guides
    │   ├── Installation.md
    │   ├── Development Setup.md
    │   ├── Logseq Setup.md
    │   └── pnpm Workspaces Guide.md
    ├── 02-corporate/                  # Corporate documentation
    │   ├── Entity Structure.md        # Acumen Desktop Software Canada Inc.
    │   ├── Directors and Officers.md  # Leadership information
    │   ├── Legal and Compliance.md    # Regulatory requirements
    │   └── Business Relationships.md  # Client and partner relationships
    ├── 03-fap-platform/              # FAP platform documentation
    │   ├── Platform Overview.md       # FAP philosophy and architecture
    │   ├── components/                # Component documentation
    │   │   ├── FAP Core.md           # Theme system and utilities
    │   │   ├── FAP Chat.md           # Chat component
    │   │   ├── FAP Tooltip.md        # Tooltip component
    │   │   └── Component Index.md     # Component relationships
    │   ├── architecture/              # Platform architecture
    │   │   ├── Semantic Elements.md   # Custom element philosophy
    │   │   ├── CSS Organization.md    # Styling conventions
    │   │   ├── JavaScript Patterns.md # Vanilla JS patterns
    │   │   └── Package Structure.md   # Monorepo organization
    │   └── tutorials/                 # Getting started guides
    │       ├── Creating Components.md
    │       ├── Building Applications.md
    │       └── Publishing Packages.md
    ├── 04-landmax/                   # LandMax business documentation
    │   ├── Business Overview.md       # LandMax as FAP customer
    │   ├── setup/                     # Setup and configuration
    │   └── user-guides/               # User documentation
    ├── 05-technical/                 # Technical documentation
    │   ├── architecture/              # System architecture
    │   │   ├── Data System.md        # Database and storage strategy
    │   │   ├── Integration Points.md  # System integrations
    │   │   └── Security Model.md     # Security and access control
    │   ├── development/               # Development guides
    │   │   ├── GitHub Packages.md    # Package publishing
    │   │   ├── GitHub Pages.md       # Documentation hosting
    │   │   ├── Publishing Strategies.md # Release management
    │   │   └── Credential Management.md # Security setup
    │   ├── third-party/              # Third-party integrations
    │   │   ├── TerminusDB/           # Graph database
    │   │   │   ├── Overview.md       # Why TerminusDB
    │   │   │   ├── Installation.md   # Setup instructions
    │   │   │   ├── Configuration.md  # Configuration guide
    │   │   │   └── Integration.md    # API and MCP integration
    │   │   └── Third Party Index.md  # Integration overview
    │   └── conventions/               # Development conventions
    │       ├── Naming Conventions.md # File and code naming
    │       ├── Database Strategy.md  # Data management approach
    │       ├── Access Control.md     # Security and permissions
    │       ├── Monorepo Vision.md    # Overall strategy
    │       └── Graph Database Evaluation.md # Technology decisions
    ├── 06-business/                  # Business documentation
    │   ├── processes/                # Business processes
    │   └── policies/                 # Company policies
    ├── 07-open-source/              # Open source project documentation
    │   ├── contributing/             # Contribution guidelines
    │   └── community/                # Community guidelines
    └── 08-project-management/        # Project and session management
        ├── Monorepo Restructure Plan.md # Overall restructuring strategy
        ├── Session Instructions.md   # Current session notes
        ├── External Test Instructions.md # Testing guidelines
        └── Project Timeline.md       # Development timeline
```

## Components and Interfaces

### Logseq Configuration System

**config.edn Configuration:**
```clojure
{:meta/version 1
 
 ;; Preferred workflow
 :preferred-workflow :now
 
 ;; Graph settings optimized for monorepo
 :graph/settings
 {:journal? true
  :project? true
  :workflow :now}
 
 ;; Custom CSS for FAP branding
 :custom-css-url "logseq/custom.css"
 
 ;; Plugin configuration
 :plugins
 [{:id "fap-monorepo"
   :name "FAP Monorepo Integration"
   :version "1.0.0"}
  {:id "terminus-sync"
   :name "TerminusDB Synchronization"
   :version "1.0.0"}]
 
 ;; Default templates
 :default-templates
 {:journals "journals/template.md"
  :pages "templates/page.md"}
 
 ;; Preferred format
 :preferred-format :markdown
 
 ;; Feature flags
 :feature/enable-block-timestamps? true
 :feature/enable-search-remove-accents? true
 :feature/enable-journals? true}
```

**Custom CSS (logseq/custom.css):**
```css
/* FAP-themed Logseq styling */
:root {
  --fap-primary: #0066cc;
  --fap-secondary: #404040;
  --fap-background: #1a1a1a;
  --fap-text: #e0e0e0;
  --fap-accent: #00cc66;
}

/* Graph view customization */
.graph-view {
  background: var(--fap-background);
}

.graph-view .node {
  fill: var(--fap-primary);
}

.graph-view .link {
  stroke: var(--fap-secondary);
}

/* Block styling for better readability */
.block-content {
  font-family: 'SF Pro Text', -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
}

/* Tag styling */
.tag {
  background: var(--fap-primary);
  color: white;
  border-radius: 4px;
  padding: 2px 6px;
  font-size: 0.85em;
}

/* Custom element highlighting */
.block-content code {
  background: var(--fap-secondary);
  color: var(--fap-accent);
  padding: 2px 4px;
  border-radius: 3px;
}
```

### Block Template System

**Component Template (templates/component.md):**
```markdown
- # {{component-name}} #fap/components #status/{{status}}
  template:: component
  created:: {{today}}
  - ## Overview
    - Brief description of the component
  - ## Dependencies
    - [[FAP Core]] - Base theme system
    - Add other dependencies as links
  - ## Used By
    - List applications and components that use this
  - ## API Reference
    - Key methods and properties
  - ## Related Documentation
    - [[Requirements]] - Functional requirements
    - [[Design]] - Technical design
    - [[Implementation]] - Code location
  - ## Status
    - Current status: {{status}}
    - Last updated: {{today}}
```

**Business Process Template (templates/business-process.md):**
```markdown
- # {{process-name}} #business/processes #priority/{{priority}}
  template:: business-process
  created:: {{today}}
  - ## Purpose
    - Why this process exists
  - ## Stakeholders
    - Who is involved in this process
  - ## Steps
    - Detailed process steps
  - ## Related Systems
    - Technical systems that support this process
  - ## Compliance Requirements
    - Regulatory or legal requirements
  - ## Related Documentation
    - Links to related processes and technical docs
```

### AI and MCP Integration Layer

**Structured Frontmatter Schema:**
```yaml
---
title: "Document Title"
type: "component|business|corporate|technical"
category: "specific-category"
tags: 
  - "primary-tag"
  - "secondary-tag"
status: "draft|review|stable|deprecated"
audience: 
  - "developers"
  - "business-users"
  - "ai-agents"
created: "2025-01-24"
updated: "2025-01-24"
version: "1.0.0"
related:
  - "[[Related Document 1]]"
  - "[[Related Document 2]]"
dependencies:
  - "[[Dependency 1]]"
  - "[[Dependency 2]]"
---
```

**MCP Server Interface:**
```javascript
// Logseq MCP Server Integration
class LogseqMCPServer {
  constructor(docsPath) {
    this.docsPath = docsPath;
    this.logseqAPI = new LogseqAPI();
  }
  
  async queryDocumentation(query) {
    // Search across all documentation using Logseq's query engine
    return await this.logseqAPI.datascript_query(`
      [:find ?title ?content ?tags
       :where
       [?b :block/content ?content]
       [?b :block/page ?p]
       [?p :block/name ?title]
       [(clojure.string/includes? ?content "${query}")]]
    `);
  }
  
  async getRelatedContent(pageTitle) {
    // Get bi-directionally linked content
    return await this.logseqAPI.get_page_linked_references(pageTitle);
  }
  
  async createDocumentation(template, data) {
    // Create new documentation from templates
    const content = this.processTemplate(template, data);
    return await this.logseqAPI.create_page(data.title, content);
  }
  
  async updateKnowledgeGraph() {
    // Export current graph state for AI processing
    return await this.logseqAPI.export_graph();
  }
}
```

## Data Models

### Documentation Metadata Model

```typescript
interface DocumentMetadata {
  title: string;
  type: 'component' | 'business' | 'corporate' | 'technical';
  category: string;
  tags: string[];
  status: 'draft' | 'review' | 'stable' | 'deprecated';
  audience: ('developers' | 'business-users' | 'ai-agents')[];
  created: Date;
  updated: Date;
  version: string;
  related: string[];
  dependencies: string[];
  location: string; // File path
  logseqBlocks?: LogseqBlock[];
}

interface LogseqBlock {
  id: string;
  content: string;
  properties: Record<string, any>;
  children: LogseqBlock[];
  refs: string[];
}
```

### Knowledge Graph Model

```typescript
interface KnowledgeNode {
  id: string;
  title: string;
  type: string;
  tags: string[];
  connections: Connection[];
  metadata: DocumentMetadata;
}

interface Connection {
  target: string;
  type: 'reference' | 'dependency' | 'related' | 'parent' | 'child';
  strength: number; // Connection strength for graph algorithms
}

interface KnowledgeGraph {
  nodes: KnowledgeNode[];
  connections: Connection[];
  metadata: {
    generated: Date;
    version: string;
    totalNodes: number;
    totalConnections: number;
  };
}
```

## Error Handling

### Migration Error Handling

```javascript
class DocumentationMigration {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.migrated = [];
  }
  
  async migrateFile(sourcePath, targetPath) {
    try {
      // Validate source file
      if (!await this.validateSourceFile(sourcePath)) {
        throw new Error(`Invalid source file: ${sourcePath}`);
      }
      
      // Process content
      const content = await this.processContent(sourcePath);
      
      // Update links
      const updatedContent = await this.updateLinks(content);
      
      // Write to target
      await this.writeFile(targetPath, updatedContent);
      
      this.migrated.push({ source: sourcePath, target: targetPath });
      
    } catch (error) {
      this.errors.push({
        file: sourcePath,
        error: error.message,
        timestamp: new Date()
      });
    }
  }
  
  async validateMigration() {
    // Validate all links are working
    // Check for orphaned files
    // Verify Logseq can parse all files
    return {
      success: this.errors.length === 0,
      errors: this.errors,
      warnings: this.warnings,
      migrated: this.migrated
    };
  }
}
```

### Logseq Integration Error Handling

```javascript
class LogseqIntegration {
  async initializeLogseq(docsPath) {
    try {
      // Validate Logseq installation
      await this.validateLogseqInstallation();
      
      // Setup configuration
      await this.setupConfiguration(docsPath);
      
      // Install plugins
      await this.installPlugins();
      
      // Validate graph integrity
      await this.validateGraph();
      
    } catch (error) {
      throw new LogseqIntegrationError(
        `Failed to initialize Logseq: ${error.message}`,
        error
      );
    }
  }
  
  async handleGraphCorruption() {
    // Backup current state
    await this.backupGraph();
    
    // Rebuild from source files
    await this.rebuildGraph();
    
    // Validate integrity
    await this.validateGraph();
  }
}
```

## Testing Strategy

### Migration Testing

```javascript
describe('Documentation Migration', () => {
  test('should migrate all files without data loss', async () => {
    const migration = new DocumentationMigration();
    
    // Get list of all current markdown files
    const sourceFiles = await migration.getAllMarkdownFiles();
    
    // Perform migration
    await migration.migrateAll(sourceFiles);
    
    // Validate results
    const result = await migration.validateMigration();
    
    expect(result.success).toBe(true);
    expect(result.errors).toHaveLength(0);
    expect(result.migrated.length).toBe(sourceFiles.length);
  });
  
  test('should preserve all internal links', async () => {
    const linkValidator = new LinkValidator();
    
    // Extract all links from migrated files
    const links = await linkValidator.extractAllLinks();
    
    // Validate each link
    const brokenLinks = await linkValidator.validateLinks(links);
    
    expect(brokenLinks).toHaveLength(0);
  });
});
```

### Logseq Integration Testing

```javascript
describe('Logseq Integration', () => {
  test('should successfully parse all migrated files', async () => {
    const logseq = new LogseqIntegration();
    
    // Initialize Logseq with migrated docs
    await logseq.initializeLogseq('./docs');
    
    // Parse all files
    const parseResults = await logseq.parseAllFiles();
    
    // Check for parsing errors
    const errors = parseResults.filter(r => r.error);
    expect(errors).toHaveLength(0);
  });
  
  test('should create proper knowledge graph', async () => {
    const logseq = new LogseqIntegration();
    
    // Generate knowledge graph
    const graph = await logseq.generateKnowledgeGraph();
    
    // Validate graph structure
    expect(graph.nodes.length).toBeGreaterThan(0);
    expect(graph.connections.length).toBeGreaterThan(0);
    
    // Validate key nodes exist
    const corporateNode = graph.nodes.find(n => n.title.includes('Acumen Desktop'));
    expect(corporateNode).toBeDefined();
    
    const fapNode = graph.nodes.find(n => n.title.includes('FAP Platform'));
    expect(fapNode).toBeDefined();
  });
});
```

### AI Integration Testing

```javascript
describe('AI Integration', () => {
  test('should provide structured responses to MCP queries', async () => {
    const mcpServer = new LogseqMCPServer('./docs');
    
    // Test various query types
    const componentQuery = await mcpServer.queryDocumentation('FAP Chat');
    expect(componentQuery.length).toBeGreaterThan(0);
    
    const corporateQuery = await mcpServer.queryDocumentation('Acumen Desktop');
    expect(corporateQuery.length).toBeGreaterThan(0);
    
    // Test relationship queries
    const related = await mcpServer.getRelatedContent('FAP Chat');
    expect(related.length).toBeGreaterThan(0);
  });
});
```

## Performance Considerations

### Logseq Performance Optimization

1. **Graph Size Management**
   - Implement pagination for large graphs
   - Use lazy loading for block content
   - Optimize query performance with proper indexing

2. **File Organization**
   - Keep individual files under 10MB for optimal Logseq performance
   - Use proper block hierarchy to avoid deep nesting
   - Implement content splitting for very large documents

3. **Search Performance**
   - Configure Logseq search indexing for optimal performance
   - Use tags strategically to improve search speed
   - Implement caching for frequently accessed content

### AI Processing Optimization

1. **Metadata Caching**
   - Cache parsed frontmatter for faster AI queries
   - Implement incremental updates for changed files
   - Use efficient data structures for relationship mapping

2. **Query Optimization**
   - Implement query result caching
   - Use database indexing for common query patterns
   - Optimize graph traversal algorithms

## Security Considerations

### Access Control

1. **Sensitive Information Handling**
   - Clearly separate public and private documentation
   - Implement access controls for corporate information
   - Use proper tagging to identify sensitive content

2. **AI Access Restrictions**
   - Limit AI access to appropriate content levels
   - Implement audit logging for AI queries
   - Use role-based access for different AI agents

### Data Protection

1. **Backup and Recovery**
   - Implement automated backups of the knowledge base
   - Provide rollback capabilities for content changes
   - Maintain version history for critical documents

2. **Integrity Validation**
   - Implement checksums for critical files
   - Validate graph integrity regularly
   - Monitor for unauthorized changes

This design provides a comprehensive foundation for implementing the Logseq-based documentation system while maintaining compatibility with existing workflows and enabling enhanced AI integration.