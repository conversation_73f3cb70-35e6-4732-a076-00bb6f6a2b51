<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAP Search Engine - Demo</title>
    <link rel="stylesheet" href="../fap-block-core/fap-block-core.css">
    <link rel="stylesheet" href="fap-search-engine.css">
    <style>
        /* CSS Variables for main colors */
        :root {
            --background: black;
            --text: white;
            --header-bg: darkgray;
            --main-bg: lightgray;
            --footer-bg: darkgray;
            --working-color: #22c55e;
            --placeholder-color: #ef4444;
        }

        /* CSS Reset */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html, body {
            background-color: black;
            bottom: 0;
            left: 0;
            overflow: hidden;
            position: absolute;
            right: 0;
            top: 0;
        }

        body {
            color: white;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 16px;
            line-height: 1.5;
        }

        header {
            align-items: center;
            background-color: darkgray;
            display: flex;
            height: 42px;
            justify-content: center;
            left: 1px;
            position: absolute;
            right: 1px;
            top: 1px;
        }

        main {
            background-color: lightgray;
            bottom: 44px;
            color: black;
            left: 1px;
            overflow: auto;
            position: absolute;
            right: 1px;
            top: 44px;
            padding: 20px;
        }

        footer {
            align-items: center;
            background-color: darkgray;
            bottom: 1px;
            display: flex;
            height: 42px;
            justify-content: center;
            left: 1px;
            position: absolute;
            right: 1px;
        }

        /* Demo layout */
        fap-demo-layout {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            height: 100%;
            max-width: 1200px;
            margin: 0 auto;
        }

        fap-demo-section {
            display: block;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 20px;
            overflow: hidden;
        }

        fap-demo-section h3 {
            margin: 0 0 15px 0;
            color: #374151;
            font-size: 18px;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 8px;
        }

        /* Instructions */
        fap-instructions {
            display: block;
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        fap-instructions h4 {
            margin: 0 0 10px 0;
            color: #1e40af;
        }

        /* Status indicators */
        .fap-working {
            outline: 2px solid var(--working-color) !important;
            outline-offset: 2px;
            position: relative;
        }

        .fap-working::after {
            content: "✓ WORKING";
            position: absolute;
            top: -25px;
            right: 0;
            background: var(--working-color);
            color: white;
            padding: 2px 6px;
            font-size: 10px;
            border-radius: 3px;
            font-weight: bold;
        }

        /* Sample data display */
        fap-sample-data {
            display: block;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 10px;
            background: #f8fafc;
            font-size: 14px;
        }

        fap-sample-block {
            display: block;
            margin: 8px 0;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border-left: 3px solid #3b82f6;
        }

        fap-block-id {
            font-family: monospace;
            font-size: 11px;
            color: #6b7280;
            margin-bottom: 4px;
        }

        fap-block-content {
            color: #374151;
            line-height: 1.4;
        }

        /* Controls */
        fap-demo-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        fap-demo-button {
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #f9fafb;
            color: #374151;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.15s ease;
        }

        fap-demo-button:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }

        /* Responsive */
        @media (max-width: 768px) {
            fap-demo-layout {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <header>FAP Search Engine - Comprehensive Search Demo</header>
    
    <main>
        <fap-instructions class="fap-working">
            <h4>🔍 Search Engine Testing Instructions</h4>
            <p><strong>What to test:</strong></p>
            <ul>
                <li>• <strong>Real-time search</strong> - Type in the search box and see instant results</li>
                <li>• <strong>Fuzzy matching</strong> - Try typos and partial words</li>
                <li>• <strong>Relevance ranking</strong> - Results are sorted by relevance score</li>
                <li>• <strong>Highlighting</strong> - Search terms are highlighted in results</li>
                <li>• <strong>Search history</strong> - Previous searches are remembered</li>
                <li>• <strong>Performance</strong> - Check console for search timing</li>
            </ul>
            <p><strong>Try searching for:</strong> "welcome", "test", "block", "reference", "demo", or any partial words!</p>
        </fap-instructions>

        <fap-demo-layout>
            <fap-demo-section class="fap-working">
                <h3>🔍 Search Interface</h3>
                
                <fap-demo-controls>
                    <fap-demo-button onclick="loadSampleData()" class="fap-working">📚 Load Sample Data</fap-demo-button>
                    <fap-demo-button onclick="rebuildIndex()" class="fap-working">🔄 Rebuild Index</fap-demo-button>
                    <fap-demo-button onclick="clearSearch()" class="fap-working">🧹 Clear Search</fap-demo-button>
                    <fap-demo-button onclick="showStats()" class="fap-working">📊 Show Stats</fap-demo-button>
                    <fap-demo-button onclick="toggleDebug()" class="fap-working">🐛 Toggle Debug</fap-demo-button>
                </fap-demo-controls>

                <fap-search-container class="fap-working">
                    <fap-search-input-area>
                        <fap-search-input 
                            id="search-input" 
                            placeholder="Search blocks... (try 'welcome', 'test', 'block')"
                            autocomplete="off">
                        </fap-search-input>
                        <fap-search-controls>
                            <fap-search-button onclick="performSearch()">🔍 Search</fap-search-button>
                            <fap-search-button onclick="clearResults()">✕ Clear</fap-search-button>
                        </fap-search-controls>
                    </fap-search-input-area>
                    
                    <fap-search-stats id="search-stats" style="display: none;">
                        <fap-search-count id="result-count">0 results</fap-search-count>
                        <fap-search-timing id="search-timing">0ms</fap-search-timing>
                    </fap-search-stats>
                    
                    <fap-search-suggestions id="search-suggestions" style="display: none;">
                        <fap-suggestions-label>Suggestions:</fap-suggestions-label>
                        <fap-suggestion-list id="suggestion-list"></fap-suggestion-list>
                    </fap-search-suggestions>
                    
                    <fap-search-results id="search-results">
                        <fap-no-results>
                            <fap-no-results-icon>🔍</fap-no-results-icon>
                            <fap-no-results-message>Start typing to search through blocks</fap-no-results-message>
                            <fap-no-results-suggestion>Try searching for "welcome", "test", or "block"</fap-no-results-suggestion>
                        </fap-no-results>
                    </fap-search-results>
                    
                    <fap-search-history id="search-history" style="display: none;">
                        <fap-history-header>
                            <fap-history-title>Recent Searches</fap-history-title>
                            <fap-history-clear onclick="clearHistory()">Clear</fap-history-clear>
                        </fap-history-header>
                        <div id="history-list"></div>
                    </fap-search-history>
                </fap-search-container>
            </fap-demo-section>

            <fap-demo-section class="fap-working">
                <h3>📚 Sample Data & Results</h3>
                
                <p style="margin-bottom: 15px; color: #6b7280; font-size: 14px;">
                    Sample blocks loaded for testing. Search results will appear here with highlighting and relevance scores.
                </p>
                
                <fap-sample-data id="sample-data">
                    <p style="text-align: center; color: #9ca3af; padding: 20px;">
                        Click "Load Sample Data" to see searchable content
                    </p>
                </fap-sample-data>
            </fap-demo-section>
        </fap-demo-layout>
    </main>
    
    <footer>Search Engine: <span id="footer-stats">Ready</span> | Indexed: <span id="footer-indexed">0</span> blocks</footer>

    <!-- Load dependencies -->
    <script src="../fap-event-system/fap-event-system.js"></script>
    <script src="../fap-block-core/fap-block-core.js"></script>
    <script src="fap-search-engine.js"></script>

    <script>
        // Global variables
        let searchEngine;
        let debugMode = false;

        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            initializeDemo();
        });

        function initializeDemo() {
            console.log('🔍 FAP Search Engine Demo initializing...');
            
            // Initialize search engine
            searchEngine = new window.FAPSearchEngine.SearchEngine({
                debounceDelay: 300,
                maxResults: 20,
                fuzzyThreshold: 0.6,
                highlightClass: 'search-highlight'
            });

            console.log('✅ Search engine initialized');

            // Set up event listeners
            setupEventListeners();
            
            // Load sample data
            loadSampleData();

            console.log('✅ FAP Search Engine Demo ready!');
            updateFooterStats();
        }

        function setupEventListeners() {
            // Search input with real-time search
            const searchInput = document.getElementById('search-input');
            searchInput.addEventListener('input', function(e) {
                const query = e.target.value;
                if (query.length > 0) {
                    searchEngine.search(query);
                } else {
                    clearResults();
                }
            });

            // Enter key for immediate search
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    performSearch();
                }
            });

            // Listen for search results
            window.Events.on('search:results', handleSearchResults);
            window.Events.on('search:index-rebuilt', handleIndexRebuilt);

            console.log('✅ Event listeners setup complete');
        }

        function loadSampleData() {
            console.log('📚 Loading comprehensive search sample data...');
            const { Block, BlockStore } = window.FAPBlockCore;
            
            // Clear existing data
            BlockStore.getAllBlocks().forEach(block => {
                BlockStore.deleteBlock(block.id);
            });
            
            // Create comprehensive sample data for search testing
            const blocks = [
                new Block({
                    id: 'search-welcome',
                    content: '🔍 Welcome to FAP Search Engine! This demonstrates comprehensive full-text search capabilities.'
                }),
                new Block({
                    id: 'search-features',
                    content: '✨ Search Features Available:',
                }),
                new Block({
                    id: 'feature-fulltext',
                    content: '📝 Full-text search across all block content with fuzzy matching',
                    parent: 'search-features'
                }),
                new Block({
                    id: 'feature-realtime',
                    content: '⚡ Real-time search results as you type with debouncing',
                    parent: 'search-features'
                }),
                new Block({
                    id: 'feature-ranking',
                    content: '📊 Relevance ranking based on content matches and recency',
                    parent: 'search-features'
                }),
                new Block({
                    id: 'feature-highlighting',
                    content: '🎨 Search term highlighting in results for easy scanning',
                    parent: 'search-features'
                }),
                new Block({
                    id: 'feature-history',
                    content: '📚 Search history tracking with result counts',
                    parent: 'search-features'
                }),
                new Block({
                    id: 'test-content',
                    content: '🧪 Test Content for Search Validation:'
                }),
                new Block({
                    id: 'test-references',
                    content: 'This block contains [[Page References]] and ((block-references)) and #hashtags',
                    parent: 'test-content'
                }),
                new Block({
                    id: 'test-markdown',
                    content: 'Markdown content with **bold text**, *italic text*, and `code snippets`',
                    parent: 'test-content'
                }),
                new Block({
                    id: 'test-technical',
                    content: 'Technical terms: JavaScript, CSS, HTML, API, database, search engine, algorithm',
                    parent: 'test-content'
                }),
                new Block({
                    id: 'test-fuzzy',
                    content: 'Fuzzy matching test: definately, recieve, seperate, occured (intentional typos)',
                    parent: 'test-content'
                }),
                new Block({
                    id: 'demo-instructions',
                    content: '📋 Demo Instructions and Usage:'
                }),
                new Block({
                    id: 'inst-search',
                    content: 'Type in the search box above to see real-time results',
                    parent: 'demo-instructions'
                }),
                new Block({
                    id: 'inst-fuzzy',
                    content: 'Try partial words or typos to test fuzzy matching',
                    parent: 'demo-instructions'
                }),
                new Block({
                    id: 'inst-performance',
                    content: 'Check console for search performance metrics and debugging',
                    parent: 'demo-instructions'
                }),
                new Block({
                    id: 'performance-data',
                    content: '⚡ Performance testing with various content types and lengths to validate search speed'
                }),
                new Block({
                    id: 'long-content',
                    content: 'This is a longer block of content designed to test search performance and relevance ranking. It contains multiple sentences with various keywords that might be searched for. The search engine should be able to find relevant matches within this longer text and rank them appropriately based on the search algorithm. This helps validate that the search functionality works well with blocks of varying lengths and complexity.'
                })
            ];

            // Set up hierarchy
            const hierarchyMap = {
                'search-features': ['feature-fulltext', 'feature-realtime', 'feature-ranking', 'feature-highlighting', 'feature-history'],
                'test-content': ['test-references', 'test-markdown', 'test-technical', 'test-fuzzy'],
                'demo-instructions': ['inst-search', 'inst-fuzzy', 'inst-performance']
            };

            Object.entries(hierarchyMap).forEach(([parentId, childIds]) => {
                const parent = blocks.find(b => b.id === parentId);
                childIds.forEach(childId => {
                    const child = blocks.find(b => b.id === childId);
                    if (parent && child) {
                        parent.addChild(child);
                    }
                });
            });

            // Add to store
            blocks.forEach(block => BlockStore.addBlock(block));

            console.log(`✅ Sample data loaded: ${blocks.length} blocks for search testing`);
            displaySampleData(blocks);
            updateFooterStats();
        }

        function displaySampleData(blocks) {
            const sampleDataContainer = document.getElementById('sample-data');
            sampleDataContainer.innerHTML = '';
            
            blocks.forEach(block => {
                const blockElement = document.createElement('fap-sample-block');
                blockElement.innerHTML = `
                    <fap-block-id>${block.id}</fap-block-id>
                    <fap-block-content>${block.content}</fap-block-content>
                `;
                sampleDataContainer.appendChild(blockElement);
            });
        }

        function performSearch() {
            const query = document.getElementById('search-input').value;
            if (query.trim()) {
                console.log(`🔍 Performing immediate search: "${query}"`);
                searchEngine.searchImmediate(query);
            }
        }

        function clearSearch() {
            document.getElementById('search-input').value = '';
            clearResults();
            console.log('🧹 Search cleared');
        }

        function clearResults() {
            const resultsContainer = document.getElementById('search-results');
            resultsContainer.innerHTML = `
                <fap-no-results>
                    <fap-no-results-icon>🔍</fap-no-results-icon>
                    <fap-no-results-message>Start typing to search through blocks</fap-no-results-message>
                    <fap-no-results-suggestion>Try searching for "welcome", "test", or "block"</fap-no-results-suggestion>
                </fap-no-results>
            `;
            
            document.getElementById('search-stats').style.display = 'none';
            document.getElementById('search-suggestions').style.display = 'none';
        }

        function rebuildIndex() {
            console.log('🔄 Manual index rebuild requested...');
            const blockCount = searchEngine.forceRebuildIndex();
            console.log(`✅ Index rebuilt: ${blockCount} blocks indexed`);
            updateFooterStats();
            
            // Show user feedback in console instead of alert
            console.log(`🎉 Search index rebuilt successfully! Indexed ${blockCount} blocks.`);
        }

        function showStats() {
            const stats = searchEngine.getStats();
            console.log('📊 Search Engine Statistics:', stats);
            
            // Also check BlockStore directly
            if (window.FAPBlockCore) {
                const blockStoreCount = window.FAPBlockCore.BlockStore.getAllBlocks().length;
                console.log(`📚 BlockStore has ${blockStoreCount} blocks`);
                
                console.log(`📊 Search Engine Statistics:
                    Indexed Blocks: ${stats.indexedBlocks}
                    BlockStore Blocks: ${blockStoreCount}
                    Cache Size: ${stats.cacheSize}
                    History Size: ${stats.historySize}
                    Current Query: "${stats.currentQuery}"
                    Last Index Update: ${new Date(stats.lastIndexUpdate).toLocaleTimeString()}`);
            } else {
                console.log(`📊 Search Engine Statistics:
                    Indexed Blocks: ${stats.indexedBlocks}
                    Cache Size: ${stats.cacheSize}
                    History Size: ${stats.historySize}
                    Current Query: "${stats.currentQuery}"
                    Last Index Update: ${new Date(stats.lastIndexUpdate).toLocaleTimeString()}`);
            }
        }

        function toggleDebug() {
            debugMode = !debugMode;
            console.log(`🐛 Debug mode ${debugMode ? 'enabled' : 'disabled'}`);
            
            if (debugMode) {
                console.log('🔍 Search Engine Debug Info:', searchEngine.getStats());
            }
        }

        function clearHistory() {
            searchEngine.clearHistory();
            document.getElementById('search-history').style.display = 'none';
            console.log('🧹 Search history cleared');
        }

        function handleSearchResults(result) {
            console.log(`🔍 Search results received: ${result.totalResults} results in ${result.searchTime}ms`);
            
            if (debugMode) {
                console.log('🔍 Detailed search result:', result);
            }

            displaySearchResults(result);
            updateSearchStats(result);
            updateSearchSuggestions(result);
            updateSearchHistory();
            updateFooterStats();
        }

        function displaySearchResults(result) {
            const resultsContainer = document.getElementById('search-results');
            
            if (result.results.length === 0) {
                resultsContainer.innerHTML = `
                    <fap-no-results>
                        <fap-no-results-icon>😔</fap-no-results-icon>
                        <fap-no-results-message>No results found for "${result.query}"</fap-no-results-message>
                        <fap-no-results-suggestion>Try different keywords or check spelling</fap-no-results-suggestion>
                    </fap-no-results>
                `;
                return;
            }

            resultsContainer.innerHTML = '';
            
            result.results.forEach((searchResult, index) => {
                const resultElement = document.createElement('fap-search-result');
                resultElement.innerHTML = `
                    <fap-result-header>
                        <fap-result-id>${searchResult.blockId}</fap-result-id>
                        <fap-result-score>Score: ${searchResult.score.toFixed(1)}</fap-result-score>
                    </fap-result-header>
                    <fap-result-content>${searchResult.highlighted}</fap-result-content>
                    <fap-result-meta>
                        <fap-result-depth>📊 Depth: ${searchResult.block.depth}</fap-result-depth>
                        <fap-result-refs>🔗 Refs: ${searchResult.block.refs.length}</fap-result-refs>
                        <fap-result-updated>📅 ${new Date(searchResult.block.updated).toLocaleDateString()}</fap-result-updated>
                    </fap-result-meta>
                `;
                
                resultElement.addEventListener('click', () => {
                    console.log(`🎯 Selected search result: ${searchResult.blockId}`);
                    // Could emit event for outliner integration
                    window.Events.emit('search:result-selected', searchResult);
                });
                
                resultsContainer.appendChild(resultElement);
            });
        }

        function updateSearchStats(result) {
            const statsElement = document.getElementById('search-stats');
            const countElement = document.getElementById('result-count');
            const timingElement = document.getElementById('search-timing');
            
            countElement.textContent = `${result.totalResults} result${result.totalResults !== 1 ? 's' : ''}`;
            timingElement.textContent = `${result.searchTime}ms`;
            
            statsElement.style.display = 'flex';
        }

        function updateSearchSuggestions(result) {
            const suggestionsElement = document.getElementById('search-suggestions');
            const suggestionList = document.getElementById('suggestion-list');
            
            if (result.suggestions.length > 0) {
                suggestionList.innerHTML = '';
                result.suggestions.forEach(suggestion => {
                    const suggestionItem = document.createElement('fap-suggestion-item');
                    suggestionItem.textContent = suggestion;
                    suggestionItem.addEventListener('click', () => {
                        document.getElementById('search-input').value = suggestion;
                        searchEngine.searchImmediate(suggestion);
                    });
                    suggestionList.appendChild(suggestionItem);
                });
                suggestionsElement.style.display = 'block';
            } else {
                suggestionsElement.style.display = 'none';
            }
        }

        function updateSearchHistory() {
            const history = searchEngine.getHistory();
            if (history.length > 0) {
                const historyElement = document.getElementById('search-history');
                const historyList = document.getElementById('history-list');
                
                historyList.innerHTML = '';
                history.slice(0, 5).forEach(item => {
                    const historyItem = document.createElement('fap-history-item');
                    historyItem.innerHTML = `
                        <fap-history-query>${item.query}</fap-history-query>
                        <fap-history-meta>${item.resultCount} results</fap-history-meta>
                    `;
                    historyItem.addEventListener('click', () => {
                        document.getElementById('search-input').value = item.query;
                        searchEngine.searchImmediate(item.query);
                    });
                    historyList.appendChild(historyItem);
                });
                
                historyElement.style.display = 'block';
            }
        }

        function handleIndexRebuilt(data) {
            console.log(`📚 Search index rebuilt: ${data.blockCount} blocks indexed`);
            updateFooterStats();
        }

        function updateFooterStats() {
            const stats = searchEngine.getStats();
            document.getElementById('footer-stats').textContent = 'Ready';
            document.getElementById('footer-indexed').textContent = stats.indexedBlocks;
        }

        // Define custom elements
        customElements.define('fap-demo-layout', class extends HTMLElement {});
        customElements.define('fap-demo-section', class extends HTMLElement {});
        customElements.define('fap-demo-controls', class extends HTMLElement {});
        customElements.define('fap-demo-button', class extends HTMLElement {});
        customElements.define('fap-instructions', class extends HTMLElement {});
        customElements.define('fap-sample-data', class extends HTMLElement {});
        customElements.define('fap-sample-block', class extends HTMLElement {});
        customElements.define('fap-block-id', class extends HTMLElement {});
        customElements.define('fap-block-content', class extends HTMLElement {});
        
        // Search-specific elements
        customElements.define('fap-search-container', class extends HTMLElement {});
        customElements.define('fap-search-input-area', class extends HTMLElement {});
        customElements.define('fap-search-input', class extends HTMLElement {});
        customElements.define('fap-search-controls', class extends HTMLElement {});
        customElements.define('fap-search-button', class extends HTMLElement {});
        customElements.define('fap-search-stats', class extends HTMLElement {});
        customElements.define('fap-search-count', class extends HTMLElement {});
        customElements.define('fap-search-timing', class extends HTMLElement {});
        customElements.define('fap-search-suggestions', class extends HTMLElement {});
        customElements.define('fap-suggestions-label', class extends HTMLElement {});
        customElements.define('fap-suggestion-list', class extends HTMLElement {});
        customElements.define('fap-suggestion-item', class extends HTMLElement {});
        customElements.define('fap-search-results', class extends HTMLElement {});
        customElements.define('fap-search-result', class extends HTMLElement {});
        customElements.define('fap-result-header', class extends HTMLElement {});
        customElements.define('fap-result-id', class extends HTMLElement {});
        customElements.define('fap-result-score', class extends HTMLElement {});
        customElements.define('fap-result-content', class extends HTMLElement {});
        customElements.define('fap-result-meta', class extends HTMLElement {});
        customElements.define('fap-result-depth', class extends HTMLElement {});
        customElements.define('fap-result-refs', class extends HTMLElement {});
        customElements.define('fap-result-updated', class extends HTMLElement {});
        customElements.define('fap-no-results', class extends HTMLElement {});
        customElements.define('fap-no-results-icon', class extends HTMLElement {});
        customElements.define('fap-no-results-message', class extends HTMLElement {});
        customElements.define('fap-no-results-suggestion', class extends HTMLElement {});
        customElements.define('fap-search-history', class extends HTMLElement {});
        customElements.define('fap-history-header', class extends HTMLElement {});
        customElements.define('fap-history-title', class extends HTMLElement {});
        customElements.define('fap-history-clear', class extends HTMLElement {});
        customElements.define('fap-history-item', class extends HTMLElement {});
        customElements.define('fap-history-query', class extends HTMLElement {});
        customElements.define('fap-history-meta', class extends HTMLElement {});
    </script>
</body>

</html>