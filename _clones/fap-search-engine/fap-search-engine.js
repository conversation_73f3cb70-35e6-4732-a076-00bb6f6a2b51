/**
 * FAP Search Engine - Full-text search with highlighting and ranking
 * 
 * Provides comprehensive search functionality for blocks and content:
 * - Full-text search with fuzzy matching
 * - Result ranking by relevance
 * - Real-time search with debouncing
 * - Highlight matching terms in results
 * - Integration with block system for granular search
 */

class SearchEngine {
    constructor(options = {}) {
        this.options = {
            debounceDelay: 300,
            maxResults: 50,
            fuzzyThreshold: 0.6,
            highlightClass: 'search-highlight',
            caseSensitive: false,
            searchFields: ['content', 'id'],
            ...options
        };
        
        this.searchIndex = new Map(); // Block ID -> searchable content
        this.searchHistory = [];
        this.currentQuery = '';
        this.searchTimer = null;
        this.resultCache = new Map();
        
        this.init();
    }

    init() {
        // Listen for block system events to maintain search index
        if (window.Events) {
            window.Events.on('block:created', this.handleBlockCreated.bind(this));
            window.Events.on('block:changed', this.handleBlockChanged.bind(this));
            window.Events.on('block:deleted', this.handleBlockDeleted.bind(this));
            window.Events.on('store:changed', this.handleStoreChanged.bind(this));
        }

        // Build initial index if BlockStore exists
        this.rebuildIndex();
    }

    // Build search index from all blocks
    rebuildIndex() {
        this.searchIndex.clear();
        this.resultCache.clear();
        
        if (window.FAPBlockCore && window.FAPBlockCore.BlockStore) {
            const { BlockStore } = window.FAPBlockCore;
            const blocks = BlockStore.getAllBlocks();
            
            if (blocks && blocks.length > 0) {
                blocks.forEach(block => {
                    this.indexBlock(block);
                });
                
                console.log(`🔍 Search index built: ${this.searchIndex.size} blocks indexed`);
            } else {
                console.log(`🔍 Search index built: 0 blocks found in BlockStore`);
            }
            
            if (window.Events) {
                window.Events.emit('search:index-rebuilt', { 
                    blockCount: this.searchIndex.size 
                });
            }
        } else {
            console.log(`🔍 Search index build failed: BlockStore not available`);
        }
    }

    // Add or update block in search index
    indexBlock(block) {
        const searchableContent = {
            id: block.id,
            content: block.content,
            parent: block.parent,
            children: block.children,
            refs: block.refs,
            created: block.created,
            updated: block.updated,
            depth: block.getDepth(),
            hasChildren: block.hasChildren(),
            // Create searchable text combining all relevant fields
            searchText: this.createSearchText(block)
        };
        
        this.searchIndex.set(block.id, searchableContent);
    }

    // Create combined searchable text from block
    createSearchText(block) {
        const parts = [
            block.content,
            block.id,
            // Include reference targets for searchability
            ...block.refs.map(ref => ref.target),
            // Include parent/child context if available
            block.parent || '',
            ...block.children
        ];
        
        return parts.filter(Boolean).join(' ').toLowerCase();
    }

    // Main search function with debouncing
    search(query, options = {}) {
        if (this.searchTimer) {
            clearTimeout(this.searchTimer);
        }
        
        this.searchTimer = setTimeout(() => {
            this.performSearch(query, options);
        }, this.options.debounceDelay);
    }

    // Immediate search without debouncing
    searchImmediate(query, options = {}) {
        return this.performSearch(query, options);
    }

    // Core search implementation
    performSearch(query, options = {}) {
        const searchOptions = { ...this.options, ...options };
        
        if (!query || query.trim().length === 0) {
            const emptyResult = {
                query: '',
                results: [],
                totalResults: 0,
                searchTime: 0,
                suggestions: []
            };
            
            this.emitSearchResults(emptyResult);
            return emptyResult;
        }

        const startTime = performance.now();
        const normalizedQuery = searchOptions.caseSensitive ? query : query.toLowerCase();
        this.currentQuery = normalizedQuery;
        
        // Check cache first
        const cacheKey = `${normalizedQuery}:${JSON.stringify(searchOptions)}`;
        if (this.resultCache.has(cacheKey)) {
            const cachedResult = this.resultCache.get(cacheKey);
            this.emitSearchResults(cachedResult);
            return cachedResult;
        }

        // Perform search
        const results = this.executeSearch(normalizedQuery, searchOptions);
        const searchTime = performance.now() - startTime;
        
        const searchResult = {
            query: query,
            results: results.slice(0, searchOptions.maxResults),
            totalResults: results.length,
            searchTime: Math.round(searchTime * 100) / 100,
            suggestions: this.generateSuggestions(normalizedQuery, results)
        };
        
        // Cache result
        this.resultCache.set(cacheKey, searchResult);
        
        // Limit cache size
        if (this.resultCache.size > 100) {
            const firstKey = this.resultCache.keys().next().value;
            this.resultCache.delete(firstKey);
        }
        
        // Add to search history
        this.addToHistory(query);
        
        // Emit results
        this.emitSearchResults(searchResult);
        
        console.log(`🔍 Search completed: "${query}" -> ${results.length} results in ${searchTime}ms`);
        
        return searchResult;
    }

    // Execute the actual search algorithm
    executeSearch(query, options) {
        const results = [];
        const queryTerms = this.tokenizeQuery(query);
        
        for (const [blockId, indexedBlock] of this.searchIndex) {
            const score = this.calculateRelevanceScore(indexedBlock, queryTerms, options);
            
            if (score > 0) {
                const result = {
                    blockId: blockId,
                    block: indexedBlock,
                    score: score,
                    matches: this.findMatches(indexedBlock, queryTerms),
                    highlighted: this.highlightMatches(indexedBlock.content, queryTerms, options)
                };
                
                results.push(result);
            }
        }
        
        // Sort by relevance score (highest first)
        results.sort((a, b) => b.score - a.score);
        
        return results;
    }

    // Tokenize search query into terms
    tokenizeQuery(query) {
        return query
            .toLowerCase()
            .split(/\s+/)
            .filter(term => term.length > 0)
            .map(term => term.replace(/[^\w]/g, ''));
    }

    // Calculate relevance score for a block
    calculateRelevanceScore(indexedBlock, queryTerms, options) {
        let score = 0;
        const content = indexedBlock.content.toLowerCase();
        const searchText = indexedBlock.searchText;
        
        queryTerms.forEach(term => {
            // Exact matches in content get highest score
            if (content.includes(term)) {
                score += 10;
                
                // Bonus for matches at start of content
                if (content.startsWith(term)) {
                    score += 5;
                }
                
                // Bonus for whole word matches
                if (content.match(new RegExp(`\\b${term}\\b`))) {
                    score += 3;
                }
            }
            
            // Fuzzy matches get lower score
            if (this.fuzzyMatch(term, searchText)) {
                score += 2;
            }
            
            // Matches in references
            indexedBlock.refs.forEach(ref => {
                if (ref.target.toLowerCase().includes(term)) {
                    score += 5;
                }
            });
            
            // Matches in block ID
            if (indexedBlock.id.toLowerCase().includes(term)) {
                score += 3;
            }
        });
        
        // Boost score for recent blocks
        const daysSinceUpdate = (Date.now() - new Date(indexedBlock.updated).getTime()) / (1000 * 60 * 60 * 24);
        if (daysSinceUpdate < 7) {
            score += 2;
        }
        
        // Boost score for blocks with children (more important content)
        if (indexedBlock.hasChildren) {
            score += 1;
        }
        
        return score;
    }

    // Simple fuzzy matching
    fuzzyMatch(term, text) {
        if (term.length < 3) return false;
        
        const termChars = term.split('');
        let textIndex = 0;
        let matches = 0;
        
        for (const char of termChars) {
            const found = text.indexOf(char, textIndex);
            if (found !== -1) {
                matches++;
                textIndex = found + 1;
            }
        }
        
        return (matches / term.length) >= this.options.fuzzyThreshold;
    }

    // Find specific matches in content
    findMatches(indexedBlock, queryTerms) {
        const matches = [];
        const content = indexedBlock.content.toLowerCase();
        
        queryTerms.forEach(term => {
            let index = content.indexOf(term);
            while (index !== -1) {
                matches.push({
                    term: term,
                    start: index,
                    end: index + term.length,
                    context: this.getMatchContext(indexedBlock.content, index, term.length)
                });
                index = content.indexOf(term, index + 1);
            }
        });
        
        return matches;
    }

    // Get context around a match
    getMatchContext(content, start, length, contextLength = 50) {
        const contextStart = Math.max(0, start - contextLength);
        const contextEnd = Math.min(content.length, start + length + contextLength);
        
        return {
            before: content.substring(contextStart, start),
            match: content.substring(start, start + length),
            after: content.substring(start + length, contextEnd),
            full: content.substring(contextStart, contextEnd)
        };
    }

    // Highlight matches in content
    highlightMatches(content, queryTerms, options) {
        let highlighted = content;
        const className = options.highlightClass || this.options.highlightClass;
        
        queryTerms.forEach(term => {
            const regex = new RegExp(`(${this.escapeRegex(term)})`, 'gi');
            highlighted = highlighted.replace(regex, `<mark class="${className}">$1</mark>`);
        });
        
        return highlighted;
    }

    // Escape special regex characters
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');
    }

    // Generate search suggestions
    generateSuggestions(query, results) {
        const suggestions = [];
        
        // Suggest common terms from results
        const termFrequency = new Map();
        results.slice(0, 10).forEach(result => {
            const words = result.block.content.toLowerCase().split(/\\s+/);
            words.forEach(word => {
                if (word.length > 3 && !query.includes(word)) {
                    termFrequency.set(word, (termFrequency.get(word) || 0) + 1);
                }
            });
        });
        
        // Get top suggestions
        const sortedTerms = Array.from(termFrequency.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([term]) => term);
        
        suggestions.push(...sortedTerms);
        
        return suggestions;
    }

    // Add query to search history
    addToHistory(query) {
        if (query && query.trim().length > 0) {
            // Remove if already exists
            this.searchHistory = this.searchHistory.filter(item => item.query !== query);
            
            // Add to beginning
            this.searchHistory.unshift({
                query: query,
                timestamp: new Date().toISOString(),
                resultCount: this.resultCache.get(query)?.totalResults || 0
            });
            
            // Limit history size
            if (this.searchHistory.length > 50) {
                this.searchHistory = this.searchHistory.slice(0, 50);
            }
        }
    }

    // Get search history
    getHistory() {
        return [...this.searchHistory];
    }

    // Clear search history
    clearHistory() {
        this.searchHistory = [];
        if (window.Events) {
            window.Events.emit('search:history-cleared');
        }
    }

    // Clear result cache
    clearCache() {
        this.resultCache.clear();
        if (window.Events) {
            window.Events.emit('search:cache-cleared');
        }
    }

    // Emit search results via event system
    emitSearchResults(result) {
        if (window.Events) {
            window.Events.emit('search:results', result);
        }
    }

    // Manual index rebuild (useful for debugging)
    forceRebuildIndex() {
        console.log('🔍 Forcing search index rebuild...');
        this.rebuildIndex();
        return this.searchIndex.size;
    }

    // Event handlers for block system integration
    handleBlockCreated(block) {
        console.log(`🔍 Indexing new block: ${block.id}`);
        this.indexBlock(block);
        this.clearCache(); // Invalidate cache
    }

    handleBlockChanged(block) {
        console.log(`🔍 Re-indexing changed block: ${block.id}`);
        this.indexBlock(block);
        this.clearCache(); // Invalidate cache
    }

    handleBlockDeleted(block) {
        console.log(`🔍 Removing block from index: ${block.id}`);
        this.searchIndex.delete(block.id);
        this.clearCache(); // Invalidate cache
    }

    handleStoreChanged(data) {
        console.log(`🔍 Store changed: ${data.action} for block ${data.block.id}`);
        // Rebuild index if major changes
        if (data.action === 'clear') {
            this.rebuildIndex();
        }
    }

    // Get search statistics
    getStats() {
        return {
            indexedBlocks: this.searchIndex.size,
            cacheSize: this.resultCache.size,
            historySize: this.searchHistory.length,
            currentQuery: this.currentQuery,
            lastIndexUpdate: new Date().toISOString()
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SearchEngine };
} else {
    window.FAPSearchEngine = { SearchEngine };
}