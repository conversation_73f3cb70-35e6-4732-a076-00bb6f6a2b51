/**
 * FAP Search Engine - Styling for search interface and results
 * 
 * Provides comprehensive styling for search functionality including:
 * - Search input and controls
 * - Result display with highlighting
 * - Search statistics and history
 * - Responsive design for all screen sizes
 */

/* Search container */
fap-search-container {
    display: block;
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Search input area */
fap-search-input-area {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e5e7eb;
    background: #f9fafb;
}

fap-search-input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 14px;
    outline: none;
    transition: all 0.15s ease;
}

fap-search-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

fap-search-input::placeholder {
    color: #9ca3af;
}

/* Search controls */
fap-search-controls {
    display: flex;
    gap: 8px;
    margin-left: 12px;
}

fap-search-button {
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    background: #ffffff;
    color: #374151;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.15s ease;
}

fap-search-button:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

fap-search-button[active="true"] {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

/* Search stats */
fap-search-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    font-size: 12px;
    color: #6b7280;
}

fap-search-timing {
    font-weight: 500;
}

fap-search-count {
    color: #374151;
}

/* Results container */
fap-search-results {
    display: block;
    max-height: 400px;
    overflow-y: auto;
}

/* Individual result item */
fap-search-result {
    display: block;
    padding: 12px 16px;
    border-bottom: 1px solid #f1f5f9;
    cursor: pointer;
    transition: background-color 0.15s ease;
}

fap-search-result:hover {
    background-color: #f8fafc;
}

fap-search-result[selected="true"] {
    background-color: #eff6ff;
    border-left: 3px solid #3b82f6;
}

/* Result content */
fap-result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

fap-result-id {
    font-family: monospace;
    font-size: 11px;
    color: #6b7280;
    background: #f1f5f9;
    padding: 2px 6px;
    border-radius: 3px;
}

fap-result-score {
    font-size: 11px;
    color: #059669;
    font-weight: 500;
}

fap-result-content {
    display: block;
    line-height: 1.5;
    color: #374151;
    margin-bottom: 6px;
}

fap-result-context {
    display: block;
    font-size: 12px;
    color: #6b7280;
    font-style: italic;
}

/* Search highlighting */
.search-highlight {
    background-color: #fef3c7;
    color: #92400e;
    padding: 1px 2px;
    border-radius: 2px;
    font-weight: 500;
}

/* Result metadata */
fap-result-meta {
    display: flex;
    gap: 12px;
    margin-top: 6px;
    font-size: 11px;
    color: #9ca3af;
}

fap-result-depth {
    display: flex;
    align-items: center;
    gap: 4px;
}

fap-result-refs {
    display: flex;
    align-items: center;
    gap: 4px;
}

fap-result-updated {
    margin-left: auto;
}

/* No results state */
fap-no-results {
    display: block;
    text-align: center;
    padding: 40px 20px;
    color: #6b7280;
}

fap-no-results-icon {
    font-size: 48px;
    margin-bottom: 12px;
    opacity: 0.5;
}

fap-no-results-message {
    font-size: 16px;
    margin-bottom: 8px;
}

fap-no-results-suggestion {
    font-size: 14px;
    color: #9ca3af;
}

/* Search suggestions */
fap-search-suggestions {
    display: block;
    padding: 12px 16px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
}

fap-suggestions-label {
    font-size: 12px;
    color: #6b7280;
    margin-bottom: 6px;
    font-weight: 500;
}

fap-suggestion-list {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
}

fap-suggestion-item {
    padding: 4px 8px;
    background: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    color: #374151;
    cursor: pointer;
    transition: all 0.15s ease;
}

fap-suggestion-item:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

/* Search history */
fap-search-history {
    display: block;
    max-height: 200px;
    overflow-y: auto;
    border-top: 1px solid #e5e7eb;
}

fap-history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
}

fap-history-title {
    font-size: 12px;
    font-weight: 500;
    color: #374151;
}

fap-history-clear {
    font-size: 11px;
    color: #6b7280;
    cursor: pointer;
    padding: 2px 6px;
    border-radius: 3px;
    transition: background-color 0.15s ease;
}

fap-history-clear:hover {
    background: #e5e7eb;
}

fap-history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 16px;
    cursor: pointer;
    transition: background-color 0.15s ease;
}

fap-history-item:hover {
    background-color: #f8fafc;
}

fap-history-query {
    font-size: 13px;
    color: #374151;
}

fap-history-meta {
    font-size: 11px;
    color: #9ca3af;
}

/* Loading state */
fap-search-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #6b7280;
}

fap-loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Search filters */
fap-search-filters {
    display: flex;
    gap: 8px;
    padding: 8px 16px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    flex-wrap: wrap;
}

fap-filter-button {
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    background: #ffffff;
    color: #6b7280;
    cursor: pointer;
    font-size: 11px;
    transition: all 0.15s ease;
}

fap-filter-button:hover {
    background: #f3f4f6;
}

fap-filter-button[active="true"] {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

/* Responsive design */
@media (max-width: 768px) {
    fap-search-input-area {
        flex-direction: column;
        gap: 8px;
        align-items: stretch;
    }
    
    fap-search-controls {
        margin-left: 0;
        justify-content: center;
    }
    
    fap-search-stats {
        flex-direction: column;
        gap: 4px;
        text-align: center;
    }
    
    fap-search-filters {
        justify-content: center;
    }
    
    fap-result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
    
    fap-result-meta {
        flex-direction: column;
        gap: 4px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    fap-search-container {
        background: #1f2937;
        color: #f3f4f6;
    }
    
    fap-search-input-area {
        background: #374151;
        border-color: #4b5563;
    }
    
    fap-search-input {
        background: #1f2937;
        border-color: #4b5563;
        color: #f3f4f6;
    }
    
    fap-search-input:focus {
        border-color: #60a5fa;
    }
    
    fap-search-button {
        background: #374151;
        border-color: #4b5563;
        color: #f3f4f6;
    }
    
    fap-search-button:hover {
        background: #4b5563;
    }
    
    fap-search-stats {
        background: #374151;
        border-color: #4b5563;
        color: #9ca3af;
    }
    
    fap-search-result:hover {
        background-color: #374151;
    }
    
    fap-search-result[selected="true"] {
        background-color: #1e40af;
        border-left-color: #60a5fa;
    }
    
    .search-highlight {
        background-color: #fbbf24;
        color: #92400e;
    }
}

/* Print styles */
@media print {
    fap-search-container {
        box-shadow: none;
        border: 1px solid #000;
    }
    
    fap-search-input-area,
    fap-search-controls,
    fap-search-filters {
        display: none;
    }
    
    fap-search-result {
        break-inside: avoid;
        border-bottom: 1px solid #ccc;
    }
    
    .search-highlight {
        background-color: #ffff99;
        color: #000;
    }
}