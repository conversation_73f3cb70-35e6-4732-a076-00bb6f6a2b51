/**
 * FAP Outliner - Styling for the interactive outliner interface
 * 
 * Extends the base block styling with interactive editing features
 */

/* Main outliner container */
.fap-outliner {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #374151;
    background: #ffffff;
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

/* Block item in outliner context */
.fap-outliner block-item {
    display: flex;
    align-items: flex-start;
    margin: 2px 0;
    padding: 2px 0;
    border-radius: 4px;
    transition: background-color 0.15s ease;
    position: relative;
}

.fap-outliner block-item:hover {
    background-color: #f9fafb;
}

/* Block bullet styling */
.fap-outliner block-bullet {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #6b7280;
    border-radius: 50%;
    margin: 8px 8px 0 0;
    cursor: pointer;
    flex-shrink: 0;
    transition: all 0.15s ease;
}

.fap-outliner block-bullet:hover {
    background-color: #374151;
    transform: scale(1.2);
}

/* Expandable bullets */
.fap-outliner block-bullet[expandable="true"] {
    width: 0;
    height: 0;
    background: none;
    border-left: 5px solid #6b7280;
    border-top: 4px solid transparent;
    border-bottom: 4px solid transparent;
    border-radius: 0;
    margin-top: 6px;
    transition: transform 0.2s ease;
}

.fap-outliner block-bullet[expandable="true"]:hover {
    border-left-color: #374151;
    transform: scale(1.1);
}

.fap-outliner block-bullet[expandable="true"][expanded="true"] {
    transform: rotate(90deg);
}

.fap-outliner block-bullet[expandable="true"][expanded="true"]:hover {
    transform: rotate(90deg) scale(1.1);
}

/* Block content styling */
.fap-outliner block-content {
    flex: 1;
    min-height: 1.6em;
    padding: 2px 4px;
    border: 1px solid transparent;
    border-radius: 3px;
    outline: none;
    word-wrap: break-word;
    white-space: pre-wrap;
    transition: all 0.15s ease;
}

.fap-outliner block-content:focus {
    background-color: #fffbf0;
    border-color: #fbbf24;
    box-shadow: 0 0 0 2px rgba(251, 191, 36, 0.1);
}

.fap-outliner block-content:empty::before {
    content: "Type something...";
    color: #9ca3af;
    font-style: italic;
}

/* Block hierarchy indentation */
.fap-outliner block-item[depth="0"] { margin-left: 0; }
.fap-outliner block-item[depth="1"] { margin-left: 24px; }
.fap-outliner block-item[depth="2"] { margin-left: 48px; }
.fap-outliner block-item[depth="3"] { margin-left: 72px; }
.fap-outliner block-item[depth="4"] { margin-left: 96px; }
.fap-outliner block-item[depth="5"] { margin-left: 120px; }
.fap-outliner block-item[depth="6"] { margin-left: 144px; }

/* Children container */
.fap-outliner block-children {
    display: block;
    width: 100%;
    margin-top: 2px;
}

.fap-outliner block-children[collapsed="true"] {
    display: none;
}

/* Block selection and focus states */
.fap-outliner block-item[selected="true"] {
    background-color: #eff6ff;
    border-radius: 4px;
}

.fap-outliner block-item[editing="true"] {
    background-color: #fffbf0;
    border-radius: 4px;
}

/* Drag and drop states */
.fap-outliner block-item[dragging="true"] {
    opacity: 0.6;
    transform: rotate(2deg);
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.fap-outliner block-item[drop-target="true"] {
    border-top: 2px solid #3b82f6;
    margin-top: 4px;
}

.fap-outliner block-item[drop-target="true"]::before {
    content: "";
    position: absolute;
    top: -3px;
    left: 0;
    right: 0;
    height: 2px;
    background: #3b82f6;
    border-radius: 1px;
}

/* Reference styling within outliner */
.fap-outliner block-ref {
    color: #0066cc;
    text-decoration: none;
    cursor: pointer;
    border-radius: 2px;
    padding: 1px 3px;
    transition: all 0.15s ease;
}

.fap-outliner block-ref:hover {
    background-color: #dbeafe;
    text-decoration: underline;
}

.fap-outliner block-ref[type="page"] {
    color: #0066cc;
    background-color: rgba(0, 102, 204, 0.1);
}

.fap-outliner block-ref[type="block"] {
    color: #8b5cf6;
    background-color: rgba(139, 92, 246, 0.1);
}

.fap-outliner block-ref[type="tag"] {
    color: #059669;
    background-color: rgba(5, 150, 105, 0.1);
    font-weight: 500;
}

/* Keyboard navigation indicators */
.fap-outliner block-item[keyboard-focus="true"] {
    outline: 2px solid #3b82f6;
    outline-offset: 1px;
}

/* Auto-save indicator */
.fap-outliner[saving="true"]::after {
    content: "Saving...";
    position: fixed;
    top: 20px;
    right: 20px;
    background: #059669;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
}

/* Empty outliner state */
.fap-outliner:empty::before {
    content: "Start typing to create your first block...";
    color: #9ca3af;
    font-style: italic;
    display: block;
    text-align: center;
    padding: 40px 20px;
    border: 2px dashed #d1d5db;
    border-radius: 8px;
    margin: 20px 0;
}

/* Responsive design */
@media (max-width: 768px) {
    .fap-outliner {
        padding: 10px;
    }
    
    .fap-outliner block-item[depth="1"] { margin-left: 16px; }
    .fap-outliner block-item[depth="2"] { margin-left: 32px; }
    .fap-outliner block-item[depth="3"] { margin-left: 48px; }
    .fap-outliner block-item[depth="4"] { margin-left: 64px; }
    .fap-outliner block-item[depth="5"] { margin-left: 80px; }
    .fap-outliner block-item[depth="6"] { margin-left: 96px; }
    
    .fap-outliner block-bullet {
        margin-right: 6px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .fap-outliner {
        background: #1f2937;
        color: #f3f4f6;
    }
    
    .fap-outliner block-item:hover {
        background-color: #374151;
    }
    
    .fap-outliner block-content:focus {
        background-color: #374151;
        border-color: #fbbf24;
    }
    
    .fap-outliner block-bullet {
        background-color: #9ca3af;
    }
    
    .fap-outliner block-bullet[expandable="true"] {
        border-left-color: #9ca3af;
    }
    
    .fap-outliner block-ref {
        color: #60a5fa;
    }
    
    .fap-outliner block-ref[type="page"] {
        color: #60a5fa;
        background-color: rgba(96, 165, 250, 0.1);
    }
    
    .fap-outliner block-ref[type="block"] {
        color: #a78bfa;
        background-color: rgba(167, 139, 250, 0.1);
    }
    
    .fap-outliner block-ref[type="tag"] {
        color: #34d399;
        background-color: rgba(52, 211, 153, 0.1);
    }
}

/* Print styles */
@media print {
    .fap-outliner {
        background: white;
        color: black;
        padding: 0;
    }
    
    .fap-outliner block-bullet {
        background-color: black;
    }
    
    .fap-outliner block-bullet[expandable="true"] {
        border-left-color: black;
    }
    
    .fap-outliner block-content:focus {
        background: none;
        border: none;
        box-shadow: none;
    }
    
    .fap-outliner block-ref {
        color: black;
        background: none;
        text-decoration: underline;
    }
}