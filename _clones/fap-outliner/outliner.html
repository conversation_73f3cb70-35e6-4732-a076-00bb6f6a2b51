<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAP Outliner - Demo</title>
    <link rel="stylesheet" href="../fap-block-core/fap-block-core.css">
    <link rel="stylesheet" href="fap-outliner.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f8fafc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .demo-controls {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        
        .demo-controls h3 {
            margin: 0 0 10px 0;
            color: #374151;
        }
        
        .demo-controls button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            background: #f9fafb;
            color: #374151;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.15s ease;
        }
        
        .demo-controls button:hover {
            background: #f3f4f6;
            border-color: #9ca3af;
        }
        
        .demo-controls button:active {
            background: #e5e7eb;
        }
        
        .outliner-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            min-height: 400px;
        }
        
        .keyboard-help {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
        }
        
        .keyboard-help h4 {
            margin: 0 0 10px 0;
            color: #1e40af;
        }
        
        .keyboard-help ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .keyboard-help li {
            margin: 5px 0;
        }
        
        .keyboard-help kbd {
            background: #f1f5f9;
            border: 1px solid #cbd5e1;
            border-radius: 3px;
            padding: 2px 6px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="demo-header">
        <h1>FAP Outliner Demo</h1>
        <p>Interactive hierarchical block editing with keyboard navigation</p>
    </div>

    <div class="demo-controls">
        <h3>Controls</h3>
        <button onclick="loadSampleData()">Load Sample Data</button>
        <button onclick="addRootBlock()">Add Root Block</button>
        <button onclick="clearOutliner()">Clear All</button>
        <button onclick="exportData()">Export Data</button>
        <button onclick="toggleDragDrop()">Toggle Drag & Drop</button>
        <button onclick="saveOutliner()">Save</button>
    </div>

    <div class="outliner-container">
        <div id="outliner"></div>
    </div>

    <div class="keyboard-help">
        <h4>Keyboard Shortcuts</h4>
        <ul>
            <li><kbd>Enter</kbd> - Create new block</li>
            <li><kbd>Shift + Enter</kbd> - Insert line break</li>
            <li><kbd>Tab</kbd> - Indent block (make it a child)</li>
            <li><kbd>Shift + Tab</kbd> - Outdent block (move up hierarchy)</li>
            <li><kbd>↑</kbd> / <kbd>↓</kbd> - Navigate between blocks</li>
            <li><kbd>Backspace</kbd> - Delete empty block at start</li>
            <li>Click bullet to expand/collapse children</li>
            <li>Drag blocks to reorder (if enabled)</li>
        </ul>
    </div>

    <!-- Load dependencies -->
    <script src="../fap-event-system/fap-event-system.js"></script>
    <script src="../fap-block-core/fap-block-core.js"></script>
    <script src="fap-outliner.js"></script>

    <script>
        // Initialize the demo
        let outliner;
        let dragDropEnabled = true;

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize outliner
            outliner = new window.FAPOutliner.Outliner('#outliner', {
                autoSave: true,
                autoSaveDelay: 1000,
                enableDragDrop: dragDropEnabled,
                enableKeyboardNav: true
            });

            // Load sample data
            loadSampleData();

            // Listen for outliner events
            window.Events.on('outliner:save', function(data) {
                console.log('Outliner saved:', data);
                showSaveIndicator();
            });

            // Listen for block events
            window.Events.on('block:changed', function(block) {
                console.log('Block changed:', block.id, block.content);
            });

            console.log('FAP Outliner demo initialized');
        });

        function loadSampleData() {
            const { Block, BlockStore } = window.FAPBlockCore;
            
            // Clear existing data
            BlockStore.clear();
            
            // Create sample blocks
            const blocks = [
                new Block({
                    id: 'root-1',
                    content: 'Welcome to FAP Outliner! This is a root block with a [[Page Reference]]'
                }),
                new Block({
                    id: 'child-1',
                    content: 'This is a child block with #tag and ((block-reference))',
                    parent: 'root-1'
                }),
                new Block({
                    id: 'grandchild-1',
                    content: 'This is a nested child block - try the keyboard shortcuts!',
                    parent: 'child-1'
                }),
                new Block({
                    id: 'child-2',
                    content: 'Another child block at the same level',
                    parent: 'root-1'
                }),
                new Block({
                    id: 'root-2',
                    content: 'Second root block - try creating new blocks with Enter'
                }),
                new Block({
                    id: 'root-3',
                    content: 'Third root block with some **formatting** and more content to test wrapping behavior in the outliner interface'
                })
            ];

            // Set up hierarchy
            blocks[0].addChild(blocks[1]); // root-1 -> child-1
            blocks[1].addChild(blocks[2]); // child-1 -> grandchild-1
            blocks[0].addChild(blocks[3]); // root-1 -> child-2

            // Add to store
            blocks.forEach(block => BlockStore.addBlock(block));

            // Load into outliner
            outliner.loadBlocks();
            
            console.log('Sample data loaded');
        }

        function addRootBlock() {
            const { Block, BlockStore } = window.FAPBlockCore;
            
            const newBlock = new Block({
                content: 'New root block - start typing!'
            });
            
            BlockStore.addBlock(newBlock);
            outliner.loadBlocks(); // Refresh display
            
            // Focus the new block
            setTimeout(() => {
                outliner.focusBlock(newBlock.id);
            }, 100);
        }

        function clearOutliner() {
            if (confirm('Clear all blocks? This cannot be undone.')) {
                window.FAPBlockCore.BlockStore.clear();
                outliner.loadBlocks();
                console.log('Outliner cleared');
            }
        }

        function exportData() {
            const { BlockStore } = window.FAPBlockCore;
            const blocks = BlockStore.getAllBlocks();
            const data = blocks.map(block => block.toJSON());
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = 'outliner-data.json';
            a.click();
            
            URL.revokeObjectURL(url);
            console.log('Data exported');
        }

        function toggleDragDrop() {
            dragDropEnabled = !dragDropEnabled;
            
            // Reinitialize outliner with new settings
            const container = document.getElementById('outliner');
            container.innerHTML = '';
            
            outliner = new window.FAPOutliner.Outliner('#outliner', {
                autoSave: true,
                autoSaveDelay: 1000,
                enableDragDrop: dragDropEnabled,
                enableKeyboardNav: true
            });
            
            outliner.loadBlocks();
            
            console.log('Drag & drop', dragDropEnabled ? 'enabled' : 'disabled');
        }

        function saveOutliner() {
            outliner.save();
        }

        function showSaveIndicator() {
            const container = document.querySelector('.outliner-container');
            container.setAttribute('saving', 'true');
            
            setTimeout(() => {
                container.removeAttribute('saving');
            }, 1000);
        }

        // Add some demo keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            // Ctrl/Cmd + S to save
            if ((event.ctrlKey || event.metaKey) && event.key === 's') {
                event.preventDefault();
                saveOutliner();
            }
            
            // Ctrl/Cmd + N to add new root block
            if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
                event.preventDefault();
                addRootBlock();
            }
        });
    </script>
</body>
</html>