# FAP Outliner

Interactive hierarchical block editing interface that provides the core Logseq-like editing experience. Built with vanilla JavaScript and semantic HTML elements.

## Features

- **Hierarchical Editing**: Create nested block structures with visual indentation up to 10 levels
- **Complete Keyboard Navigation**: Full keyboard support for efficient editing
  - Enter: Create new block at same level
  - Shift+Enter: Insert line break within block
  - Tab: Indent block (make child of previous)
  - Shift+Tab: Outdent block (move up hierarchy)
  - Arrow keys: Navigate between blocks when at start/end
  - Backspace: Delete empty blocks
- **Real-time Auto-save**: Automatic saving with configurable delay (default 500ms)
- **Drag & Drop Framework**: Structure ready for reordering blocks (visual feedback implemented)
- **Expand/Collapse**: Click bullets to hide/show child blocks with smooth animations
- **Reference Integration**: Automatic extraction and display of [[page]], ((block)), and #tag references
- **Event-Driven Architecture**: Full integration with event system for loose coupling
- **Focus Management**: Proper focus handling with event emission for integration

## Keyboard Shortcuts

- **Enter**: Create new block at same level
- **Shift + Enter**: Insert line break within current block
- **Tab**: Indent block (make it a child of previous block)
- **Shift + Tab**: Outdent block (move up in hierarchy)
- **Arrow Up/Down**: Navigate between blocks when at start/end
- **Backspace**: Delete empty block when at start of content

## Usage

```javascript
// Create outliner instance
const outliner = new Outliner('#container', {
    autoSave: true,
    autoSaveDelay: 500,
    enableDragDrop: true,
    enableKeyboardNav: true,
    maxDepth: 10
});

// Load blocks from BlockStore
outliner.loadBlocks();

// Or load specific root blocks
outliner.loadBlocks(['block-id-1', 'block-id-2']);

// Focus a specific block
outliner.focusBlock('block-id');

// Save manually
outliner.save();
```

## HTML Structure

The outliner generates semantic HTML:

```html
<div class="fap-outliner">
    <block-item depth="0" id="block-1">
        <block-bullet expandable="true" expanded="true"></block-bullet>
        <block-content contenteditable="true" data-block-id="block-1">
            Block content here
        </block-content>
        <block-children>
            <block-item depth="1" id="child-1">
                <!-- Child block structure -->
            </block-item>
        </block-children>
    </block-item>
</div>
```

## CSS Classes and Attributes

### Block States
- `depth="N"`: Visual indentation level (0-10)
- `has-children="true"`: Block contains child blocks
- `dragging="true"`: Block is being dragged
- `drop-target="true"`: Valid drop target during drag
- `selected="true"`: Block is selected
- `editing="true"`: Block is being edited

### Bullet States
- `expandable="true"`: Bullet can expand/collapse children
- `expanded="true"`: Children are currently visible

### Container States
- `saving="true"`: Auto-save in progress

## Events

### Emitted Events
- `outliner:save`: Fired when auto-save or manual save occurs
- `outliner:block-focused`: When a block gains focus
- `outliner:block-created`: When a new block is created via UI
- `outliner:hierarchy-changed`: When block hierarchy is modified

### Listened Events
- `block:changed`: Updates UI when block content changes
- `block:created`: Adds new blocks to the display
- `block:deleted`: Removes blocks from the display

## Configuration Options

```javascript
const options = {
    autoSave: true,           // Enable automatic saving
    autoSaveDelay: 500,       // Delay in ms before auto-save
    enableDragDrop: true,     // Enable drag and drop reordering
    enableKeyboardNav: true,  // Enable keyboard navigation
    maxDepth: 10             // Maximum nesting depth
};
```

## Integration with Other Packages

### Block Core
- Uses Block and BlockStore classes for data management
- Automatically extracts and displays references
- Maintains block hierarchy and relationships

### Event System
- Emits events for UI changes and user actions
- Listens for data changes from other components
- Enables loose coupling with other packages

### Future Integrations
- **fap-search-engine**: Highlight search results in outliner
- **fap-page-references**: Navigate to referenced pages
- **fap-whiteboard**: Drag blocks to whiteboard canvas

## Styling

The outliner uses semantic HTML elements for clean, maintainable CSS:

```css
/* Target specific elements */
.fap-outliner block-item[depth="2"] { /* 2nd level blocks */ }
.fap-outliner block-bullet[expandable] { /* Expandable bullets */ }
.fap-outliner block-content:focus { /* Focused content */ }
```

## Responsive Design

- Mobile-friendly with adjusted indentation
- Touch-friendly bullet sizes
- Responsive typography and spacing
- Dark mode support via CSS media queries

## Performance Considerations

- Efficient DOM updates using element caching
- Debounced auto-save to prevent excessive saves
- Event delegation for keyboard and mouse handling
- Minimal DOM manipulation during editing

## Browser Support

- Modern browsers with ES6+ support (Chrome, Firefox, Safari, Edge)
- Optimized for Chromium/Electron (primary target)
- Uses standard DOM APIs and CSS features
- No external dependencies beyond our own packages
- Tested with Playwright MCP integration
- Graceful degradation for older browsers

## Development Notes

### Design Decisions
1. **Semantic HTML**: Custom elements like `<block-item>` for clarity
2. **Event-Driven**: Loose coupling via event system
3. **Vanilla JS**: No framework dependencies for simplicity
4. **Progressive Enhancement**: Works without JavaScript for basic viewing

### Future Enhancements
- Multi-selection support
- Copy/paste between blocks
- Undo/redo functionality
- Block templates and snippets
- Collaborative editing indicators
- Advanced drag and drop with visual feedback

### Testing Strategy
- Interactive HTML demo for manual testing
- Event logging for debugging
- Performance monitoring in debug mode
- Cross-browser compatibility testing