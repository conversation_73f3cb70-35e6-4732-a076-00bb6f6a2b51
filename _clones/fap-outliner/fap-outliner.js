/**
 * FAP Outliner - Visual editing interface for hierarchical blocks
 * 
 * Provides the interactive editing experience for blocks, including:
 * - Visual hierarchy with bullets and indentation
 * - Keyboard navigation (En<PERSON>, Tab, Shift+Tab, <PERSON> keys)
 * - Drag and drop reordering
 * - Expand/collapse functionality
 * - Real-time editing with auto-save
 */

class Outliner {
    constructor(container, options = {}) {
        this.container = typeof container === 'string' ? document.querySelector(container) : container;
        this.options = {
            autoSave: true,
            autoSaveDelay: 500,
            enableDragDrop: true,
            enableKeyboardNav: true,
            maxDepth: 10,
            ...options
        };
        
        this.blocks = new Map(); // Block ID -> Block instance
        this.blockElements = new Map(); // Block ID -> DOM element
        this.currentFocus = null;
        this.autoSaveTimer = null;
        this.dragState = null;
        
        this.init();
    }

    init() {
        this.container.classList.add('fap-outliner');
        this.setupEventListeners();
        
        // Listen for block system events
        if (window.Events) {
            window.Events.on('block:changed', this.handleBlockChanged.bind(this));
            window.Events.on('block:created', this.handleBlockCreated.bind(this));
            window.Events.on('block:deleted', this.handleBlockDeleted.bind(this));
        }
    }

    setupEventListeners() {
        // Keyboard navigation
        if (this.options.enableKeyboardNav) {
            this.container.addEventListener('keydown', this.handleKeyDown.bind(this));
        }

        // Click handling
        this.container.addEventListener('click', this.handleClick.bind(this));
        
        // Input handling
        this.container.addEventListener('input', this.handleInput.bind(this));
        
        // Focus handling
        this.container.addEventListener('focusin', this.handleFocusIn.bind(this));
        this.container.addEventListener('focusout', this.handleFocusOut.bind(this));

        // Drag and drop
        if (this.options.enableDragDrop) {
            this.container.addEventListener('dragstart', this.handleDragStart.bind(this));
            this.container.addEventListener('dragover', this.handleDragOver.bind(this));
            this.container.addEventListener('drop', this.handleDrop.bind(this));
            this.container.addEventListener('dragend', this.handleDragEnd.bind(this));
        }
    }

    // Render a block and its children
    renderBlock(block, parentElement = null) {
        const element = this.createBlockElement(block);
        this.blockElements.set(block.id, element);
        
        if (parentElement) {
            parentElement.appendChild(element);
        } else {
            this.container.appendChild(element);
        }

        // Render children
        if (block.children && block.children.length > 0) {
            const childrenContainer = element.querySelector('block-children');
            block.children.forEach(childId => {
                const childBlock = this.blocks.get(childId);
                if (childBlock) {
                    this.renderBlock(childBlock, childrenContainer);
                }
            });
        }

        return element;
    }

    createBlockElement(block) {
        const element = document.createElement('block-item');
        element.setAttribute('id', block.id);
        element.setAttribute('depth', block.getDepth().toString());
        element.setAttribute('has-children', block.hasChildren().toString());
        
        if (this.options.enableDragDrop) {
            element.setAttribute('draggable', 'true');
        }

        // Create bullet
        const bullet = document.createElement('block-bullet');
        if (block.hasChildren()) {
            bullet.setAttribute('expandable', 'true');
            bullet.setAttribute('expanded', 'true');
        }

        // Create content
        const content = document.createElement('block-content');
        content.setAttribute('contenteditable', 'true');
        content.setAttribute('data-block-id', block.id);
        content.textContent = block.content;

        // Create children container
        const children = document.createElement('block-children');

        element.appendChild(bullet);
        element.appendChild(content);
        element.appendChild(children);

        return element;
    }

    // Load blocks from BlockStore
    loadBlocks(rootBlockIds = null) {
        this.container.innerHTML = '';
        this.blockElements.clear();

        if (window.FAPBlockCore) {
            const { BlockStore } = window.FAPBlockCore;
            
            // Get root blocks or specified blocks
            const blocksToRender = rootBlockIds 
                ? rootBlockIds.map(id => BlockStore.getBlock(id)).filter(Boolean)
                : BlockStore.getRootBlocks();

            // Store all blocks for quick access
            BlockStore.getAllBlocks().forEach(block => {
                this.blocks.set(block.id, block);
            });

            // Render root blocks
            blocksToRender.forEach(block => {
                this.renderBlock(block);
            });
        }
    }

    // Handle keyboard navigation
    handleKeyDown(event) {
        const target = event.target;
        if (!target.matches('block-content[contenteditable]')) return;

        const blockId = target.getAttribute('data-block-id');
        const block = this.blocks.get(blockId);
        if (!block) return;

        switch (event.key) {
            case 'Enter':
                event.preventDefault();
                this.handleEnterKey(block, target, event.shiftKey);
                break;
            
            case 'Tab':
                event.preventDefault();
                this.handleTabKey(block, event.shiftKey);
                break;
            
            case 'ArrowUp':
                if (this.isAtStartOfBlock(target)) {
                    event.preventDefault();
                    this.focusPreviousBlock(block);
                }
                break;
            
            case 'ArrowDown':
                if (this.isAtEndOfBlock(target)) {
                    event.preventDefault();
                    this.focusNextBlock(block);
                }
                break;
            
            case 'Backspace':
                if (this.isAtStartOfBlock(target) && block.content.trim() === '') {
                    event.preventDefault();
                    this.handleBackspaceAtStart(block);
                }
                break;
        }
    }

    handleEnterKey(block, contentElement, shiftPressed) {
        if (shiftPressed) {
            // Shift+Enter: Insert line break
            document.execCommand('insertLineBreak');
            return;
        }

        // Create new block
        const newBlock = new (window.FAPBlockCore.Block)({
            content: '',
            parent: block.parent
        });

        // Add to store
        if (window.FAPBlockCore) {
            window.FAPBlockCore.BlockStore.addBlock(newBlock);
        }

        // Insert after current block
        this.insertBlockAfter(newBlock, block);
        
        // Focus new block
        setTimeout(() => {
            this.focusBlock(newBlock.id);
        }, 0);
    }

    handleTabKey(block, shiftPressed) {
        if (shiftPressed) {
            // Shift+Tab: Outdent
            this.outdentBlock(block);
        } else {
            // Tab: Indent
            this.indentBlock(block);
        }
    }

    indentBlock(block) {
        const element = this.blockElements.get(block.id);
        if (!element) return;

        // Find previous sibling to become parent
        const prevSibling = element.previousElementSibling;
        if (!prevSibling || !prevSibling.matches('block-item')) return;

        const prevBlockId = prevSibling.getAttribute('id');
        const prevBlock = this.blocks.get(prevBlockId);
        if (!prevBlock) return;

        // Update block hierarchy
        if (block.parent) {
            const oldParent = this.blocks.get(block.parent);
            if (oldParent) {
                oldParent.removeChild(block.id);
            }
        }

        prevBlock.addChild(block);
        block.parent = prevBlock.id;

        // Update DOM
        this.refreshBlockElement(block);
        this.refreshBlockElement(prevBlock);
    }

    outdentBlock(block) {
        if (!block.parent) return; // Already at root level

        const parent = this.blocks.get(block.parent);
        if (!parent) return;

        // Remove from current parent
        parent.removeChild(block.id);

        // Add to grandparent (or root)
        if (parent.parent) {
            const grandparent = this.blocks.get(parent.parent);
            if (grandparent) {
                grandparent.addChild(block);
                block.parent = grandparent.id;
            }
        } else {
            block.parent = null;
        }

        // Update DOM
        this.refreshBlockElement(block);
        this.refreshBlockElement(parent);
    }

    insertBlockAfter(newBlock, afterBlock) {
        const afterElement = this.blockElements.get(afterBlock.id);
        if (!afterElement) return;

        const newElement = this.createBlockElement(newBlock);
        this.blockElements.set(newBlock.id, newElement);
        this.blocks.set(newBlock.id, newBlock);

        afterElement.insertAdjacentElement('afterend', newElement);
    }

    refreshBlockElement(block) {
        const element = this.blockElements.get(block.id);
        if (!element) return;

        element.setAttribute('depth', block.getDepth().toString());
        element.setAttribute('has-children', block.hasChildren().toString());

        const bullet = element.querySelector('block-bullet');
        if (bullet) {
            if (block.hasChildren()) {
                bullet.setAttribute('expandable', 'true');
            } else {
                bullet.removeAttribute('expandable');
                bullet.removeAttribute('expanded');
            }
        }
    }

    // Handle input changes
    handleInput(event) {
        const target = event.target;
        if (!target.matches('block-content[contenteditable]')) return;

        const blockId = target.getAttribute('data-block-id');
        const block = this.blocks.get(blockId);
        if (!block) return;

        // Update block content
        block.updateContent(target.textContent);

        // Auto-save
        if (this.options.autoSave) {
            this.scheduleAutoSave();
        }
    }

    scheduleAutoSave() {
        if (this.autoSaveTimer) {
            clearTimeout(this.autoSaveTimer);
        }
        
        this.autoSaveTimer = setTimeout(() => {
            this.save();
        }, this.options.autoSaveDelay);
    }

    save() {
        // Emit save event
        if (window.Events) {
            window.Events.emit('outliner:save', {
                blocks: Array.from(this.blocks.values())
            });
        }
    }

    // Handle clicks
    handleClick(event) {
        const bullet = event.target.closest('block-bullet[expandable]');
        if (bullet) {
            this.toggleBlockExpansion(bullet);
            return;
        }

        const content = event.target.closest('block-content');
        if (content) {
            this.focusBlock(content.getAttribute('data-block-id'));
        }
    }

    toggleBlockExpansion(bullet) {
        const isExpanded = bullet.getAttribute('expanded') === 'true';
        bullet.setAttribute('expanded', (!isExpanded).toString());
        
        const blockItem = bullet.closest('block-item');
        const children = blockItem.querySelector('block-children');
        if (children) {
            children.setAttribute('collapsed', isExpanded.toString());
        }
    }

    // Focus management
    focusBlock(blockId) {
        const element = this.blockElements.get(blockId);
        if (!element) return;

        const content = element.querySelector('block-content');
        if (content) {
            content.focus();
            this.currentFocus = blockId;
        }
    }

    handleFocusIn(event) {
        const content = event.target.closest('block-content');
        if (content) {
            const blockId = content.getAttribute('data-block-id');
            this.currentFocus = blockId;
            
            if (window.Events) {
                window.Events.emit('outliner:focus', { blockId });
            }
        }
    }

    handleFocusOut(event) {
        // Optional: Handle focus out events
        if (window.Events) {
            window.Events.emit('outliner:blur', { blockId: this.currentFocus });
        }
    }

    focusPreviousBlock(currentBlock) {
        // Implementation for focusing previous block in hierarchy
        // This would need to traverse the DOM or maintain a flat list
    }

    focusNextBlock(currentBlock) {
        // Implementation for focusing next block in hierarchy
    }

    // Utility methods
    isAtStartOfBlock(element) {
        const selection = window.getSelection();
        return selection.anchorOffset === 0;
    }

    isAtEndOfBlock(element) {
        const selection = window.getSelection();
        return selection.anchorOffset === element.textContent.length;
    }

    // Event handlers for block system integration
    handleBlockChanged(block) {
        const element = this.blockElements.get(block.id);
        if (element) {
            const content = element.querySelector('block-content');
            if (content && content.textContent !== block.content) {
                content.textContent = block.content;
            }
        }
    }

    handleBlockCreated(block) {
        this.blocks.set(block.id, block);
        // DOM will be updated by other methods
    }

    handleBlockDeleted(block) {
        const element = this.blockElements.get(block.id);
        if (element) {
            element.remove();
        }
        this.blocks.delete(block.id);
        this.blockElements.delete(block.id);
    }

    // Drag and drop handlers (simplified)
    handleDragStart(event) {
        const blockItem = event.target.closest('block-item');
        if (!blockItem) return;

        this.dragState = {
            draggedBlockId: blockItem.getAttribute('id'),
            startTime: Date.now()
        };

        blockItem.setAttribute('dragging', 'true');
        event.dataTransfer.effectAllowed = 'move';
    }

    handleDragOver(event) {
        event.preventDefault();
        event.dataTransfer.dropEffect = 'move';
        
        const blockItem = event.target.closest('block-item');
        if (blockItem && this.dragState) {
            // Visual feedback for drop target
            document.querySelectorAll('block-item[drop-target]').forEach(el => {
                el.removeAttribute('drop-target');
            });
            blockItem.setAttribute('drop-target', 'true');
        }
    }

    handleDrop(event) {
        event.preventDefault();
        // Implementation for reordering blocks
        this.clearDragState();
    }

    handleDragEnd(event) {
        this.clearDragState();
    }

    clearDragState() {
        document.querySelectorAll('block-item[dragging]').forEach(el => {
            el.removeAttribute('dragging');
        });
        document.querySelectorAll('block-item[drop-target]').forEach(el => {
            el.removeAttribute('drop-target');
        });
        this.dragState = null;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Outliner };
} else {
    window.FAPOutliner = { Outliner };
}