type:: [[Feature]]
alias:: Graph view
description:: Graph view of your whole graph
platforms:: [[All Platforms]]

- ## Usage
	- Watch your knowledge graph grow by going to your main menu (click your profile picture) -> select "Graph"
- ## Functionality
	- TODO Fill out functionality #docs
- ## Background
	- As you add ideas and thoughts into Logseq and connect them, your knowledge graph grows. Just as your brain generates and connects new neurons to form new knowledge and ideas.
	- These connections are what makes Logseq so powerful as a second brain, we generate and synthesize information much like your own brain does, but better.
	- In Logseq, you can filter and even "remember" loose connections by checking out your linked references. You can see all the times you thought about [[Productivity]] and [[Habit Creation]] in their separate nodes and areas of your graph, but you never thought to connect the two. But since you wrote [[Productivity]] and happened to mention [[Habit Creation]] 4 months ago (in some random journal or essay), that proximity and connection live with you forever, even if you never drew the lines in your own head:
	  ![image.png](/assets/pages_knowledge_graph_1612308816189_0.png){:height 338, :width 468}
	- related terms:
	  {{embed [[term/network of connected ideas]]}}