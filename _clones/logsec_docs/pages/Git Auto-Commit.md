type:: [[Feature]]
platforms:: [[Desktop]]
description:: Automatically save commit changes to a git repository.

- ## Usage
	- You can make Logseq save changes to a git repository at a specified interval.
		- ![version-control-settings-page.png](../assets/version-control-settings-page_1676506969044_0.png)
		- When auto-commit is enabled, each graph's directory will be made into a git repository (if it's not already).
		- Logseq will commit changes at the specified interval of time.
			- Valid intervals are 1 - 600 seconds.
		- Auto-commit *will not* automatically push changes to a remote.
		-