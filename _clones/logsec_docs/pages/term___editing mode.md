- description:
  - Unlike WYSIWYG editors, Logseq offers two modes to display text: editing mode and non-editing mode.
  - Editing mode differs from non-editing mode in these ways:
    - Users can only add or edit text to a block when a block is in editing mode (exception: some operations such as copy and paste allow adding or editing text in non-editing mode).
    - Block text is not rendered when in editing mode.
    - Some information, such as block references and link syntax, are shown only in editing mode.
- produced by:
  - enter editing mode by putting the cursor in a block,
    - either by clicking with the mouse, navigating using the keyboard, or hitting `enter` while a block is highlighted and selected