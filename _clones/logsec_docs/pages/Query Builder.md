type:: [[Feature]]
platforms:: [[All Platforms]]
description:: Build [[Queries]] easily with filters and nested operators

- ## Usage
	- You can try this feature right here!
		- {{query }}
	- Click on the `+` button to add a [query operator](((641c8e5f-f890-4c98-8221-652a4ef0970d))) or a [query filter](((62967225-37d9-46b7-859f-92e0311ab4be))).
	- Select a filter like `page reference` to get a dropdown of valid values for it.
	- After selecting a filter value, you'll see results immediately. You can select an additional filter to filter results even further.
	- You can also select an operator either with `+` or by clicking on the filter label and selecting an operator to wrap the filter with.
- ## Functionality
	- There are two types of queries - blocks and pages. The default is to query by blocks. To change to a pages search, click on the upper right dropdown. This decision has to be made when the query is empty since some operators only work for one type of search.
	- Filters can have different interfaces since they have different abilities. Some filters like `page reference` are a single choice dropdown while other filters like `task` are multiple choice dropdowns. Filters like `property` can also have multiple dropdowns, first to choose the property name and then a value. Note that `Select all` is a special value that selects all values for the property.
	- Any filter or operator can be clicked to have the option of deleting it, replacing it or wrapping it with an operator.