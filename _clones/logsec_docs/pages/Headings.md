type:: [[Feature]]
platforms:: [[All Platforms]] 
description:: Creating headings on a page

- ## Usage
	- There are multiple ways to create a heading:
		- You can use markdown syntax while editing a block, by adding `#` signs at the beginning of the block, followed by a space character. The heading level will be based on the number of signs that you used. For example, `### Title` will be translated into an `<h3>`.
		- You can also use the context menu to convert a block to a heading, or remove it.
		  ![Screenshot from 2024-02-08 20-29-54.png](../assets/Screenshot_from_2023-02-08_20-29-54_1675881023234_0.png)
		- An auto-heading (`HA`) option is also available on the context menu. If you set the heading level to auto, the heading will be based on the nesting level of the block. This can be extremely helpful when we want to format existing pages, because it automatically handles the heading hierarchy.
	- Note that there are only six supported heading levels, even when you use the auto-heading option.
- ## Functionality
	- There are two modes to setting headings - an explicit heading or auto-heading. When choosing between one or the other, the previous mode is no longer active.
	- When switching between an explicit heading and auto-heading, characters like `#` are added or removed to the block.
	-
- ## Additional Links
	- https://www.markdownguide.org/basic-syntax/#headings