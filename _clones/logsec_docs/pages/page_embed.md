title:: Page embed
type:: [[Feature]]
platforms:: [[All Platforms]]
alias:: term/embedded page
tags:: [[embed]]
description:: Allows an entire page to be displayed within another. Also known as transclusion. See [[Block embed]] for block version of this

## Usage
	- Create a page embed by typing `{{{embed [[page name]]}}}` or by using the `/Page embed` command:
	  collapsed:: true
		- type:: [[Command]]
		  name:: Page embed
		  description:: Embeds a page with autocompletion ready for the page
	- For example, embedding [[term/page]] looks like this:
		- {{embed [[term/page]]}}
## Functionality
	- Edits made to the embedded content are also made to the referenced content.
	- Embedded page is marked by a different colored background.
	- Embedding does not contribute to a page's [[Linked References]]