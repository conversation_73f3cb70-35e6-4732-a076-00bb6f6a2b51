- #+BEGIN_TIP
  For people familiar with Roam Research: the Journals page in Logseq is the same as the Daily Notes Page in Roam.
  #+END_TIP
- Every time you open Logseq, you start on the Journals page.
- The [[Journals page]] is the ideal place to enter information in your graph. Every day at midnight a new Journals page is created with that day's date, making it easy to see when you've added what information.
- The Journals page also functions as an area to resurface notes. In the linked references of each day's Journals page you'll see the mentions of that day's date. This is handy for when you want to send your future self reminders or TODOs.
- When it's your first time using an outliner tool like Logseq, we recommend you write everything on the Journals page of that day. Avoid creating pages for everything and scattering your information over unconnected islands. Until you know how to link pages and blocks—and even after—it's best to use your Journals pages for everything.
- Once you feel more comfortable with Logseq, [continue with the lesson on how to create pages.]([[How to create pages in Logseq]])
- If you first want to know more about linking in Logseq, [continue with the lesson on why linking matters.]([[Why linking matters]])