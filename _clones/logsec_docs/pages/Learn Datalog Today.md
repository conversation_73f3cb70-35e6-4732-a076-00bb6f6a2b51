url:: https://www.learndatalogtoday.org/
description:: This page is a static version of https://www.learndatalogtoday.org/ in case this site goes down again.

- ## Intro
	- #+BEGIN_QUOTE
	  **Learn Datalog Today** is an interactive tutorial designed to teach you the [Datomic](http://datomic.com/) dialect of [Datalog](http://en.wikipedia.org/wiki/Datalog). Datalog is a declarative **database query language** with roots in logic programming. Datalog has similar expressive power as [SQL](http://en.wikipedia.org/wiki/Sql).
	  #+END_QUOTE
- ## Table of Contents
	- [Chapter 0: Extensible Data Notation](https://github.com/jonase/learndatalogtoday/blob/master/resources/chapters/chapter-0.md)
	- [Chapter 1: Basic Queries](https://github.com/jonase/learndatalogtoday/blob/master/resources/chapters/chapter-1.md)
	- [Chapter 2: Data patterns](https://github.com/jonase/learndatalogtoday/blob/master/resources/chapters/chapter-2.md)
	- [Chapter 3: Parameterized Queries](https://github.com/jonase/learndatalogtoday/blob/master/resources/chapters/chapter-3.md)
	- [Chapter 4: More Queries](https://github.com/jonase/learndatalogtoday/blob/master/resources/chapters/chapter-4.md)
	- [Chapter 5: Predicates](https://github.com/jonase/learndatalogtoday/blob/master/resources/chapters/chapter-5.md)
	- [Chapter 6: Transformation Functions](https://github.com/jonase/learndatalogtoday/blob/master/resources/chapters/chapter-6.md)
	- [Chapter 7: Aggregates](https://github.com/jonase/learndatalogtoday/blob/master/resources/chapters/chapter-7.md)
	- [Chapter 8: Rules](https://github.com/jonase/learndatalogtoday/blob/master/resources/chapters/chapter-8.md)
	- Note: Chapters have [corresponding exercises]( https://github.com/jonase/learndatalogtoday/tree/master/resources/chapters). Exercises are in EDN and not as easy to follow as the chapters.
- ## Credits
	- Full source for tutorial is at https://github.com/jonase/learndatalogtoday/. The interactive version of this tutorial can be cloned and run locally.