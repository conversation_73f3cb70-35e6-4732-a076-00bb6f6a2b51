alias:: Whiteboard objects, Whiteboard object
type:: [[Class]]
parent:: [[UI Element]]
description:: An element that can be placed on the [[Whiteboard/Canvas]]. Just like [[Blocks]] or [[Pages]], objects can be referenced anywhere in your [[Graph]]
url:: {{docs-base-url Whiteboard%2FObject}}

- produced by:
	- select the object of your choice from the [[Toolbar]] and draw it on the [[Whiteboard/Canvas]]
	- the appearance of the object can be changed via the [[Object Action Bar]]
- example:
	- ![ObjectExamples.mp4](../assets/ObjectExamples_1669389480174_0.mp4)
	- The following objects exist in [[Whiteboards]]:
	  id:: 6380d82c-a061-4ebb-9b5d-8fe6aabbf454
		- [[Pencil]]
			- supports:
				- [[Color swatch]]
				- [[Scale select]]
				- [[Link]]
		- [[Highlight]]
			- supports:
				- [[Color swatch]]
				- [[Scale select]]
				- [[Link]]
		- [[Connector]]
			- supports:
				- [[Color swatch]]
				- [[Scale select]]
				- [[Bold toggle]]
				- [[Italic toggle]]
				- [[Arrow head toggle]]
				- [[Link]]
		- [[Text]]
			- supports:
				- [[Auto resize toggle]]
				- [[Scale select]]
				- [[Color swatch]]
				- [[Bold toggle]]
				- [[Italic toggle]]
				- [[Scale select]]
				- [[Link]]
		- [[Shape]] ([[Rectangle]], [[Circle]], [[Triangle]] )
			- supports:
				- [[Shape select]]
				- [[Color swatch]]
				- [[Fill toggle]]
				- [[Stroke type select]]
				- [[Scale select]]
				- [[Bold toggle]]
				- [[Italic toggle]]
				- [[Link]]
		- [[Logseq Portal]] ([[Block]] / [[Page]] / [[Whiteboard]] )
			- supports:
				- [[Collapse toggle]]
				  id:: 645a7999-ba15-4d17-9285-7319c152cc52
				- [[Auto resize toggle]]
				- [[Color swatch]]
				- [[Scale select]]
				- [[Link]]
		- [[Image]]
			- supports:
				- [[Link]]
		- [[YouTube]]
			- supports:
				- [[Url input]]
				- [[Open embedded url]]
				- [[Link]]
		- [[Tweet]]
			- supports:
				- [[Url input]]
				- [[Open embedded url]]
				- [[Link]]
		- [[iFrame]] (website)
			- supports:
				- [[Reload]]
				- [[Url input]]
				- [[Open embedded url]]
				- [[Link]]
		- [[PDF]]
			- supports:
				- [[Edit]]
				- [[Open embedded url]]