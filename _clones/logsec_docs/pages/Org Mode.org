* description:: Org mode is a markup language popularized by the Emacs editor. Our implementation is parsed by https://github.com/logseq/mldoc. See https://orgmode.org/quickstart.html to learn more about this format.
* Org mode is one of the formats that Logseq supports. Logseq supports standard and some extended syntaxes.
* *Standard* syntax
** ~*Bold*~ -> *Bold*
** ~/Italics/~ -> /Italics/
** ~+Strikethrough+~ -> +Strikethrough+
** ~^^Highlight^^~ -> ^^Highlight^^
** [:div [:code "~Code~"] " -> " [:code "Code"]]
** ~[[https://www.example.com][label]]~ -> [[https://www.example.com][label]]
:PROPERTIES:
:id: 60ab7486-1318-48cc-85bc-02561429e331
:END:
** ~[[https://asset.logseq.com/static/img/logo.png][image]]~ -> [[https://asset.logseq.com/static/img/logo.png][image]]{:height 53, :width 42}
:PROPERTIES:
:id: 60aba888-5cf4-4cb0-94df-1e4a07b3af34
:END:
** and so on ...
* *Extended* syntax
** {{embed ((60ab4582-5a6e-4f3a-84a2-71ae056455a0))}}
** {{embed ((60ab3eb7-c1e8-47ad-8a18-770896a10c5c))}}
** {{embed ((60ab3eb7-2fb1-4148-af2b-9ba2319ef5b6))}}
** {{embed ((60ab3eb7-5a6b-4e6a-90e0-80dd11e47c48))}}
** [[term/page reference with label]]
*** syntax: ~[[page name][display text]]~
:PROPERTIES:
:id: 60ab6d72-9ad0-429f-8673-d13e81a93f23
:END:
** [[Properties]]
*** syntax: ~#+PROPERTY: value~
:PROPERTIES:
:id: 60ab7357-2744-42bc-a8fd-a9c8db3051df
:END:
** [[term/block reference with label]]
*** syntax: ~[[((block-uuid))][display text]]~
:PROPERTIES:
:id: 60ab6f5b-eb43-422b-9e89-0969670af709
:END:
** {{embed ((60ab4582-b191-496a-be39-0a865f9ecece))}}
** {{embed ((60ab5bc7-f196-4f87-91aa-164ac71184ed))}}
** {{embed ((60ab5bc7-f08f-4e7d-a293-add673533700))}}