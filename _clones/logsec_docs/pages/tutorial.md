- Hi, welcome to Logseq!
  heading:: true
- > Logseq is a _privacy-first_, _open-source_ platform for _knowledge_ sharing and management.
- Here are some tips that might be useful:
  id:: 60293d41-1351-40ed-aa00-0e0c12be1175
	- Click to edit any block.
	  Type `Enter` to start a new block.
	  Type `Shift+Enter` to create a new line in the current block.
	  Type `/` to show all the commands.
	  Type `[[` to reference or create a new page.
	  Type `((` to reference a block on any page.
	  Type `Tab` to indent and `Shift+Tab` to unindent a block.
	  Type `/Draw` for free drawing. 
	  Type `<` for more formatting commands (like quotes and tips!)
	  Click the help button at the bottom right to see more keybindings.
- Getting Started
  heading:: true
- **1. Let's create a page** - called [[How to Take Notes]]. Type `[[]]` to create or reference any other page. You can also make a new page in the search bar.
	- You can click it to actually go to that page, or you can `Shift+Click` to open it in the right sidebar! At the bottom, you'll see both _Linked References_ and _Unlinked References_ - every single other time you thought about it inside Logseq.
- **2. Let's reference some blocks** - In [[How to Take Notes]] , you can `Shift+Click` any page or block reference to open it in the right sidebar. Try making some changes on the right sidebar, those referenced blocks will be changed too!
	- ((60293d41-1351-40ed-aa00-0e0c12be1175)) : This is a block reference.
	- ((60293d41-2c37-43f2-aa0e-4e0ad8e38531)) : This is another block reference.
- **3. Embed a Block** - References aren't enough! I wan't to see and edit any block in my entire Graph! You can embed entire blocks and their children by typing `/` and selecting `Block Embed` :
	- {{embed ((6018cc98-67a8-4eb2-bfd7-2affe517a7c7)) }}
- **4. Embed a Page** - What if I want to embed the whole page? The `/` menu has a option for that - `Page Embed` :
	- {{{embed [[How to Take Notes]] }}}
- **5. Use Tags** - Use tags to categorize any page or block, then click on it to see every single time you used it!
	- Check out this #dummy tag.
	  id:: 60293d41-2c37-43f2-aa0e-4e0ad8e38531
- **6. Manage your Tasks + Todos** - Do you support tasks like todo/doing/done and priorities?
	- Yes, type press `Ctrl/Cmd + Enter` inside a block to cycle through different TODO statuses or `/` and pick your favorite todo keyword or priority (A/B/C).
	- NOW [#A] A dummy tutorial on "How to Take Notes"
	- DONE Create Logseq Getting Started
	  todo:: 1612237041309
	  done:: 1612237041727
	- CANCELED [#C] Write a page with more than 1000 blocks
- **7. Step back and admire your growing second brain** - After you've created and connected some pages and ideas, check out your digital brain growing!
	- Click on the Graph in the left sidebar!
- Need more?
  heading:: true
	- Check out this awesome video by [:a {:href "https://twitter.com/EdTravelling" :target "_blank"} "@EdTravelling"], which shows you how to install Logseq app on your GitHub repo and other cool features like graph view.
	  
	  [:div.videoWrapper.mb-4
	        [:iframe
	         {:allowFullScreen "allowfullscreen"
	          :allow
	          "accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
	        :frameBorder "0"
	        :src "https://www.youtube.com/embed/Vw-x7yTTO0s"
	        :height "367"
	        :width "653"}]]
	- this carefully crafted [[Glossary]] might be helpful
	- Check out the full documentation in the Right Sidebar ->