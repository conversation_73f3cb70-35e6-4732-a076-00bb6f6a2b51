title:: Block embed
type:: [[Feature]]
platforms:: [[All Platforms]]
alias:: term/embedded block
tags:: embed
description:: Also known as block embedding, this allows a portion of a page to be displayed within another. Edits made to the embedded content are also made in the referenced content.

## Usage
	- Create a block embed by typing `{{{embed ((block id))}}}` or typing the `/Block embed` command:
	  collapsed:: true
	  type:: [[Command]]
	  name:: Block embed
	  description:: Embeds a block with autocompletion ready for the block
	- An embed example
	  {{{embed ((5fbf4fbf-82c5-4d81-ba82-b66726bda00c)) }}}
## Functionality
	- Embedded block is marked by a different colored background.
	- Auto-completion is available for blocks and will automatically generate and insert a block id