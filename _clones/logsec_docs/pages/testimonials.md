---
title: Testimonials
---

- id:: 6071c223-b0ed-4235-80b2-f5e44d3679b9
  #+BEGIN_NOTE
  This page lists generous things that our community have said about Logseq. Feel free to [add](https://github.com/logseq/docs/edit/master/pages/testimonials.md) yours or messaging tienson at <<EMAIL>>
  #+END_NOTE
- [<PERSON><PERSON><PERSON>](https://github.com/ssakhavi)
  id:: 607454a1-a6a5-4356-af6d-ae5ed0a2051a
  #+BEGIN_QUOTE
  Since I started my new job as a software manager, I realized I have to take more and more notes. Initially, I started out with notion but I just couldn't. Too much clutter.
  After Notion, I found Logseq and I felt this is for me. I started taking notes and as a person that didn't take notes before, I saw that how much the structure of Logseq was and is helping me.
  After finding Logseq, I tries different note-taking apps: <PERSON>ynalist, <PERSON><PERSON>y, Remnote, Obsidian, Foam, <PERSON>dron, <PERSON><PERSON><PERSON><PERSON>,... But at the end, I would always find my way back to Logseq. I've never used Roam before, I don't know how it is and I don't think I have to try it because I see how good Logseq is.
  Great job Logseq team and all the best. Please make the app stable and I hope with the help of the community, you can get rid of the bugs and catch up with your technical debt.
  #+END_QUOTE
- Luke {{cloze @white999 on Discord}} 
  id:: 607454a1-043d-4beb-b86e-c513a68ad47e
  #+BEGIN_QUOTE
  I stumbled upon Logseq just this week. Initially I had seen Athens Research getting some attention on Twitter so I gave it a try. It was pretty cool but still rather buggy and maybe shallow of me but I dont like the branding too much. So in watching some videos on Athens, I saw the word Logseq being thrown around as an app doing a similar thing in taking the best ideas from Roam but putting a more open source, privacy spin on it. So decided to YouTube it... There really is'nt much content yet (hope this gets the attention it deserves soon) but what I did see looked super interesting. Over the past 6 months I have been trying loads of note-taking apps to see what I am needing for my personal workflow. I started with Notion, then moved onto Obsidian after Notion was going offline a lot and generally being super slow. Obsidian was and is great. I really value that the files are stored locally and my own, portable. Privacy at the core is super valuable as we move into this age of mass data collection. My files are not locked in some proprietary app on the cloud that could disappear and leave us in the dust. Then I tried Amplenote as I wanted to incorporate tasks and todos more into my workflow. I was really missing markdown and a more outliner format though in Amplenote. Now I am on Logseq and I have to say, this is the most simple app that is ticking all the boxes for me.
  - [[Privacy]] - local storage
  - Longevity - .md files
  - Outliner format
  - Templates
  - Graph - build notes bottom up and link together organically
  - Todos - that can be queried across notes
  - Online and desktop apps - portable
  - Super lightweight and responsive
  
  Of course I am sure what most people say will tip this over the edge will be the ability to access the app and my notes on mobile. Fully appreciate though this is still in Alpha and quickly developing. 
  Thanks so much for making such a brilliant app. Looking forward to see where this goes in the future.
  #+END_QUOTE
- [Pi-Yueh Chuang](https://github.com/piyueh)
  id:: 607d1394-3b32-4613-9acf-9bade8ad7817
  #+BEGIN_QUOTE
  I jumped the boat from Roam Research to Logseq because I wanted something that
  - is open-source,
  - uses local filesystem without a paywall,
  - does not need an account, and
  - provides a seamless transition from Roam Research.
  Logseq has them all. I believe most Roam Research users will agree that Logseq is a fantastic alternative.
  
  I had also tried some other alternatives, but they don't fit my need. They provide fancy user interfaces that distract me from simply taking notes. I just want a desktop app that helps take notes of my random thoughts and <u><b><i>links</i></b></u> these snippets of thoughts. But don't get me wrong: Logseq is very powerful. It's just that Logseq hides advanced functions in the user interface and keeps the look clean. Whenever a user needs an advanced feature that other alternatives have, he/she can still find it in Logseq. And when a user can't find the needed feature, she/he will be impressed by how responsive/accessible Logseq's development team is!
  #+END_QUOTE