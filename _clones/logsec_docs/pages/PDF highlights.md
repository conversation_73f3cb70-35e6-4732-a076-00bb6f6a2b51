type:: [[Feature]]
platforms:: [[Desktop]]

- <PERSON><PERSON><PERSON> [[<PERSON><PERSON>]] would like to rename to PDF Reader #docs
- <PERSON>D<PERSON> Organize this page into a feature page #docs
- #+BEGIN_NOTE
  This only works on the desktop app at the moment.
  #+END_NOTE
- **How to add a PDF file?**
	- NOTE: PDF files get stored inside your graph's assets folder. There's currently no automated way to remove them even if unlinked. You might be able to remove them manually to reclaim space.
	- There're two ways to add PDF files to your graph:
		- 1. Create a new block in any page, drag your PDF file to the new block.
		- 2. Type '/upload an asset' and choose your PDF file.
- **How to highlight text?**
	- Click to select some text and choose your favorite color.
		- ![CleanShot 2021-08-06 at 20.32.52.png](../assets/CleanShot_202021-08-06_20at_2020.32.52_1628253194728_0.png)
	- Paste (`Cmd/Ctrl + v`) your highlighted reference to any block if you need.
- **How to highlight area?**
	- Press either `Cmd` or `Shift`, click to select an area and choose your favorite color.
		- ![CleanShot 2021-08-06 at 20.47.34.png](../assets/CleanShot_202021-08-06_20at_2020.47.34_1628254091556_0.png)
	- Paste (`Cmd/Ctrl + v`) your highlighted reference to any block if you need.
- **Do you have a dark theme for the PDF viewer?**
	- Yes, there are **three** themes.
- **Do you support outline?**
	- Yes.
	  ![CleanShot 2021-08-06 at 20.48.49.png](../assets/CleanShot_202021-08-06_20at_2020.48.49_1628254163734_0.png){:height 563, :width 325}
