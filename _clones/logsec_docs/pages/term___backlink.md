- description:
	- A Logseq page link for a page that links to a currently displayed page, whether it has focus or not.
	- A list of backlinks are presented in a particular location or frame, and “backlinks” refers to the page links in this area only.
- produced by:
	- Automatically created by Logseq.
	- Create a backlink by  surrounding a word or sentence with a pair of square brackets e.g `[[logseq]]`. Typing `[[` will initiate page auto-completion.
- example:
	- this is a backlink to [[Glossary]]
-
  #+BEGIN_TIP 
  * Clicking a backlink will open the linked page in the main window
  * Shift + clicking a backlink will open the linked page in the sidebar
  * Opening a backlink to a page which does not currently exist will create a new page with the backlink text as its title
  * The [[Unlinked References]] list located below [[Linked References]] on a page displays potential backlinks, files which refer to the page but do not have a relevant backlink present.
   #+END_TIP