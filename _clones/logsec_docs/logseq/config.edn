{:text-formats [],
 :all-pages-public? true
 :default-home {:page "Contents"}

 ;; Currently, we support either "Markdown" or "Org".
 ;; This can overwrite your global preference so that
 ;; maybe your personal preferred format is Org but you'd
 ;; need to use Markdown for some projects.
 ;; :preferred-format ""
 :preferred-format "Markdown"
 :feature/enable-grammarly? false

 ;; The app will ignore those directories or files.
 ;; E.g. "/archived" "/test.md"
 :hidden ["script" "README.md" "CONTRIBUTING.md" "LICENSE.md" "db-version.md" "db-version-changes.md"]

 ;; When creating the new journal page, the app will use your template content here.
 ;; Example for Markdown users: "## [[Work]]\n###\n## [[Family]]\n###\n
 ;; Example for Org mode users: "** [[Work]]\n***\n** [[Family]]\n***\n
 :default-templates
 {:journals ""}

 ;; The app will show those queries in today's journal page,
 ;; the "NOW" query asks the tasks which need to be finished "now",
 ;; the "NEXT" query asks the future tasks.

 ;; Add your own commands to speedup.
 ;; E.g. [["js" "Javascript"]]
 :commands
 []

 ;; Macros replace texts and will make you more productive.
 ;; For example:
 ;; Add this to the macros below:
 ;; {"poem" "Rose is $1, violet's $2. Life's ordered: Org assists you."}
 ;; input "{{{poem(red,blue)}}}"
 ;; becomes
 ;; Rose is red, violet's blue. Life's ordered: Org assists you.
 :macros
 {"docs-base-url" "https://docs.logseq.com/#/page/$1"
  "query-done" "{{query (and (task done) $1)}}"
  "mark" "[:mark \"$1\"]"}
 :feature/enable-block-time? true
 :feature/enable-timetracking? true
 :feature/enable-journals? false
 :ui/show-brackets? true
 :feature/enable-encryption? false
 :markdown/version 2
 :favorites ["Getting started" "Changelog" "Videos"]
 :file/name-format :triple-lowbar
 :feature/enable-whiteboards? true}
