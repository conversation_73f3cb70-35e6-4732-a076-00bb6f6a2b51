* Saturday, 05/30/2020
** Changelog
*** Add custom datalog query support.
    1. Add multiple queries to the journals page:
       For example, to see all your todos for the last 2 days in the home page,
       add the content below to your ~logseq.edn~:
       #+BEGIN_SRC clojure
        :default-queries
        {:journals [{:title "TODO"
                     :query "[[:find (pull ?h [*])
                              :in $ ?start ?today
                              :where
                              [?h :heading/marker ?marker]
                              [?h :heading/page ?p]
                              [?p :page/journal? true]
                              [?p :page/journal-day ?d]
                              [(>= ?d ?start)]
                              [(< ?d ?today)]
                              [(contains? #{\"TODO\" \"DOING\" \"WAITING\" \"WAIT\"} ?marker)]]
                             :2d
                             :today]"}]}
       #+END_SRC
    2. Add a query block to any pages:
       For example:

  #+BEGIN_SRC clojure
  {:query
   [:find (pull ?h [*])
    :where
    [?h :heading/content ?content]
    [?h :heading/page ?p]
    [?p :block/name "jun 1st, 2020"]]}
    #+END_SRC

  #+BEGIN_QUERY
  {:query
   [:find (pull ?h [*])
    :where
    [?h :heading/content ?content]
    [?h :heading/page ?p]
    [?p :block/name "jun 1st, 2020"]]}
  #+END_QUERY
