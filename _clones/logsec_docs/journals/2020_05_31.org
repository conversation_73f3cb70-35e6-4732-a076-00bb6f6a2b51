* Sunday, 05/31/2020
** Changelog
*** Custom journal date format support. [[Features]]
:PROPERTIES:
:id: 60acdeb9-aa65-492b-8398-d4d65c1631c1
:END:

    You can set the ~:date-formatter~ in ~logseq.edn~.
    The default formatter is ~"MMM do, yyyy"~, which will be something like
    ~Jun 1st, 2020~.
*** Templates support . [[Features]]
    You can set the option ~:default-templates~ in the file ~logseq.edn~.
**** For Markdown users
     #+BEGIN_SRC clojure
       :default-templates
       {:journals "## [[Work]]
       ###
       ## [[Family]]
       ###
       "}
     #+END_SRC
**** For Org mode users
     #+BEGIN_SRC clojure
       :default-templates
       {:journals "** [[Work]]
       ,***
       ,** [[Family]]
       ,***
       "}
     #+END_SRC
** Fixed issues
*** Org mode auto link parsing.
*** Markdown definition parsing (it supports both multiple definitions and multiple lines now).
*** Footnote parsing.