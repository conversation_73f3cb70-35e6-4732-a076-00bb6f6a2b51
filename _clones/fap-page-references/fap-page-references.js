/**
 * FAP Page References - [[Page]] linking and navigation system (Functional Style)
 * 
 * Functional approach to page reference management including:
 * - Page creation and management functions
 * - [[Page]] reference extraction and resolution
 * - Backlink tracking and display
 * - Page navigation and discovery
 * - Integration with block system for seamless linking
 */

// State management through closure
const createPageReferenceSystem = (options = {}) => {
    const defaultOptions = {
        autoCreatePages: true,
        caseSensitive: false,
        maxBacklinks: 100,
        pageNameValidation: true,
        ...options
    };
    
    let state = {
        options: defaultOptions,
        pages: new Map(), // Page name -> Page object
        backlinks: new Map(), // Page name -> Set of block IDs that reference it
        pageBlocks: new Map(), // Page name -> Set of block IDs that belong to it
        referenceCache: new Map() // Cache for reference lookups
    };
    
    // Pure functions for validation
    const isValidPageName = (pageName) => {
        if (!state.options.pageNameValidation) return true;
        
        return pageName.length > 0 && 
               pageName.length <= 100 && 
               !pageName.includes('\n') && 
               !pageName.includes('\t');
    };
    
    const normalizePage = (pageName) => {
        return state.options.caseSensitive ? pageName : pageName.toLowerCase();
    };
    
    // Extract [[Page]] references from text content
    const extractPageReferences = (content) => {
        const pageRefs = [];
        const regex = /\[\[([^\]]+)\]\]/g;
        let match;
        
        while ((match = regex.exec(content)) !== null) {
            let pageName = match[1].trim();
            pageName = normalizePage(pageName);
            
            if (isValidPageName(pageName)) {
                pageRefs.push(pageName);
            }
        }
        
        return [...new Set(pageRefs)]; // Remove duplicates
    };
    
    // Get page name from block (if block represents a page)
    const getPageNameFromBlock = (block) => {
        // Simple heuristic: if block ID starts with 'page-' or has page metadata
        if (block.id.startsWith('page-')) {
            return normalizePage(block.id.substring(5)); // Remove 'page-' prefix
        }
        
        // Check if block has page property
        if (block.properties && block.properties.page) {
            return normalizePage(block.properties.page);
        }
        
        // Check if block content looks like a page title (starts with # )
        if (block.content.startsWith('# ')) {
            const title = block.content.substring(2).trim();
            if (isValidPageName(title)) {
                return normalizePage(title);
            }
        }
        
        return null;
    };
    
    // Create a new page
    const createPage = (pageName, options = {}) => {
        pageName = normalizePage(pageName);
        
        if (state.pages.has(pageName)) {
            return state.pages.get(pageName);
        }
        
        const page = {
            name: pageName,
            displayName: options.displayName || pageName,
            created: new Date().toISOString(),
            updated: new Date().toISOString(),
            blocks: new Set(),
            backlinks: new Set(),
            properties: options.properties || {},
            exists: options.exists !== false // Default to true unless explicitly false
        };
        
        state.pages.set(pageName, page);
        
        if (window.Events) {
            window.Events.emit('page:created', page);
        }
        
        console.log(`🔗 Page created: "${pageName}"`);
        return page;
    };
    
    // Get page by name
    const getPage = (pageName) => {
        return state.pages.get(normalizePage(pageName));
    };
    
    // Get all pages
    const getAllPages = () => {
        return Array.from(state.pages.values());
    };
    
    // Add backlink from block to page
    const addBacklink = (pageName, blockId) => {
        pageName = normalizePage(pageName);
        
        if (!state.backlinks.has(pageName)) {
            state.backlinks.set(pageName, new Set());
        }
        
        state.backlinks.get(pageName).add(blockId);
        
        // Update page object if it exists
        const page = state.pages.get(pageName);
        if (page) {
            page.backlinks.add(blockId);
            page.updated = new Date().toISOString();
        }
    };
    
    // Remove backlink
    const removeBacklink = (pageName, blockId) => {
        pageName = normalizePage(pageName);
        
        const backlinks = state.backlinks.get(pageName);
        if (backlinks) {
            backlinks.delete(blockId);
            
            // Clean up empty backlink sets
            if (backlinks.size === 0) {
                state.backlinks.delete(pageName);
            }
        }
        
        // Update page object
        const page = state.pages.get(pageName);
        if (page) {
            page.backlinks.delete(blockId);
            page.updated = new Date().toISOString();
        }
    };
    
    // Add block to page (for blocks that belong to a page)
    const addBlockToPage = (pageName, blockId) => {
        pageName = normalizePage(pageName);
        
        if (!state.pageBlocks.has(pageName)) {
            state.pageBlocks.set(pageName, new Set());
        }
        
        state.pageBlocks.get(pageName).add(blockId);
        
        // Update page object
        const page = state.pages.get(pageName);
        if (page) {
            page.blocks.add(blockId);
            page.updated = new Date().toISOString();
        }
    };
    
    // Get backlinks for a page
    const getBacklinks = (pageName) => {
        pageName = normalizePage(pageName);
        
        const backlinks = state.backlinks.get(pageName);
        if (!backlinks) return [];
        
        // Get actual block objects
        const blocks = [];
        if (window.FAPBlockCore && window.FAPBlockCore.BlockStore) {
            const { BlockStore } = window.FAPBlockCore;
            backlinks.forEach(blockId => {
                const block = BlockStore.getBlock(blockId);
                if (block) {
                    blocks.push(block);
                }
            });
        }
        
        return blocks;
    };
    
    // Get blocks that belong to a page
    const getPageBlocks = (pageName) => {
        pageName = normalizePage(pageName);
        
        const blockIds = state.pageBlocks.get(pageName);
        if (!blockIds) return [];
        
        // Get actual block objects
        const blocks = [];
        if (window.FAPBlockCore && window.FAPBlockCore.BlockStore) {
            const { BlockStore } = window.FAPBlockCore;
            blockIds.forEach(blockId => {
                const block = BlockStore.getBlock(blockId);
                if (block) {
                    blocks.push(block);
                }
            });
        }
        
        return blocks;
    };
    
    // Search pages by name
    const searchPages = (query) => {
        const results = [];
        const lowerQuery = query.toLowerCase();
        
        state.pages.forEach((page, pageName) => {
            const searchName = state.options.caseSensitive ? pageName : pageName.toLowerCase();
            const searchDisplay = state.options.caseSensitive ? page.displayName : page.displayName.toLowerCase();
            
            let score = 0;
            
            // Exact match gets highest score
            if (searchName === lowerQuery || searchDisplay === lowerQuery) {
                score = 100;
            }
            // Starts with query
            else if (searchName.startsWith(lowerQuery) || searchDisplay.startsWith(lowerQuery)) {
                score = 50;
            }
            // Contains query
            else if (searchName.includes(lowerQuery) || searchDisplay.includes(lowerQuery)) {
                score = 25;
            }
            
            if (score > 0) {
                results.push({
                    page: page,
                    score: score,
                    matchType: score === 100 ? 'exact' : score === 50 ? 'prefix' : 'contains'
                });
            }
        });
        
        // Sort by score (highest first)
        results.sort((a, b) => b.score - a.score);
        
        return results;
    };
    
    // Resolve page reference (get page or create if needed)
    const resolvePage = (pageName) => {
        pageName = normalizePage(pageName);
        
        let page = state.pages.get(pageName);
        
        if (!page && state.options.autoCreatePages) {
            page = createPage(pageName, { exists: false });
        }
        
        return page;
    };
    
    // Process a block's references to update page index
    const processBlockReferences = (block) => {
        // Extract page references from block
        const pageRefs = extractPageReferences(block.content);
        
        pageRefs.forEach(pageName => {
            // Create page if it doesn't exist and auto-create is enabled
            if (!state.pages.has(pageName) && state.options.autoCreatePages) {
                createPage(pageName);
            }
            
            // Add backlink
            addBacklink(pageName, block.id);
        });
        
        // Check if this block represents a page (based on naming convention or metadata)
        const blockPageName = getPageNameFromBlock(block);
        if (blockPageName) {
            addBlockToPage(blockPageName, block.id);
        }
    };
    
    // Remove all references for a specific block
    const removeBlockReferences = (blockId) => {
        // Remove from backlinks
        state.backlinks.forEach((blockSet, pageName) => {
            if (blockSet.has(blockId)) {
                blockSet.delete(blockId);
                
                // Update page object
                const page = state.pages.get(pageName);
                if (page) {
                    page.backlinks.delete(blockId);
                    page.updated = new Date().toISOString();
                }
                
                // Clean up empty sets
                if (blockSet.size === 0) {
                    state.backlinks.delete(pageName);
                }
            }
        });
        
        // Remove from page blocks
        state.pageBlocks.forEach((blockSet, pageName) => {
            if (blockSet.has(blockId)) {
                blockSet.delete(blockId);
                
                // Update page object
                const page = state.pages.get(pageName);
                if (page) {
                    page.blocks.delete(blockId);
                    page.updated = new Date().toISOString();
                }
                
                // Clean up empty sets
                if (blockSet.size === 0) {
                    state.pageBlocks.delete(pageName);
                }
            }
        });
    };
    
    // Rebuild page index from all blocks
    const rebuildPageIndex = () => {
        state.pages.clear();
        state.backlinks.clear();
        state.pageBlocks.clear();
        state.referenceCache.clear();
        
        if (window.FAPBlockCore && window.FAPBlockCore.BlockStore) {
            const { BlockStore } = window.FAPBlockCore;
            const blocks = BlockStore.getAllBlocks();
            
            if (blocks && blocks.length > 0) {
                blocks.forEach(processBlockReferences);
                
                console.log(`🔗 Page index built: ${state.pages.size} pages, ${state.backlinks.size} backlink entries`);
            } else {
                console.log('🔗 Page index built: 0 blocks found in BlockStore');
            }
            
            if (window.Events) {
                window.Events.emit('pages:index-rebuilt', { 
                    pageCount: state.pages.size,
                    backlinkCount: state.backlinks.size
                });
            }
        } else {
            console.log('🔗 Page index build failed: BlockStore not available');
        }
    };
    
    // Get page statistics
    const getPageStats = (pageName) => {
        const page = getPage(pageName);
        if (!page) return null;
        
        return {
            name: page.name,
            displayName: page.displayName,
            exists: page.exists,
            blockCount: page.blocks.size,
            backlinkCount: page.backlinks.size,
            created: page.created,
            updated: page.updated,
            properties: page.properties
        };
    };
    
    // Get system statistics
    const getSystemStats = () => {
        const existingPages = Array.from(state.pages.values()).filter(p => p.exists).length;
        const phantomPages = Array.from(state.pages.values()).filter(p => !p.exists).length;
        const totalBacklinks = Array.from(state.backlinks.values()).reduce((sum, set) => sum + set.size, 0);
        
        return {
            totalPages: state.pages.size,
            existingPages: existingPages,
            phantomPages: phantomPages,
            totalBacklinks: totalBacklinks,
            averageBacklinks: state.pages.size > 0 ? Math.round(totalBacklinks / state.pages.size * 10) / 10 : 0
        };
    };
    
    // Event handlers for block system integration
    const handleBlockCreated = (block) => {
        console.log(`🔗 Processing references for new block: ${block.id}`);
        processBlockReferences(block);
        state.referenceCache.clear();
    };
    
    const handleBlockChanged = (block) => {
        console.log(`🔗 Updating references for changed block: ${block.id}`);
        // Remove old references for this block
        removeBlockReferences(block.id);
        // Add new references
        processBlockReferences(block);
        state.referenceCache.clear();
    };
    
    const handleBlockDeleted = (block) => {
        console.log(`🔗 Removing references for deleted block: ${block.id}`);
        removeBlockReferences(block.id);
        state.referenceCache.clear();
    };
    
    const handleStoreChanged = (data) => {
        console.log(`🔗 Store changed: ${data.action} for block ${data.block.id}`);
        if (data.action === 'clear') {
            rebuildPageIndex();
        }
    };
    
    // Force rebuild of page index (for debugging)
    const forceRebuildIndex = () => {
        console.log('🔗 Forcing page index rebuild...');
        rebuildPageIndex();
        return {
            pages: state.pages.size,
            backlinks: state.backlinks.size
        };
    };
    
    // Set up event listeners for block system integration
    const setupEventListeners = () => {
        if (window.Events) {
            window.Events.on('block:changed', handleBlockChanged);
            window.Events.on('block:created', handleBlockCreated);
            window.Events.on('block:deleted', handleBlockDeleted);
            window.Events.on('store:changed', handleStoreChanged);
        }
    };
    
    // Initialize the system
    const init = () => {
        setupEventListeners();
        rebuildPageIndex();
        console.log('🔗 Page Reference System initialized (functional)');
    };
    
    // Return public API
    return {
        // Initialization
        init,
        
        // Core functions
        rebuildPageIndex,
        processBlockReferences,
        extractPageReferences,
        
        // Page management
        createPage,
        getPage,
        getAllPages,
        resolvePage,
        searchPages,
        
        // Backlink management
        addBacklink,
        removeBacklink,
        getBacklinks,
        
        // Page block management
        addBlockToPage,
        getPageBlocks,
        
        // Statistics and utilities
        getPageStats,
        getSystemStats,
        forceRebuildIndex,
        
        // Validation
        isValidPageName,
        getPageNameFromBlock,
        
        // State access (for debugging)
        getState: () => state
    };
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { createPageReferenceSystem };
} else {
    window.FAPPageReferences = { createPageReferenceSystem };
}