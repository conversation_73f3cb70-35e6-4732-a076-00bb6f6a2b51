# FAP Page References - [[Page]] Linking System

## Overview

The FAP Page References package provides a comprehensive [[Page]] linking and navigation system for knowledge management applications. It enables automatic page creation, backlink tracking, and seamless navigation between related content.

## Features

### Core Functionality
- **[[Page]] Reference Extraction**: Automatically detects `[[Page Name]]` syntax in content
- **Automatic Page Creation**: Creates pages on-demand when referenced
- **Backlink Tracking**: Maintains bidirectional links between pages and content
- **Page Search**: Fast search across all pages with relevance ranking
- **Phantom Pages**: Handles referenced but not yet created pages
- **Event Integration**: Real-time updates via event system

### Advanced Features
- **Case Sensitivity Options**: Configurable case handling for page names
- **Page Validation**: Customizable page name validation rules
- **Statistics & Analytics**: Comprehensive page and system statistics
- **Cache Management**: Optimized performance with intelligent caching
- **Debug Support**: Built-in debugging and inspection tools

## Installation

### Basic Setup
```html
<!-- Include dependencies -->
<script src="../fap-event-system/fap-event-system.js"></script>
<script src="../fap-block-core/fap-block-core.js"></script>

<!-- Include page references -->
<script src="fap-page-references.js"></script>
<link rel="stylesheet" href="fap-page-references.css">
```

### Initialization
```javascript
// Initialize event system
const eventSystem = window.FAPEventSystem.createEventSystem();
window.Events = eventSystem;

// Initialize block store
const blockStore = window.FAPBlockCore.createBlockStore();
window.FAPBlockCore.BlockStore = blockStore;

// Initialize page reference system
const pageSystem = window.FAPPageReferences.createPageReferenceSystem({
    autoCreatePages: true,
    caseSensitive: false,
    pageNameValidation: true,
    maxBacklinks: 100
});

pageSystem.init();
```

## API Reference

### Core Functions

#### `createPageReferenceSystem(options)`
Creates a new page reference system instance.

**Parameters:**
- `options` (Object): Configuration options
  - `autoCreatePages` (Boolean): Auto-create pages when referenced (default: true)
  - `caseSensitive` (Boolean): Case-sensitive page names (default: false)
  - `pageNameValidation` (Boolean): Enable page name validation (default: true)
  - `maxBacklinks` (Number): Maximum backlinks per page (default: 100)

**Returns:** Page reference system instance

#### `init()`
Initializes the system and sets up event listeners.

#### `extractPageReferences(content)`
Extracts [[Page]] references from text content.

**Parameters:**
- `content` (String): Text content to analyze

**Returns:** Array of page names found in content

### Page Management

#### `createPage(pageName, options)`
Creates a new page.

**Parameters:**
- `pageName` (String): Name of the page
- `options` (Object): Page options
  - `displayName` (String): Display name for the page
  - `exists` (Boolean): Whether page actually exists (default: true)
  - `properties` (Object): Additional page properties

**Returns:** Page object

#### `getPage(pageName)`
Retrieves a page by name.

**Parameters:**
- `pageName` (String): Name of the page

**Returns:** Page object or undefined

#### `getAllPages()`
Gets all pages in the system.

**Returns:** Array of page objects

#### `searchPages(query)`
Searches pages by name with relevance scoring.

**Parameters:**
- `query` (String): Search query

**Returns:** Array of search results with scores

#### `resolvePage(pageName)`
Gets existing page or creates it if auto-creation is enabled.

**Parameters:**
- `pageName` (String): Name of the page

**Returns:** Page object

### Backlink Management

#### `addBacklink(pageName, blockId)`
Adds a backlink from a block to a page.

**Parameters:**
- `pageName` (String): Target page name
- `blockId` (String): Source block ID

#### `removeBacklink(pageName, blockId)`
Removes a backlink.

**Parameters:**
- `pageName` (String): Target page name
- `blockId` (String): Source block ID

#### `getBacklinks(pageName)`
Gets all blocks that link to a page.

**Parameters:**
- `pageName` (String): Target page name

**Returns:** Array of block objects

### Page Block Management

#### `addBlockToPage(pageName, blockId)`
Associates a block with a page (for blocks that belong to a page).

**Parameters:**
- `pageName` (String): Page name
- `blockId` (String): Block ID

#### `getPageBlocks(pageName)`
Gets all blocks that belong to a page.

**Parameters:**
- `pageName` (String): Page name

**Returns:** Array of block objects

### Statistics & Utilities

#### `getPageStats(pageName)`
Gets detailed statistics for a specific page.

**Parameters:**
- `pageName` (String): Page name

**Returns:** Page statistics object

#### `getSystemStats()`
Gets overall system statistics.

**Returns:** System statistics object

#### `forceRebuildIndex()`
Forces a complete rebuild of the page index.

**Returns:** Object with rebuild results

## Usage Examples

### Basic Page Reference Extraction
```javascript
const content = "Today we discussed [[Project Alpha]] and [[Database Migration]].";
const references = pageSystem.extractPageReferences(content);
console.log(references); // ['project alpha', 'database migration']
```

### Creating and Managing Pages
```javascript
// Create a new page
const page = pageSystem.createPage('My Project', {
    displayName: 'My Project',
    properties: { category: 'work' }
});

// Get page information
const stats = pageSystem.getPageStats('My Project');
console.log(`Page has ${stats.backlinkCount} backlinks`);
```

### Search Functionality
```javascript
// Search for pages
const results = pageSystem.searchPages('project');
results.forEach(result => {
    console.log(`${result.page.name} (score: ${result.score})`);
});
```

### Working with Backlinks
```javascript
// Get all pages that link to a specific page
const backlinks = pageSystem.getBacklinks('Project Alpha');
backlinks.forEach(block => {
    console.log(`Block ${block.id}: ${block.content}`);
});
```

## HTML Elements

The package provides semantic HTML elements for building page reference interfaces:

### Page Links
```html
<page-link data-page="project-alpha" data-exists="true">Project Alpha</page-link>
<page-link data-page="phantom-page" data-exists="false">Phantom Page</page-link>
```

### Search Interface
```html
<page-search>
    <page-search-input placeholder="Search pages..."></page-search-input>
    <page-search-results>
        <page-search-result data-match-type="exact">
            <page-search-result-name>Project Alpha</page-search-result-name>
            <page-search-result-meta>5 blocks, 3 backlinks</page-search-result-meta>
        </page-search-result>
    </page-search-results>
</page-search>
```

### Backlinks Display
```html
<page-backlinks>
    <page-backlinks-header>Backlinks (3)</page-backlinks-header>
    <page-backlinks-list>
        <page-backlink-item>
            <page-backlink-content>Meeting notes about [[Project Alpha]]</page-backlink-content>
            <page-backlink-meta>Block ID: block-123</page-backlink-meta>
        </page-backlink-item>
    </page-backlinks-list>
</page-backlinks>
```

### Statistics Display
```html
<page-stats>
    <page-stats-header>System Statistics</page-stats-header>
    <page-stats-grid>
        <page-stat-item>
            <page-stat-value>42</page-stat-value>
            <page-stat-label>Total Pages</page-stat-label>
        </page-stat-item>
    </page-stats-grid>
</page-stats>
```

## Events

The system emits and listens for various events:

### Emitted Events
- `page:created` - When a new page is created
- `pages:index-rebuilt` - When the page index is rebuilt

### Listened Events
- `block:created` - Process references in new blocks
- `block:changed` - Update references when blocks change
- `block:deleted` - Remove references from deleted blocks
- `store:changed` - Handle store-level changes

## Configuration Options

### Page Name Validation
```javascript
const pageSystem = createPageReferenceSystem({
    pageNameValidation: true, // Enable validation
    // Custom validation can be added by modifying isValidPageName function
});
```

### Case Sensitivity
```javascript
const pageSystem = createPageReferenceSystem({
    caseSensitive: false // Case-insensitive page names (default)
});
```

### Auto Page Creation
```javascript
const pageSystem = createPageReferenceSystem({
    autoCreatePages: true // Automatically create referenced pages
});
```

## Integration with Other Packages

### Block System Integration
The page references system automatically integrates with the block system to:
- Extract references from block content
- Track which blocks reference which pages
- Update references when blocks change
- Clean up references when blocks are deleted

### Search Engine Integration
Works seamlessly with the search engine package:
- Pages can be searched alongside blocks
- Page references enhance search relevance
- Backlinks provide additional search context

### Event System Integration
Uses the event system for:
- Real-time updates when content changes
- Loose coupling with other packages
- Debugging and monitoring capabilities

## Performance Considerations

### Caching
- Reference extraction results are cached
- Page lookups use efficient Map structures
- Cache is automatically cleared when content changes

### Memory Management
- Automatic cleanup of empty backlink sets
- Efficient Set operations for backlink tracking
- Configurable limits on backlinks per page

### Scalability
- Optimized for large numbers of pages and references
- Incremental updates rather than full rebuilds
- Debounced operations for better performance

## Browser Support

- **Modern Browsers**: Full support for Chrome, Firefox, Safari, Edge
- **ES6+ Features**: Uses modern JavaScript features
- **No Dependencies**: Pure vanilla JavaScript implementation
- **Responsive Design**: Mobile-friendly CSS styling

## Troubleshooting

### Common Issues

**Pages not being created automatically:**
- Check that `autoCreatePages` is set to `true`
- Verify that page names pass validation rules
- Ensure the block system is properly initialized

**Backlinks not updating:**
- Confirm event system is properly connected
- Check that blocks are being processed through the system
- Verify block content contains valid [[Page]] syntax

**Search not working:**
- Ensure pages exist in the system
- Check that page names match search query format
- Verify case sensitivity settings

### Debug Mode
Enable debug logging by checking the browser console for messages prefixed with `🔗`.

## Demo

Run the interactive demo by opening `page-references.html` in a web browser. The demo showcases:

- Page reference extraction and creation
- Search functionality with real-time results
- Backlink tracking and display
- System statistics and monitoring
- Integration with block system

## Dependencies

### Required
- **fap-event-system**: Event communication system
- **fap-block-core**: Block data structure and storage

### Optional
- **fap-search-engine**: Enhanced search capabilities
- **fap-outliner**: Hierarchical content editing

## License

This package is part of the FAP (Functional Application Packages) ecosystem and follows the same licensing terms as the parent project.

## Contributing

When contributing to this package:

1. Maintain functional programming patterns
2. Follow semantic HTML conventions
3. Ensure comprehensive test coverage
4. Update documentation for API changes
5. Test integration with other packages

## Changelog

### Version 1.0.0
- Initial release with core functionality
- [[Page]] reference extraction and resolution
- Backlink tracking and management
- Page search with relevance scoring
- Phantom page support
- Event system integration
- Comprehensive CSS styling
- Interactive HTML demo
- Full documentation