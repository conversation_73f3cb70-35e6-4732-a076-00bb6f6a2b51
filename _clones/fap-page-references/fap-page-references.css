/**
 * FAP Page References - CSS Styling
 * 
 * Styles for page reference system including:
 * - Page link styling with hover effects
 * - Backlink display and navigation
 * - Page search interface
 * - Page statistics and metadata
 * - Phantom page indicators
 */

/* Page Reference Container */
page-references {
  display: block;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.5;
}

/* Page Link Styling */
page-link {
  display: inline;
  color: #0066cc;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

page-link:hover {
  color: #004499;
  border-bottom-color: #0066cc;
  background-color: rgba(0, 102, 204, 0.05);
}

page-link[data-exists="false"] {
  color: #cc6600;
  font-style: italic;
}

page-link[data-exists="false"]:hover {
  color: #994400;
  border-bottom-color: #cc6600;
  background-color: rgba(204, 102, 0, 0.05);
}

page-link[data-exists="false"]::after {
  content: " (phantom)";
  font-size: 0.8em;
  opacity: 0.6;
  font-weight: normal;
}

/* Page Search Interface */
page-search {
  display: block;
  margin: 1rem 0;
}

page-search-input {
  display: block;
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

page-search-input:focus {
  outline: none;
  border-color: #0066cc;
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.1);
}

page-search-results {
  display: block;
  margin-top: 0.5rem;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
  background: white;
}

page-search-result {
  display: block;
  padding: 0.5rem;
  border-bottom: 1px solid #f5f5f5;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

page-search-result:hover {
  background-color: #f8f9fa;
}

page-search-result:last-child {
  border-bottom: none;
}

page-search-result-name {
  display: block;
  font-weight: 500;
  color: #333;
}

page-search-result-meta {
  display: block;
  font-size: 0.85em;
  color: #666;
  margin-top: 0.2rem;
}

page-search-result[data-match-type="exact"] page-search-result-name {
  color: #0066cc;
  font-weight: 600;
}

page-search-result[data-match-type="prefix"] page-search-result-name {
  color: #0088cc;
}

/* Backlinks Section */
page-backlinks {
  display: block;
  margin: 1rem 0;
}

page-backlinks-header {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1.1em;
}

page-backlinks-list {
  display: block;
  margin-left: 1rem;
}

page-backlink-item {
  display: block;
  margin: 0.3rem 0;
  padding: 0.3rem;
  background: #f8f9fa;
  border-radius: 3px;
  border-left: 3px solid #0066cc;
}

page-backlink-content {
  display: block;
  color: #555;
  font-size: 0.9em;
}

page-backlink-meta {
  display: block;
  font-size: 0.8em;
  color: #888;
  margin-top: 0.2rem;
}

/* Page Statistics */
page-stats {
  display: block;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
}

page-stats-header {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1.1em;
}

page-stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.5rem;
}

page-stat-item {
  display: block;
  text-align: center;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
  border: 1px solid #e9ecef;
}

page-stat-value {
  display: block;
  font-size: 1.5em;
  font-weight: 600;
  color: #0066cc;
}

page-stat-label {
  display: block;
  font-size: 0.85em;
  color: #666;
  margin-top: 0.2rem;
}

/* Page List */
page-list {
  display: block;
  margin: 1rem 0;
}

page-list-header {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1.1em;
}

page-list-items {
  display: block;
}

page-list-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s ease;
}

page-list-item:hover {
  background-color: #f8f9fa;
}

page-list-item-name {
  display: block;
  font-weight: 500;
  color: #333;
}

page-list-item-name[data-exists="false"] {
  color: #cc6600;
  font-style: italic;
}

page-list-item-meta {
  display: flex;
  gap: 1rem;
  font-size: 0.85em;
  color: #666;
}

page-list-item-stat {
  display: block;
}

/* Page Creation Form */
page-create-form {
  display: block;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
}

page-create-input {
  display: block;
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  margin-bottom: 0.5rem;
}

page-create-button {
  display: inline-block;
  background: #0066cc;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9em;
  transition: background-color 0.2s ease;
}

page-create-button:hover {
  background: #0052a3;
}

page-create-button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

/* Debug Panel */
page-debug {
  display: block;
  background: #f1f3f4;
  border: 1px solid #dadce0;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.85em;
}

page-debug-header {
  display: block;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

page-debug-content {
  display: block;
  background: white;
  padding: 0.5rem;
  border-radius: 4px;
  border: 1px solid #e8eaed;
  white-space: pre-wrap;
  overflow-x: auto;
}

/* Action Buttons */
page-action-buttons {
  display: flex;
  gap: 0.5rem;
  margin: 1rem 0;
  flex-wrap: wrap;
}

page-action-button {
  display: inline-block;
  background: #f8f9fa;
  color: #333;
  border: 1px solid #dee2e6;
  padding: 0.4rem 0.8rem;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85em;
  transition: all 0.2s ease;
}

page-action-button:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

page-action-button.primary {
  background: #0066cc;
  color: white;
  border-color: #0066cc;
}

page-action-button.primary:hover {
  background: #0052a3;
  border-color: #0052a3;
}

page-action-button.danger {
  background: #dc3545;
  color: white;
  border-color: #dc3545;
}

page-action-button.danger:hover {
  background: #c82333;
  border-color: #c82333;
}

/* Responsive Design */
@media (max-width: 768px) {
  page-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  page-list-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.3rem;
  }
  
  page-list-item-meta {
    gap: 0.5rem;
  }
  
  page-action-buttons {
    flex-direction: column;
  }
  
  page-action-button {
    text-align: center;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  page-references {
    color: #e8eaed;
  }
  
  page-link {
    color: #8ab4f8;
  }
  
  page-link:hover {
    color: #aecbfa;
    background-color: rgba(138, 180, 248, 0.1);
  }
  
  page-link[data-exists="false"] {
    color: #fdd663;
  }
  
  page-link[data-exists="false"]:hover {
    color: #fce293;
    background-color: rgba(253, 214, 99, 0.1);
  }
  
  page-search-input {
    background: #303134;
    border-color: #5f6368;
    color: #e8eaed;
  }
  
  page-search-input:focus {
    border-color: #8ab4f8;
    box-shadow: 0 0 0 2px rgba(138, 180, 248, 0.2);
  }
  
  page-search-results {
    background: #303134;
    border-color: #5f6368;
  }
  
  page-search-result:hover {
    background-color: #3c4043;
  }
  
  page-backlink-item {
    background: #3c4043;
    border-left-color: #8ab4f8;
  }
  
  page-stats {
    background: #3c4043;
    border-color: #5f6368;
  }
  
  page-stat-item {
    background: #303134;
    border-color: #5f6368;
  }
  
  page-stat-value {
    color: #8ab4f8;
  }
  
  page-create-form {
    background: #3c4043;
    border-color: #5f6368;
  }
  
  page-create-input {
    background: #303134;
    border-color: #5f6368;
    color: #e8eaed;
  }
  
  page-debug {
    background: #3c4043;
    border-color: #5f6368;
  }
  
  page-debug-content {
    background: #303134;
    border-color: #5f6368;
    color: #e8eaed;
  }
}

/* Print Styles */
@media print {
  page-references {
    color: black;
  }
  
  page-link {
    color: black;
    text-decoration: underline;
  }
  
  page-search,
  page-action-buttons,
  page-debug {
    display: none;
  }
  
  page-stats,
  page-backlinks {
    break-inside: avoid;
  }
}