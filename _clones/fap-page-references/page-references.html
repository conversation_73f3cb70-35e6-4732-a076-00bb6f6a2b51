<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAP Page References Demo</title>
    <link rel="stylesheet" href="fap-page-references.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            line-height: 1.6;
            background: #fafafa;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e9ecef;
        }
        
        .demo-section {
            background: white;
            margin: 2rem 0;
            padding: 1.5rem;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .demo-section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 0.5rem;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .demo-content {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        
        .demo-content h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: 500;
            margin-left: 0.5rem;
        }
        
        .status-ready {
            background: #d4edda;
            color: #155724;
        }
        
        .status-loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 1rem;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="demo-header">
        <h1>🔗 FAP Page References Demo</h1>
        <p>Interactive demonstration of the [[Page]] linking and navigation system</p>
        <div id="system-status">
            <span>System Status:</span>
            <span id="status-indicator" class="status-indicator status-loading">Loading...</span>
        </div>
    </div>

    <!-- Page Search Section -->
    <div class="demo-section">
        <h2>Page Search & Discovery</h2>
        <page-search>
            <page-search-input 
                id="page-search-input" 
                placeholder="Search for pages... (try 'project', 'meeting', or 'idea')"
            ></page-search-input>
            <page-search-results id="page-search-results"></page-search-results>
        </page-search>
    </div>

    <!-- Page Creation Section -->
    <div class="demo-section">
        <h2>Page Creation</h2>
        <page-create-form>
            <page-create-input 
                id="page-create-input" 
                placeholder="Enter new page name..."
            ></page-create-input>
            <page-action-buttons>
                <page-action-button id="create-page-btn" class="primary">Create Page</page-action-button>
                <page-action-button id="create-phantom-btn">Create Phantom Page</page-action-button>
            </page-action-buttons>
        </page-create-form>
    </div>

    <div class="demo-grid">
        <!-- Page Statistics -->
        <div class="demo-section">
            <h2>System Statistics</h2>
            <page-stats id="page-stats">
                <page-stats-header>Page Reference System</page-stats-header>
                <page-stats-grid id="stats-grid">
                    <!-- Stats will be populated by JavaScript -->
                </page-stats-grid>
            </page-stats>
        </div>

        <!-- Page List -->
        <div class="demo-section">
            <h2>All Pages</h2>
            <page-list id="page-list">
                <page-list-header>Pages in System</page-list-header>
                <page-list-items id="page-list-items">
                    <!-- Pages will be populated by JavaScript -->
                </page-list-items>
            </page-list>
        </div>
    </div>

    <!-- Sample Content with Page References -->
    <div class="demo-section">
        <h2>Sample Content with Page References</h2>
        <div class="demo-content">
            <h3>Meeting Notes</h3>
            <p>Today's discussion covered the <page-link data-page="project-alpha">Project Alpha</page-link> timeline and integration with <page-link data-page="database-migration">Database Migration</page-link>.</p>
            
            <p>Key action items:</p>
            <ul>
                <li>Review <page-link data-page="api-documentation">API Documentation</page-link> by Friday</li>
                <li>Schedule follow-up with <page-link data-page="team-beta">Team Beta</page-link></li>
                <li>Update <page-link data-page="project-roadmap">Project Roadmap</page-link> with new milestones</li>
            </ul>
            
            <p>Related pages: <page-link data-page="sprint-planning">Sprint Planning</page-link>, <page-link data-page="technical-requirements">Technical Requirements</page-link></p>
        </div>
        
        <div class="demo-content">
            <h3>Research Notes</h3>
            <p>Investigating <page-link data-page="machine-learning">Machine Learning</page-link> approaches for <page-link data-page="data-analysis">Data Analysis</page-link>.</p>
            
            <p>Promising techniques include:</p>
            <ul>
                <li><page-link data-page="neural-networks">Neural Networks</page-link> for pattern recognition</li>
                <li><page-link data-page="clustering-algorithms">Clustering Algorithms</page-link> for data segmentation</li>
                <li><page-link data-page="feature-engineering">Feature Engineering</page-link> for model improvement</li>
            </ul>
            
            <p>See also: <page-link data-page="python-libraries">Python Libraries</page-link>, <page-link data-page="model-evaluation">Model Evaluation</page-link></p>
        </div>
    </div>

    <!-- Backlinks Section -->
    <div class="demo-section">
        <h2>Page Backlinks</h2>
        <div class="demo-content">
            <h3>Select a page to view its backlinks:</h3>
            <select id="backlink-page-select">
                <option value="">Choose a page...</option>
            </select>
            <page-backlinks id="page-backlinks-display">
                <!-- Backlinks will be populated by JavaScript -->
            </page-backlinks>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="demo-section">
        <h2>System Actions</h2>
        <page-action-buttons>
            <page-action-button id="rebuild-index-btn" class="primary">Rebuild Page Index</page-action-button>
            <page-action-button id="load-sample-data-btn">Load Sample Data</page-action-button>
            <page-action-button id="clear-all-btn" class="danger">Clear All Data</page-action-button>
            <page-action-button id="export-data-btn">Export Data</page-action-button>
        </page-action-buttons>
    </div>

    <!-- Debug Panel -->
    <div class="demo-section">
        <h2>Debug Information</h2>
        <page-debug id="debug-panel">
            <page-debug-header>System State</page-debug-header>
            <page-debug-content id="debug-content">
                System initializing...
            </page-debug-content>
        </page-debug>
    </div>

    <!-- Load Dependencies -->
    <script src="../fap-event-system/fap-event-system.js"></script>
    <script src="../fap-block-core/fap-block-core.js"></script>
    <script src="fap-page-references.js"></script>

    <script>
        // Initialize the demo
        let pageSystem = null;
        let blockStore = null;
        let eventSystem = null;

        // Initialize systems
        function initializeSystems() {
            try {
                // Initialize event system
                eventSystem = window.FAPEventSystem.createEventSystem();
                window.Events = eventSystem;
                
                // Initialize block store
                blockStore = window.FAPBlockCore.createBlockStore();
                window.FAPBlockCore.BlockStore = blockStore;
                
                // Initialize page reference system
                pageSystem = window.FAPPageReferences.createPageReferenceSystem({
                    autoCreatePages: true,
                    caseSensitive: false,
                    pageNameValidation: true
                });
                
                pageSystem.init();
                
                updateStatus('ready', 'System Ready');
                loadSampleData();
                updateDisplay();
                
                console.log('✅ All systems initialized successfully');
            } catch (error) {
                console.error('❌ System initialization failed:', error);
                updateStatus('error', 'Initialization Failed');
            }
        }

        // Update system status indicator
        function updateStatus(status, message) {
            const indicator = document.getElementById('status-indicator');
            indicator.className = `status-indicator status-${status}`;
            indicator.textContent = message;
        }

        // Load sample data
        function loadSampleData() {
            if (!blockStore || !pageSystem) return;
            
            const sampleBlocks = [
                {
                    id: 'block-1',
                    content: 'Meeting notes for [[Project Alpha]] and [[Database Migration]] planning.',
                    properties: {}
                },
                {
                    id: 'block-2', 
                    content: 'Research on [[Machine Learning]] and [[Data Analysis]] techniques.',
                    properties: {}
                },
                {
                    id: 'block-3',
                    content: 'Review [[API Documentation]] and update [[Project Roadmap]].',
                    properties: {}
                },
                {
                    id: 'block-4',
                    content: 'Sprint planning session with [[Team Beta]] for [[Technical Requirements]].',
                    properties: {}
                },
                {
                    id: 'page-project-alpha',
                    content: '# Project Alpha\n\nMain project documentation and timeline.',
                    properties: { page: 'project-alpha' }
                }
            ];
            
            sampleBlocks.forEach(block => {
                blockStore.createBlock(block.content, block.properties, block.id);
            });
            
            console.log('📝 Sample data loaded');
        }

        // Update all display elements
        function updateDisplay() {
            if (!pageSystem) return;
            
            updateStats();
            updatePageList();
            updateBacklinkSelect();
            updateDebugPanel();
            setupPageLinks();
        }

        // Update statistics display
        function updateStats() {
            const stats = pageSystem.getSystemStats();
            const statsGrid = document.getElementById('stats-grid');
            
            statsGrid.innerHTML = `
                <page-stat-item>
                    <page-stat-value>${stats.totalPages}</page-stat-value>
                    <page-stat-label>Total Pages</page-stat-label>
                </page-stat-item>
                <page-stat-item>
                    <page-stat-value>${stats.existingPages}</page-stat-value>
                    <page-stat-label>Existing Pages</page-stat-label>
                </page-stat-item>
                <page-stat-item>
                    <page-stat-value>${stats.phantomPages}</page-stat-value>
                    <page-stat-label>Phantom Pages</page-stat-label>
                </page-stat-item>
                <page-stat-item>
                    <page-stat-value>${stats.totalBacklinks}</page-stat-value>
                    <page-stat-label>Total Backlinks</page-stat-label>
                </page-stat-item>
                <page-stat-item>
                    <page-stat-value>${stats.averageBacklinks}</page-stat-value>
                    <page-stat-label>Avg Backlinks</page-stat-label>
                </page-stat-item>
            `;
        }

        // Update page list display
        function updatePageList() {
            const pages = pageSystem.getAllPages();
            const listItems = document.getElementById('page-list-items');
            
            if (pages.length === 0) {
                listItems.innerHTML = '<p style="text-align: center; color: #666; padding: 1rem;">No pages found</p>';
                return;
            }
            
            listItems.innerHTML = pages.map(page => `
                <page-list-item>
                    <page-list-item-name data-exists="${page.exists}">${page.displayName}</page-list-item-name>
                    <page-list-item-meta>
                        <page-list-item-stat>${page.blocks.size} blocks</page-list-item-stat>
                        <page-list-item-stat>${page.backlinks.size} backlinks</page-list-item-stat>
                        <page-list-item-stat>${page.exists ? 'exists' : 'phantom'}</page-list-item-stat>
                    </page-list-item-meta>
                </page-list-item>
            `).join('');
        }

        // Update backlink select dropdown
        function updateBacklinkSelect() {
            const pages = pageSystem.getAllPages();
            const select = document.getElementById('backlink-page-select');
            
            select.innerHTML = '<option value="">Choose a page...</option>' +
                pages.map(page => `<option value="${page.name}">${page.displayName}</option>`).join('');
        }

        // Update debug panel
        function updateDebugPanel() {
            const state = pageSystem.getState();
            const debugContent = document.getElementById('debug-content');
            
            const debugInfo = {
                pages: state.pages.size,
                backlinks: state.backlinks.size,
                pageBlocks: state.pageBlocks.size,
                cacheSize: state.referenceCache.size,
                options: state.options
            };
            
            debugContent.textContent = JSON.stringify(debugInfo, null, 2);
        }

        // Setup page link interactions
        function setupPageLinks() {
            document.querySelectorAll('page-link').forEach(link => {
                const pageName = link.getAttribute('data-page');
                const page = pageSystem.getPage(pageName);
                
                link.setAttribute('data-exists', page ? page.exists : 'false');
                
                link.addEventListener('click', () => {
                    if (page) {
                        showPageDetails(page);
                    } else {
                        createPageFromLink(pageName);
                    }
                });
            });
        }

        // Show page details
        function showPageDetails(page) {
            const stats = pageSystem.getPageStats(page.name);
            alert(`Page: ${page.displayName}\n\nBlocks: ${stats.blockCount}\nBacklinks: ${stats.backlinkCount}\nExists: ${stats.exists}\nCreated: ${new Date(stats.created).toLocaleString()}`);
        }

        // Create page from link
        function createPageFromLink(pageName) {
            if (confirm(`Create page "${pageName}"?`)) {
                pageSystem.createPage(pageName, { exists: true });
                updateDisplay();
            }
        }

        // Event handlers
        document.getElementById('page-search-input').addEventListener('input', (e) => {
            const query = e.target.value.trim();
            const resultsContainer = document.getElementById('page-search-results');
            
            if (query.length < 2) {
                resultsContainer.innerHTML = '';
                return;
            }
            
            const results = pageSystem.searchPages(query);
            
            if (results.length === 0) {
                resultsContainer.innerHTML = '<p style="padding: 1rem; text-align: center; color: #666;">No pages found</p>';
                return;
            }
            
            resultsContainer.innerHTML = results.map(result => `
                <page-search-result data-match-type="${result.matchType}" onclick="selectSearchResult('${result.page.name}')">
                    <page-search-result-name>${result.page.displayName}</page-search-result-name>
                    <page-search-result-meta>
                        ${result.page.blocks.size} blocks, ${result.page.backlinks.size} backlinks
                        ${result.page.exists ? '' : '(phantom)'}
                    </page-search-result-meta>
                </page-search-result>
            `).join('');
        });

        function selectSearchResult(pageName) {
            const page = pageSystem.getPage(pageName);
            if (page) {
                showPageDetails(page);
            }
        }

        document.getElementById('create-page-btn').addEventListener('click', () => {
            const input = document.getElementById('page-create-input');
            const pageName = input.value.trim();
            
            if (pageName) {
                pageSystem.createPage(pageName, { exists: true });
                input.value = '';
                updateDisplay();
            }
        });

        document.getElementById('create-phantom-btn').addEventListener('click', () => {
            const input = document.getElementById('page-create-input');
            const pageName = input.value.trim();
            
            if (pageName) {
                pageSystem.createPage(pageName, { exists: false });
                input.value = '';
                updateDisplay();
            }
        });

        document.getElementById('backlink-page-select').addEventListener('change', (e) => {
            const pageName = e.target.value;
            const backlinksDisplay = document.getElementById('page-backlinks-display');
            
            if (!pageName) {
                backlinksDisplay.innerHTML = '';
                return;
            }
            
            const backlinks = pageSystem.getBacklinks(pageName);
            
            if (backlinks.length === 0) {
                backlinksDisplay.innerHTML = '<p style="color: #666; padding: 1rem;">No backlinks found for this page</p>';
                return;
            }
            
            backlinksDisplay.innerHTML = `
                <page-backlinks-header>Backlinks to "${pageName}" (${backlinks.length})</page-backlinks-header>
                <page-backlinks-list>
                    ${backlinks.map(block => `
                        <page-backlink-item>
                            <page-backlink-content>${block.content}</page-backlink-content>
                            <page-backlink-meta>Block ID: ${block.id}</page-backlink-meta>
                        </page-backlink-item>
                    `).join('')}
                </page-backlinks-list>
            `;
        });

        document.getElementById('rebuild-index-btn').addEventListener('click', () => {
            pageSystem.forceRebuildIndex();
            updateDisplay();
        });

        document.getElementById('load-sample-data-btn').addEventListener('click', () => {
            loadSampleData();
            updateDisplay();
        });

        document.getElementById('clear-all-btn').addEventListener('click', () => {
            if (confirm('Clear all data? This cannot be undone.')) {
                blockStore.clear();
                updateDisplay();
            }
        });

        document.getElementById('export-data-btn').addEventListener('click', () => {
            const data = {
                pages: Array.from(pageSystem.getState().pages.entries()),
                stats: pageSystem.getSystemStats(),
                timestamp: new Date().toISOString()
            };
            
            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'page-references-export.json';
            a.click();
            URL.revokeObjectURL(url);
        });

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeSystems);

        // Auto-refresh display every 5 seconds
        setInterval(() => {
            if (pageSystem) {
                updateDisplay();
            }
        }, 5000);
    </script>
</body>
</html>