<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAP Block Core - Demo</title>
    <link rel="stylesheet" href="fap-block-core.css">
</head>
<body>
    <h1>FAP Block Core Demo</h1>
    
    <div id="demo-container">
        <!-- Example block structure -->
        <block-item depth="0" id="block-1">
            <block-bullet></block-bullet>
            <block-content contenteditable="true">
                This is a root block with a [[Page Reference]] and #tag
            </block-content>
        </block-item>

        <block-item depth="1" id="block-2" has-children="true">
            <block-bullet expandable="true" expanded="true"></block-bullet>
            <block-content contenteditable="true">
                This is a child block that references ((another-block))
            </block-content>
            <block-children>
                <block-item depth="2" id="block-3">
                    <block-bullet></block-bullet>
                    <block-content contenteditable="true">
                        This is a nested child block
                    </block-content>
                </block-item>
            </block-children>
        </block-item>

        <block-item depth="0" id="block-4">
            <block-bullet></block-bullet>
            <block-content contenteditable="true">
                Another root block for testing
            </block-content>
        </block-item>
    </div>

    <div id="controls">
        <h3>Controls</h3>
        <button onclick="createNewBlock()">Create New Block</button>
        <button onclick="showAllBlocks()">Show All Blocks</button>
        <button onclick="searchBlocks()">Search Blocks</button>
        <button onclick="showReferences()">Show References</button>
    </div>

    <div id="output">
        <h3>Output</h3>
        <pre id="output-content"></pre>
    </div>

    <script src="fap-block-core.js"></script>
    <script>
        // Demo functionality
        const { Block, BlockStore } = window.FAPBlockCore;

        // Initialize some demo blocks
        function initDemo() {
            const block1 = new Block({
                id: 'block-1',
                content: 'This is a root block with a [[Page Reference]] and #tag'
            });

            const block2 = new Block({
                id: 'block-2', 
                content: 'This is a child block that references ((another-block))'
            });

            const block3 = new Block({
                id: 'block-3',
                content: 'This is a nested child block',
                parent: 'block-2'
            });

            const block4 = new Block({
                id: 'block-4',
                content: 'Another root block for testing'
            });

            // Set up hierarchy
            block2.addChild(block3);

            // Add to store
            BlockStore.addBlock(block1);
            BlockStore.addBlock(block2);
            BlockStore.addBlock(block3);
            BlockStore.addBlock(block4);

            console.log('Demo initialized with', BlockStore.getAllBlocks().length, 'blocks');
        }

        function createNewBlock() {
            const content = prompt('Enter block content:');
            if (content) {
                const block = new Block({ content });
                BlockStore.addBlock(block);
                updateOutput('Created block: ' + JSON.stringify(block.toJSON(), null, 2));
            }
        }

        function showAllBlocks() {
            const blocks = BlockStore.getAllBlocks();
            updateOutput('All blocks:\n' + JSON.stringify(blocks.map(b => b.toJSON()), null, 2));
        }

        function searchBlocks() {
            const query = prompt('Enter search query:');
            if (query) {
                const results = BlockStore.searchBlocks(query);
                updateOutput('Search results for "' + query + '":\n' + 
                    JSON.stringify(results.map(b => b.toJSON()), null, 2));
            }
        }

        function showReferences() {
            const blocks = BlockStore.getAllBlocks();
            const refsData = blocks.map(block => ({
                id: block.id,
                content: block.content,
                refs: block.refs
            })).filter(block => block.refs.length > 0);
            
            updateOutput('Blocks with references:\n' + JSON.stringify(refsData, null, 2));
        }

        function updateOutput(text) {
            document.getElementById('output-content').textContent = text;
        }

        // Add event listeners for interactive editing
        document.addEventListener('DOMContentLoaded', function() {
            initDemo();

            // Listen for content changes
            document.querySelectorAll('block-content[contenteditable]').forEach(element => {
                element.addEventListener('input', function() {
                    const blockId = this.closest('block-item').id;
                    const block = BlockStore.getBlock(blockId);
                    if (block) {
                        block.updateContent(this.textContent);
                        console.log('Updated block:', blockId, 'with content:', this.textContent);
                    }
                });
            });

            // Handle bullet clicks for expand/collapse
            document.querySelectorAll('block-bullet[expandable]').forEach(bullet => {
                bullet.addEventListener('click', function() {
                    const isExpanded = this.getAttribute('expanded') === 'true';
                    this.setAttribute('expanded', !isExpanded);
                    
                    const children = this.parentElement.querySelector('block-children');
                    if (children) {
                        children.setAttribute('collapsed', isExpanded);
                    }
                });
            });
        });

        // Simple event system for demonstration
        window.BlockEvents = {
            listeners: {},
            emit: function(event, data) {
                console.log('Event:', event, data);
                if (this.listeners[event]) {
                    this.listeners[event].forEach(callback => callback(data));
                }
            },
            on: function(event, callback) {
                if (!this.listeners[event]) {
                    this.listeners[event] = [];
                }
                this.listeners[event].push(callback);
            }
        };

        // Listen for block changes
        BlockEvents.on('block:changed', function(block) {
            console.log('Block changed:', block.id);
        });

        BlockEvents.on('store:changed', function(data) {
            console.log('Store changed:', data.action, data.block.id);
        });
    </script>
</body>
</html>