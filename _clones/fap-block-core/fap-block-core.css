/**
 * FAP Block Core - Base styling for blocks
 * 
 * This provides the fundamental visual structure for blocks.
 * Individual components can extend these styles.
 */

/* Block container - semantic HTML element */
block-item {
    display: block;
    position: relative;
    margin: 0;
    padding: 0;
    line-height: 1.6;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Block content area */
block-content {
    display: block;
    padding: 2px 0;
    min-height: 1.6em;
    word-wrap: break-word;
    outline: none;
}

/* Block with children gets extra spacing */
block-item[has-children="true"] {
    margin-bottom: 4px;
}

/* Block hierarchy indentation */
block-item[depth="0"] { margin-left: 0; }
block-item[depth="1"] { margin-left: 24px; }
block-item[depth="2"] { margin-left: 48px; }
block-item[depth="3"] { margin-left: 72px; }
block-item[depth="4"] { margin-left: 96px; }
block-item[depth="5"] { margin-left: 120px; }
block-item[depth="6"] { margin-left: 144px; }

/* Block bullet/marker */
block-bullet {
    display: inline-block;
    width: 6px;
    height: 6px;
    background-color: #666;
    border-radius: 50%;
    margin-right: 8px;
    margin-top: 8px;
    vertical-align: top;
    cursor: pointer;
    flex-shrink: 0;
}

/* Expandable bullet for blocks with children */
block-bullet[expandable="true"] {
    width: 0;
    height: 0;
    background: none;
    border-left: 4px solid #666;
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    border-radius: 0;
    margin-top: 6px;
    transition: transform 0.2s ease;
}

block-bullet[expandable="true"][expanded="true"] {
    transform: rotate(90deg);
}

/* Block references styling */
block-ref {
    color: #0066cc;
    text-decoration: none;
    cursor: pointer;
    border-radius: 2px;
    padding: 1px 2px;
}

block-ref:hover {
    background-color: #f0f8ff;
    text-decoration: underline;
}

/* Page references [[Page]] */
block-ref[type="page"] {
    color: #0066cc;
}

/* Block references ((block)) */
block-ref[type="block"] {
    color: #8b5cf6;
}

/* Tag references #tag */
block-ref[type="tag"] {
    color: #059669;
    font-weight: 500;
}

/* Block states */
block-item[editing="true"] block-content {
    background-color: #fffbf0;
    border: 1px solid #fbbf24;
    border-radius: 3px;
    padding: 4px 6px;
}

block-item[selected="true"] {
    background-color: #f3f4f6;
    border-radius: 4px;
}

/* Block children container */
block-children {
    display: block;
    margin-top: 2px;
}

block-children[collapsed="true"] {
    display: none;
}

/* Empty block placeholder */
block-content:empty::before {
    content: "Type something...";
    color: #9ca3af;
    font-style: italic;
}

/* Focus states */
block-content:focus {
    outline: none;
}

/* Block drag and drop states */
block-item[dragging="true"] {
    opacity: 0.5;
}

block-item[drop-target="true"] {
    border-top: 2px solid #3b82f6;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    block-item[depth="1"] { margin-left: 16px; }
    block-item[depth="2"] { margin-left: 32px; }
    block-item[depth="3"] { margin-left: 48px; }
    block-item[depth="4"] { margin-left: 64px; }
    block-item[depth="5"] { margin-left: 80px; }
    block-item[depth="6"] { margin-left: 96px; }
}

/* Print styles */
@media print {
    block-bullet {
        background-color: #000;
    }
    
    block-ref {
        color: #000;
        text-decoration: underline;
    }
    
    block-item[editing="true"] block-content {
        background: none;
        border: none;
    }
}