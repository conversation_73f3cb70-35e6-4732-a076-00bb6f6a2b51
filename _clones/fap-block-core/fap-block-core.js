/**
 * FAP Block Core - The fundamental block data structure and operations
 * 
 * A block is the atomic unit of content in our system.
 * Every piece of content (text, image, embed, etc.) is a block.
 * Blocks can have children (nested blocks) and references to other blocks.
 */

class Block {
    constructor(options = {}) {
        this.id = options.id || this.generateId();
        this.content = options.content || '';
        this.type = options.type || 'text'; // text, image, embed, etc.
        this.parent = options.parent || null;
        this.children = options.children || [];
        this.properties = options.properties || {};
        this.created = options.created || new Date().toISOString();
        this.updated = options.updated || new Date().toISOString();
        this.refs = options.refs || []; // References to other blocks/pages
    }

    generateId() {
        // Generate a unique ID for the block
        return 'block_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // Update block content and timestamp
    updateContent(newContent) {
        this.content = newContent;
        this.updated = new Date().toISOString();
        this.extractReferences();
        this.notifyChange();
    }

    // Extract [[page]] and ((block)) references from content
    extractReferences() {
        this.refs = [];
        
        // Extract page references [[Page Name]]
        const pageRefs = this.content.match(/\[\[([^\]]+)\]\]/g);
        if (pageRefs) {
            pageRefs.forEach(ref => {
                const pageName = ref.slice(2, -2);
                this.refs.push({
                    type: 'page',
                    target: pageName,
                    original: ref
                });
            });
        }

        // Extract block references ((block-id))
        const blockRefs = this.content.match(/\(\(([^)]+)\)\)/g);
        if (blockRefs) {
            blockRefs.forEach(ref => {
                const blockId = ref.slice(2, -2);
                this.refs.push({
                    type: 'block',
                    target: blockId,
                    original: ref
                });
            });
        }

        // Extract hashtags #tag
        const hashTags = this.content.match(/#[\w-]+/g);
        if (hashTags) {
            hashTags.forEach(tag => {
                this.refs.push({
                    type: 'tag',
                    target: tag.slice(1),
                    original: tag
                });
            });
        }
    }

    // Add a child block
    addChild(childBlock) {
        childBlock.parent = this.id;
        this.children.push(childBlock.id);
        this.updated = new Date().toISOString();
        this.notifyChange();
    }

    // Remove a child block
    removeChild(childId) {
        this.children = this.children.filter(id => id !== childId);
        this.updated = new Date().toISOString();
        this.notifyChange();
    }

    // Convert block to plain object for storage
    toJSON() {
        return {
            id: this.id,
            content: this.content,
            type: this.type,
            parent: this.parent,
            children: this.children,
            properties: this.properties,
            created: this.created,
            updated: this.updated,
            refs: this.refs
        };
    }

    // Create block from stored data
    static fromJSON(data) {
        return new Block(data);
    }

    // Notify system of changes (event system integration point)
    notifyChange() {
        if (window.BlockEvents) {
            window.BlockEvents.emit('block:changed', this);
        }
    }

    // Get block depth in hierarchy
    getDepth() {
        let depth = 0;
        let current = this.parent;
        while (current) {
            depth++;
            current = BlockStore.getBlock(current)?.parent;
        }
        return depth;
    }

    // Check if this block has children
    hasChildren() {
        return this.children.length > 0;
    }

    // Get all descendant blocks (recursive)
    getAllDescendants() {
        let descendants = [];
        this.children.forEach(childId => {
            const child = BlockStore.getBlock(childId);
            if (child) {
                descendants.push(child);
                descendants = descendants.concat(child.getAllDescendants());
            }
        });
        return descendants;
    }
}

// Simple in-memory block store (will be replaced with database later)
class BlockStore {
    static blocks = new Map();

    static addBlock(block) {
        this.blocks.set(block.id, block);
        this.notifyChange('add', block);
    }

    static getBlock(id) {
        return this.blocks.get(id);
    }

    static updateBlock(block) {
        this.blocks.set(block.id, block);
        this.notifyChange('update', block);
    }

    static deleteBlock(id) {
        const block = this.blocks.get(id);
        if (block) {
            // Remove from parent's children
            if (block.parent) {
                const parent = this.getBlock(block.parent);
                if (parent) {
                    parent.removeChild(id);
                }
            }
            
            // Delete all children recursively
            block.children.forEach(childId => {
                this.deleteBlock(childId);
            });
            
            this.blocks.delete(id);
            this.notifyChange('delete', block);
        }
    }

    static getAllBlocks() {
        return Array.from(this.blocks.values());
    }

    static getBlocksByParent(parentId) {
        return this.getAllBlocks().filter(block => block.parent === parentId);
    }

    static getRootBlocks() {
        return this.getAllBlocks().filter(block => !block.parent);
    }

    static notifyChange(action, block) {
        if (window.BlockEvents) {
            window.BlockEvents.emit('store:changed', { action, block });
        }
    }

    // Search blocks by content
    static searchBlocks(query) {
        const lowerQuery = query.toLowerCase();
        return this.getAllBlocks().filter(block => 
            block.content.toLowerCase().includes(lowerQuery)
        );
    }

    // Get blocks that reference a specific page or block
    static getBlocksReferencingTarget(target, type = 'page') {
        return this.getAllBlocks().filter(block => 
            block.refs.some(ref => ref.type === type && ref.target === target)
        );
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { Block, BlockStore };
} else {
    window.FAPBlockCore = { Block, BlockStore };
}