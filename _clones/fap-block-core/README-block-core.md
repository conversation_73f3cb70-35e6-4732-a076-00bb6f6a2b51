# FAP Block Core

The fundamental building block (pun intended) of our Logseq-inspired system. Every piece of content is a block that can contain text, references, and child blocks.

## Features

- **Block Data Structure**: Core Block class with content, hierarchy, and metadata
- **Reference Extraction**: Automatically extracts [[page]], ((block)), and #tag references
- **Hierarchical Structure**: Blocks can have parent-child relationships
- **In-Memory Store**: Simple BlockStore for managing blocks (will be replaced with database)
- **Event System**: Notifies other components when blocks change
- **Semantic HTML**: Uses `<block-item>`, `<block-content>`, etc. for self-documenting markup

## Usage

```javascript
// Create a new block
const block = new Block({
    content: 'This is a block with a [[Page Reference]] and #tag'
});

// Add to store
BlockStore.addBlock(block);

// Create child block
const child = new Block({
    content: 'Child block content',
    parent: block.id
});
block.addChild(child);

// Search blocks
const results = BlockStore.searchBlocks('reference');

// Get blocks referencing a page
const refs = BlockStore.getBlocksReferencingTarget('Page Reference', 'page');
```

## HTML Structure

```html
<block-item depth="0" id="block-1">
    <block-bullet></block-bullet>
    <block-content contenteditable="true">
        Block content goes here
    </block-content>
    <block-children>
        <!-- Child blocks here -->
    </block-children>
</block-item>
```

## CSS Classes

- `block-item`: Main block container
- `block-content`: Editable content area
- `block-bullet`: Visual bullet point
- `block-children`: Container for child blocks
- `block-ref`: Styled references

## Events

- `block:changed`: Fired when a block's content changes
- `store:changed`: Fired when blocks are added/updated/deleted

## Next Steps

This core will be used by:
- `fap-outliner`: Visual editing interface
- `fap-page-references`: Page linking system
- `fap-block-editor`: Rich text editing
- `fap-search-engine`: Full-text search

## Design Decisions

1. **Semantic HTML**: Using custom element names like `<block-item>` instead of `<div class="block-item">` for cleaner, self-documenting code
2. **Vanilla JS**: No frameworks, no build step, just plain JavaScript
3. **Event-Driven**: Loose coupling between components via events
4. **Immutable IDs**: Block IDs never change once created
5. **Reference Extraction**: Automatic parsing of [[page]], ((block)), and #tag syntax