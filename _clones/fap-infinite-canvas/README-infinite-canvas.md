# FAP Infinite Canvas - Large-Scale Spatial Canvas System

## Overview

The FAP Infinite Canvas provides a high-performance, reusable foundation for spatial applications requiring pan and zoom navigation across large coordinate spaces. It supports both Google Maps-style and Logseq-style controls, touch gestures, keyboard navigation, and comprehensive event integration.

## Features

### Core Functionality
- **Large Coordinate Space**: Supports coordinates from -1,000,000 to +1,000,000
- **High-Performance Rendering**: Maintains 60fps during pan and zoom operations
- **Dual Control Styles**: Google Maps (default) and Logseq-style navigation
- **Touch Support**: Mobile-friendly pinch-to-zoom and pan gestures
- **Keyboard Navigation**: Arrow keys, zoom shortcuts, and accessibility support
- **State Persistence**: Save and restore viewport state with localStorage

### Advanced Features
- **Viewport Culling**: Efficient rendering of large content sets
- **Performance Monitoring**: Real-time FPS and memory usage tracking
- **Event System Integration**: Comprehensive event emission and listening
- **Animation Support**: Smooth transitions with customizable easing
- **High-DPI Support**: Automatic device pixel ratio handling
- **Responsive Design**: Adapts to container size changes

## Installation

### Basic Setup
```html
<!-- Include dependencies -->
<script src="../fap-event-system/fap-event-system.js"></script>

<!-- Include infinite canvas -->
<script src="fap-infinite-canvas.js"></script>
<link rel="stylesheet" href="fap-infinite-canvas.css">
```

### Initialization
```javascript
// Initialize event system
const eventSystem = window.FAPEventSystem.createEventSystem();
window.Events = eventSystem;

// Create canvas instance
const container = document.getElementById('canvas-container');
const canvas = window.FAPInfiniteCanvas.createInfiniteCanvas(container, {
    controlStyle: 'google-maps',
    minZoom: 0.1,
    maxZoom: 10,
    enableKeyboard: true,
    enableTouch: true,
    debugMode: false
});

// Initialize the canvas
if (canvas.init()) {
    console.log('Canvas initialized successfully');
} else {
    console.error('Canvas initialization failed');
}
```

## API Reference

### Core Functions

#### `createInfiniteCanvas(container, options)`
Creates a new infinite canvas instance.

**Parameters:**
- `container` (HTMLElement): DOM element to contain the canvas
- `options` (Object): Configuration options
  - `controlStyle` (String): 'google-maps' | 'logseq' (default: 'google-maps')
  - `minZoom` (Number): Minimum zoom level (default: 0.1)
  - `maxZoom` (Number): Maximum zoom level (default: 10)
  - `panBounds` (Object): Optional pan boundaries
  - `enableKeyboard` (Boolean): Enable keyboard controls (default: true)
  - `enableTouch` (Boolean): Enable touch gestures (default: true)
  - `smoothing` (Boolean): Enable smooth animations (default: true)
  - `debugMode` (Boolean): Show debug information (default: false)
  - `coordinateLimit` (Number): Coordinate space limit (default: 1,000,000)

**Returns:** Canvas instance object

#### `init()`
Initializes the canvas system and sets up event listeners.

**Returns:** Boolean indicating success

#### `destroy()`
Cleans up all resources and removes the canvas from DOM.

### Canvas Access

#### `getCanvas()`
Returns the HTML5 canvas element.

**Returns:** HTMLCanvasElement

#### `getContext()`
Returns the 2D rendering context.

**Returns:** CanvasRenderingContext2D

#### `getDimensions()`
Returns current canvas dimensions.

**Returns:** Object with width, height, and devicePixelRatio

### Coordinate Transformations

#### `screenToWorld(screenX, screenY)`
Converts screen coordinates to world coordinates.

**Parameters:**
- `screenX` (Number): Screen X coordinate
- `screenY` (Number): Screen Y coordinate

**Returns:** Object with x and y world coordinates

#### `worldToScreen(worldX, worldY)`
Converts world coordinates to screen coordinates.

**Parameters:**
- `worldX` (Number): World X coordinate
- `worldY` (Number): World Y coordinate

**Returns:** Object with x and y screen coordinates

#### `applyTransform(ctx)`
Applies current viewport transformation to canvas context.

**Parameters:**
- `ctx` (CanvasRenderingContext2D): Canvas context (optional, uses default if not provided)

#### `getTransformMatrix()`
Returns current transformation matrix.

**Returns:** Object with matrix components (a, b, c, d, e, f)

#### `transformRect(worldRect)`
Transforms a rectangle from world to screen coordinates.

**Parameters:**
- `worldRect` (Object): Rectangle with x, y, width, height

**Returns:** Transformed rectangle object

### Viewport Management

#### `updateViewport(panX, panY, zoom, emitEvents)`
Updates viewport state with bounds checking.

**Parameters:**
- `panX` (Number): New pan X offset
- `panY` (Number): New pan Y offset
- `zoom` (Number): New zoom level
- `emitEvents` (Boolean): Whether to emit change events (default: true)

**Returns:** Boolean indicating if viewport changed

#### `panToPoint(worldX, worldY, animated)`
Pans to center a specific world coordinate.

**Parameters:**
- `worldX` (Number): Target world X coordinate
- `worldY` (Number): Target world Y coordinate
- `animated` (Boolean): Use smooth animation (default: false)

#### `zoomToPoint(worldX, worldY, newZoom, animated)`
Zooms to specific level while keeping point under cursor.

**Parameters:**
- `worldX` (Number): World X coordinate to zoom to
- `worldY` (Number): World Y coordinate to zoom to
- `newZoom` (Number): Target zoom level
- `animated` (Boolean): Use smooth animation (default: false)

#### `fitToContent(contentBounds, padding)`
Fits viewport to show all content with padding.

**Parameters:**
- `contentBounds` (Object): Bounds with left, top, right, bottom
- `padding` (Number): Padding around content (default: 50)

#### `resetViewport(animated)`
Resets viewport to default state (0, 0, 1x zoom).

**Parameters:**
- `animated` (Boolean): Use smooth animation (default: false)

#### `getViewportCenter()`
Returns center point of viewport in world coordinates.

**Returns:** Object with x and y coordinates

#### `getVisibleBounds()`
Returns currently visible world bounds.

**Returns:** Object with left, top, right, bottom bounds

#### `isAtZoomLimit()`
Checks if viewport is at zoom limits.

**Returns:** Object with atMin and atMax boolean properties

### Viewport Culling

#### `getCullingBounds(padding)`
Returns expanded visible bounds for culling calculations.

**Parameters:**
- `padding` (Number): Extra padding around visible area (default: 100)

**Returns:** Object with left, top, right, bottom bounds

#### `isRectVisible(worldRect)`
Checks if a rectangle is visible in current viewport.

**Parameters:**
- `worldRect` (Object): Rectangle with x, y, width, height

**Returns:** Boolean indicating visibility

#### `isPointInCullingBounds(worldX, worldY, padding)`
Checks if a point is within culling bounds.

**Parameters:**
- `worldX` (Number): World X coordinate
- `worldY` (Number): World Y coordinate
- `padding` (Number): Extra padding (default: 100)

**Returns:** Boolean indicating if point is in bounds

#### `cullRectangles(rectangles, padding)`
Filters array of rectangles to only visible ones.

**Parameters:**
- `rectangles` (Array): Array of rectangle objects
- `padding` (Number): Extra padding (default: 100)

**Returns:** Filtered array of visible rectangles

#### `cullPoints(points, padding)`
Filters array of points to only visible ones.

**Parameters:**
- `points` (Array): Array of point objects
- `padding` (Number): Extra padding (default: 100)

**Returns:** Filtered array of visible points

### Control Style Management

#### `setControlStyle(newStyle)`
Changes control style without restart.

**Parameters:**
- `newStyle` (String): 'google-maps' | 'logseq'

**Returns:** String of actual style set (defaults to 'google-maps' if invalid)

#### `getControlStyle()`
Returns current control style.

**Returns:** String of current control style

#### `getAvailableControlStyles()`
Returns array of available control styles.

**Returns:** Array of style strings

#### `getControlStyleInfo()`
Returns detailed information about control styles.

**Returns:** Object with style descriptions and behaviors

### Performance Optimization

#### `scheduleUpdate(callback)`
Schedules an update using requestAnimationFrame.

**Parameters:**
- `callback` (Function): Function to call during update (optional)

#### `getPerformanceStats()`
Returns current performance statistics.

**Returns:** Object with FPS, memory usage, and viewport info

#### `startPerformanceMonitoring(interval)`
Starts automatic performance monitoring.

**Parameters:**
- `interval` (Number): Monitoring interval in milliseconds (default: 1000)

**Returns:** Interval ID

#### `stopPerformanceMonitoring()`
Stops performance monitoring.

#### `clearCaches()`
Clears internal caches to free memory.

#### `optimizeForLargeSpace()`
Enables optimizations for large coordinate spaces.

### State Persistence

#### `serializeState()`
Serializes current viewport state for persistence.

**Returns:** Object with serializable state data

#### `restoreState(serializedState, animated)`
Restores viewport from serialized state.

**Parameters:**
- `serializedState` (Object): Previously serialized state
- `animated` (Boolean): Use smooth animation (default: false)

**Returns:** Boolean indicating success

#### `enableAutoSave(key, delay)`
Enables automatic state saving to localStorage.

**Parameters:**
- `key` (String): localStorage key (default: 'fap-infinite-canvas-state')
- `delay` (Number): Save delay in milliseconds (default: 500)

**Returns:** String of storage key used

#### `disableAutoSave()`
Disables automatic state saving.

#### `loadState(key, animated)`
Loads state from localStorage.

**Parameters:**
- `key` (String): localStorage key (default: 'fap-infinite-canvas-state')
- `animated` (Boolean): Use smooth animation (default: false)

**Returns:** Boolean indicating success

#### `saveState(key)`
Manually saves current state to localStorage.

**Parameters:**
- `key` (String): localStorage key (default: 'fap-infinite-canvas-state')

**Returns:** Boolean indicating success

#### `clearSavedState(key)`
Clears saved state from localStorage.

**Parameters:**
- `key` (String): localStorage key (default: 'fap-infinite-canvas-state')

**Returns:** Boolean indicating success

### Event System

#### `getEmittedEvents()`
Returns array of all events this canvas can emit.

**Returns:** Array of event type strings

#### `getListenedEvents()`
Returns array of all events this canvas listens for.

**Returns:** Array of event type strings

## Usage Examples

### Basic Canvas Setup
```javascript
const container = document.getElementById('my-canvas');
const canvas = createInfiniteCanvas(container, {
    controlStyle: 'google-maps',
    minZoom: 0.5,
    maxZoom: 5,
    debugMode: true
});

canvas.init();
```

### Drawing on Canvas
```javascript
const ctx = canvas.getContext();

// Apply viewport transformation
canvas.applyTransform(ctx);

// Draw in world coordinates
ctx.fillStyle = 'red';
ctx.fillRect(100, 100, 50, 50); // Rectangle at world position (100, 100)

ctx.fillStyle = 'blue';
ctx.beginPath();
ctx.arc(200, 200, 30, 0, Math.PI * 2); // Circle at world position (200, 200)
ctx.fill();
```

### Viewport Culling for Performance
```javascript
const objects = [
    { x: 100, y: 100, width: 50, height: 50 },
    { x: 500, y: 300, width: 30, height: 30 },
    // ... many more objects
];

// Only render visible objects
const visibleObjects = canvas.cullRectangles(objects);
visibleObjects.forEach(obj => {
    // Render only visible objects
    ctx.fillRect(obj.x, obj.y, obj.width, obj.height);
});
```

### Event Handling
```javascript
// Listen for viewport changes
window.Events.on('canvas:viewport-changed', (data) => {
    console.log('Viewport changed:', data.panX, data.panY, data.zoom);
    // Re-render content
    redrawCanvas();
});

// Listen for pointer interactions
window.Events.on('canvas:pointer-down', (data) => {
    console.log('Clicked at world position:', data.world.x, data.world.y);
});
```

### State Persistence
```javascript
// Enable auto-save
canvas.enableAutoSave('my-app-canvas-state', 1000);

// Manual save/load
document.getElementById('save-btn').onclick = () => {
    canvas.saveState('my-saved-view');
};

document.getElementById('load-btn').onclick = () => {
    canvas.loadState('my-saved-view', true); // animated restore
};
```

### Control Style Switching
```javascript
// Switch between control styles
document.getElementById('style-toggle').onclick = () => {
    const current = canvas.getControlStyle();
    const newStyle = current === 'google-maps' ? 'logseq' : 'google-maps';
    canvas.setControlStyle(newStyle);
};

// Get style information
const styleInfo = canvas.getControlStyleInfo();
console.log('Google Maps style:', styleInfo['google-maps']);
```

## HTML Elements

The package provides semantic HTML elements for building canvas interfaces:

### Canvas Container
```html
<infinite-canvas id="my-canvas">
    <!-- Canvas will be created here -->
</infinite-canvas>
```

### Control Panel
```html
<canvas-controls>
    <canvas-zoom-controls>
        <canvas-zoom-in>+</canvas-zoom-in>
        <canvas-zoom-level>100%</canvas-zoom-level>
        <canvas-zoom-out>−</canvas-zoom-out>
    </canvas-zoom-controls>
    
    <canvas-control-style>
        <canvas-style-option data-style="google-maps" data-active="true">Maps</canvas-style-option>
        <canvas-style-option data-style="logseq">Logseq</canvas-style-option>
    </canvas-control-style>
    
    <canvas-control-button id="reset-view">⌂</canvas-control-button>
</canvas-controls>
```

### Performance Monitor
```html
<canvas-performance data-visible="true">
    <canvas-fps>FPS: 60</canvas-fps>
    <canvas-memory>Memory: 45 MB</canvas-memory>
    <canvas-viewport-info>Zoom: 150%</canvas-viewport-info>
</canvas-performance>
```

### Status Bar
```html
<canvas-status>
    <canvas-coordinates>World: (150, 200) | Screen: (400, 300)</canvas-coordinates>
    <canvas-help-text>Use mouse to pan and zoom</canvas-help-text>
</canvas-status>
```

## Events

### Emitted Events

#### Viewport Events
- `canvas:initialized` - Canvas system initialized
- `canvas:resized` - Canvas size changed
- `canvas:viewport-changed` - Pan or zoom changed
- `canvas:zoom-changed` - Zoom level changed
- `canvas:pan-changed` - Pan position changed

#### Interaction Events
- `canvas:pan-start` - Pan operation started
- `canvas:pan-move` - Pan operation in progress
- `canvas:pan-end` - Pan operation ended
- `canvas:pan-cancel` - Pan operation cancelled
- `canvas:pointer-down` - Pointer pressed
- `canvas:pointer-move` - Pointer moved
- `canvas:pointer-up` - Pointer released
- `canvas:wheel` - Mouse wheel used

#### Touch Events
- `canvas:touch-start` - Touch interaction started
- `canvas:touch-move` - Touch interaction in progress
- `canvas:touch-end` - Touch interaction ended
- `canvas:touch-cancel` - Touch interaction cancelled
- `canvas:pinch-start` - Pinch gesture started
- `canvas:pinch-move` - Pinch gesture in progress
- `canvas:pinch-end` - Pinch gesture ended

#### System Events
- `canvas:control-style-changed` - Control style switched
- `canvas:performance-stats` - Performance monitoring data
- `canvas:error` - Error occurred

### Listened Events
- `canvas:focus-point` - Request to pan to specific point
- `canvas:zoom-to-point` - Request to zoom to specific point
- `canvas:fit-content` - Request to fit content in viewport
- `canvas:reset-viewport` - Request to reset viewport
- `canvas:set-control-style` - Request to change control style

## Control Styles

### Google Maps Style (Default)
- **Pan**: Left click + drag
- **Zoom**: Scroll wheel at cursor position
- **Context Menu**: Right click available
- **Touch**: Single finger pan, pinch to zoom

### Logseq Style
- **Pan**: Middle click + drag or Shift + left click + drag
- **Zoom**: Scroll wheel at viewport center
- **Selection**: Left click available for other interactions
- **Touch**: Same as Google Maps style

## Keyboard Shortcuts

| Key | Action |
|-----|--------|
| Arrow Keys | Pan viewport |
| + / = | Zoom in |
| - | Zoom out |
| Home | Reset viewport |
| F | Fit content (emits event for external handling) |
| C | Toggle control style |
| D | Show debug information |
| Space + Drag | Alternative pan mode |

## Performance Considerations

### Rendering Optimization
- Use viewport culling to avoid rendering off-screen content
- Implement level-of-detail rendering for complex scenes
- Cache rendered content when possible
- Use `scheduleUpdate()` for smooth animations

### Memory Management
- Enable performance monitoring to track memory usage
- Use `clearCaches()` periodically to free memory
- Implement content streaming for very large datasets
- Clean up event listeners when destroying canvas

### Mobile Optimization
- Touch events are optimized for mobile devices
- High-DPI displays are automatically supported
- Responsive design adapts to different screen sizes
- Performance monitoring helps identify mobile bottlenecks

## Browser Support

- **Modern Browsers**: Full support for Chrome, Firefox, Safari, Edge
- **Canvas 2D**: Requires HTML5 Canvas 2D context support
- **Touch Events**: Touch-enabled devices supported
- **High-DPI**: Automatic device pixel ratio handling
- **ES6+**: Uses modern JavaScript features

## Integration Examples

### With Whiteboard Package
```javascript
// Canvas provides spatial foundation
const canvas = createInfiniteCanvas(container);
canvas.init();

// Whiteboard uses canvas for drawing
const whiteboard = createWhiteboard(canvas);
whiteboard.enableDrawing();
```

### With Node Editor
```javascript
// Canvas handles pan/zoom
const canvas = createInfiniteCanvas(container);

// Node editor places nodes in world coordinates
window.Events.on('canvas:pointer-down', (data) => {
    if (data.button === 0) { // Left click
        nodeEditor.createNode(data.world.x, data.world.y);
    }
});
```

### With Block System
```javascript
// Render blocks at world positions
const renderBlocks = () => {
    const ctx = canvas.getContext();
    canvas.applyTransform(ctx);
    
    const visibleBlocks = canvas.cullRectangles(allBlocks);
    visibleBlocks.forEach(block => {
        renderBlock(ctx, block);
    });
};

window.Events.on('canvas:viewport-changed', renderBlocks);
```

## Troubleshooting

### Common Issues

**Canvas not initializing:**
- Check that container element exists
- Verify Canvas 2D context is supported
- Ensure event system is initialized first

**Poor performance:**
- Enable viewport culling for large content sets
- Use performance monitoring to identify bottlenecks
- Implement level-of-detail rendering
- Check for memory leaks in content rendering

**Touch gestures not working:**
- Verify `enableTouch` option is true
- Check that touch events aren't being prevented elsewhere
- Ensure proper viewport meta tag for mobile

**Coordinate precision issues:**
- Stay within coordinate limits (-1M to +1M)
- Use appropriate zoom levels for content scale
- Consider coordinate normalization for extreme values

### Debug Mode
Enable debug mode for detailed logging:
```javascript
const canvas = createInfiniteCanvas(container, { debugMode: true });
```

## Demo

Run the interactive demo by opening `infinite-canvas.html` in a web browser. The demo showcases:

- Pan and zoom navigation with both control styles
- Touch gesture support on mobile devices
- Keyboard navigation and shortcuts
- Performance monitoring and optimization
- State persistence and restoration
- Event system integration
- Viewport culling demonstration

## Dependencies

### Required
- **fap-event-system**: Event communication system

### Optional
- Modern browser with Canvas 2D support
- Touch-enabled device for gesture support
- High-DPI display for pixel ratio optimization

## License

This package is part of the FAP (Functional Application Packages) ecosystem and follows the same licensing terms as the parent project.

## Contributing

When contributing to this package:

1. Maintain functional programming patterns
2. Follow semantic HTML conventions
3. Ensure comprehensive test coverage
4. Update documentation for API changes
5. Test on multiple devices and browsers
6. Verify performance with large datasets

## Changelog

### Version 1.0.0
- Initial release with core functionality
- Large coordinate space support (-1M to +1M)
- Google Maps and Logseq control styles
- Touch gesture support for mobile
- Keyboard navigation with shortcuts
- High-performance viewport culling
- State persistence with localStorage
- Comprehensive event system integration
- Performance monitoring and optimization
- Responsive design with dark mode support
- Complete CSS styling and semantic HTML
- Interactive demo with stress testing
- Full documentation and API reference