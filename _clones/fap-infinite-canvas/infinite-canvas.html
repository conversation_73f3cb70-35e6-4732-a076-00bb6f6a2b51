<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAP Infinite Canvas Demo</title>
    <link rel="stylesheet" href="fap-infinite-canvas.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
            line-height: 1.6;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e0e0e0;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .canvas-container {
            height: 600px;
            margin: 20px 0;
            position: relative;
        }
        
        .demo-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .demo-section h2 {
            margin-top: 0;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }
        
        .demo-controls {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin: 15px 0;
        }
        
        .demo-button {
            padding: 8px 16px;
            background: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .demo-button:hover {
            background: #0052a3;
        }
        
        .demo-button.secondary {
            background: #6c757d;
        }
        
        .demo-button.secondary:hover {
            background: #545b62;
        }
        
        .demo-button.danger {
            background: #dc3545;
        }
        
        .demo-button.danger:hover {
            background: #c82333;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }
        
        .status-ready {
            background: #d4edda;
            color: #155724;
        }
        
        .status-loading {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .info-panel {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .info-panel h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .coordinate-display {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #2d3748;
            color: #e2e8f0;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .demo-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .canvas-container {
                height: 400px;
            }
            
            .demo-controls {
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🎨 FAP Infinite Canvas Demo</h1>
            <p>Interactive demonstration of the large-scale spatial canvas system</p>
            <div>
                <span>System Status:</span>
                <span id="system-status" class="status-indicator status-loading">Initializing...</span>
            </div>
        </div>

        <!-- Main Canvas -->
        <div class="demo-section">
            <h2>Interactive Canvas</h2>
            <div class="canvas-container">
                <infinite-canvas id="main-canvas">
                    <canvas-loading id="canvas-loading">
                        <canvas-loading-spinner></canvas-loading-spinner>
                        <canvas-loading-text>Initializing canvas...</canvas-loading-text>
                    </canvas-loading>
                </infinite-canvas>
                
                <!-- Canvas Controls -->
                <canvas-controls id="canvas-controls">
                    <canvas-zoom-controls>
                        <canvas-zoom-in id="zoom-in">+</canvas-zoom-in>
                        <canvas-zoom-level id="zoom-level">100%</canvas-zoom-level>
                        <canvas-zoom-out id="zoom-out">−</canvas-zoom-out>
                    </canvas-zoom-controls>
                    
                    <canvas-control-style id="control-style">
                        <canvas-style-option data-style="google-maps" data-active="true">Maps</canvas-style-option>
                        <canvas-style-option data-style="logseq">Logseq</canvas-style-option>
                    </canvas-control-style>
                    
                    <canvas-control-button id="reset-view" title="Reset View (Home)">⌂</canvas-control-button>
                    <canvas-control-button id="fit-content" title="Fit Content (F)">⊞</canvas-control-button>
                    <canvas-control-button id="toggle-performance" title="Toggle Performance (P)">📊</canvas-control-button>
                </canvas-controls>
                
                <!-- Performance Monitor -->
                <canvas-performance id="performance-monitor" data-visible="false">
                    <canvas-fps id="fps-display">FPS: 60</canvas-fps>
                    <canvas-memory id="memory-display">Memory: 0 MB</canvas-memory>
                    <canvas-viewport-info id="viewport-info">Zoom: 100%</canvas-viewport-info>
                </canvas-performance>
                
                <!-- Status Bar -->
                <canvas-status id="canvas-status">
                    <canvas-coordinates id="coordinates">World: (0, 0) | Screen: (0, 0)</canvas-coordinates>
                    <canvas-help-text id="help-text">Use mouse to pan and zoom</canvas-help-text>
                </canvas-status>
            </div>
            
            <div class="demo-controls">
                <button class="demo-button" onclick="addSampleContent()">Add Sample Content</button>
                <button class="demo-button secondary" onclick="clearCanvas()">Clear Canvas</button>
                <button class="demo-button secondary" onclick="toggleGrid()">Toggle Grid</button>
                <button class="demo-button secondary" onclick="showShortcuts()">Keyboard Shortcuts</button>
                <button class="demo-button" onclick="saveState()">Save State</button>
                <button class="demo-button" onclick="loadState()">Load State</button>
                <button class="demo-button danger" onclick="stressTest()">Stress Test</button>
            </div>
        </div>

        <div class="demo-grid">
            <!-- Control Style Comparison -->
            <div class="demo-section">
                <h2>Control Styles</h2>
                <div class="info-panel">
                    <h3 id="current-style-title">Google Maps Style (Active)</h3>
                    <div id="current-style-description">
                        <p><strong>Pan:</strong> Left click + drag</p>
                        <p><strong>Zoom:</strong> Scroll wheel at cursor</p>
                        <p><strong>Context:</strong> Right click for menu</p>
                    </div>
                    <button class="demo-button" onclick="switchControlStyle()">Switch to Logseq Style</button>
                </div>
            </div>

            <!-- Viewport Information -->
            <div class="demo-section">
                <h2>Viewport Information</h2>
                <div class="coordinate-display" id="viewport-display">
                    Pan: (0, 0)<br>
                    Zoom: 1.00x<br>
                    Visible: (-400, -300) to (400, 300)<br>
                    Canvas: 800x600
                </div>
                <div class="demo-controls">
                    <button class="demo-button" onclick="panToOrigin()">Go to Origin</button>
                    <button class="demo-button" onclick="randomPan()">Random Location</button>
                </div>
            </div>
        </div>

        <!-- Performance Testing -->
        <div class="demo-section">
            <h2>Performance Testing</h2>
            <div class="demo-grid">
                <div>
                    <h3>Rendering Performance</h3>
                    <div class="info-panel">
                        <p><strong>Current FPS:</strong> <span id="current-fps">60</span></p>
                        <p><strong>Frame Time:</strong> <span id="frame-time">16.7ms</span></p>
                        <p><strong>Objects Rendered:</strong> <span id="objects-rendered">0</span></p>
                    </div>
                    <div class="demo-controls">
                        <button class="demo-button" onclick="startPerformanceTest()">Start Monitoring</button>
                        <button class="demo-button secondary" onclick="stopPerformanceTest()">Stop Monitoring</button>
                    </div>
                </div>
                
                <div>
                    <h3>Memory Usage</h3>
                    <div class="info-panel">
                        <p><strong>Used Memory:</strong> <span id="used-memory">0 MB</span></p>
                        <p><strong>Total Memory:</strong> <span id="total-memory">0 MB</span></p>
                        <p><strong>Memory Limit:</strong> <span id="memory-limit">0 MB</span></p>
                    </div>
                    <div class="demo-controls">
                        <button class="demo-button" onclick="clearCaches()">Clear Caches</button>
                        <button class="demo-button secondary" onclick="forceGC()">Force GC</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Event Log -->
        <div class="demo-section">
            <h2>Event Log</h2>
            <div class="coordinate-display" id="event-log" style="height: 200px; overflow-y: auto;">
                Canvas events will appear here...
            </div>
            <div class="demo-controls">
                <button class="demo-button secondary" onclick="clearEventLog()">Clear Log</button>
                <button class="demo-button secondary" onclick="toggleEventLogging()">Toggle Logging</button>
            </div>
        </div>
    </div>

    <!-- Keyboard Shortcuts Modal -->
    <canvas-shortcuts id="shortcuts-modal" data-visible="false">
        <canvas-shortcuts-header>Keyboard Shortcuts</canvas-shortcuts-header>
        <canvas-shortcut-item>
            <canvas-shortcut-description>Pan viewport</canvas-shortcut-description>
            <canvas-shortcut-key>Arrow Keys</canvas-shortcut-key>
        </canvas-shortcut-item>
        <canvas-shortcut-item>
            <canvas-shortcut-description>Zoom in/out</canvas-shortcut-description>
            <canvas-shortcut-key>+ / -</canvas-shortcut-key>
        </canvas-shortcut-item>
        <canvas-shortcut-item>
            <canvas-shortcut-description>Reset viewport</canvas-shortcut-description>
            <canvas-shortcut-key>Home</canvas-shortcut-key>
        </canvas-shortcut-item>
        <canvas-shortcut-item>
            <canvas-shortcut-description>Fit content</canvas-shortcut-description>
            <canvas-shortcut-key>F</canvas-shortcut-key>
        </canvas-shortcut-item>
        <canvas-shortcut-item>
            <canvas-shortcut-description>Toggle control style</canvas-shortcut-description>
            <canvas-shortcut-key>C</canvas-shortcut-key>
        </canvas-shortcut-item>
        <canvas-shortcut-item>
            <canvas-shortcut-description>Show debug info</canvas-shortcut-description>
            <canvas-shortcut-key>D</canvas-shortcut-key>
        </canvas-shortcut-item>
        <canvas-shortcut-item>
            <canvas-shortcut-description>Alternative pan mode</canvas-shortcut-description>
            <canvas-shortcut-key>Space + Drag</canvas-shortcut-key>
        </canvas-shortcut-item>
        <div style="text-align: center; margin-top: 15px;">
            <button class="demo-button secondary" onclick="hideShortcuts()">Close</button>
        </div>
    </canvas-shortcuts>

    <!-- Load Dependencies -->
    <script src="../fap-event-system/fap-event-system.js"></script>
    <script src="fap-infinite-canvas.js"></script>

    <script>
        // Global variables
        let canvas = null;
        let eventSystem = null;
        let performanceInterval = null;
        let eventLoggingEnabled = true;
        let sampleObjects = [];
        let gridVisible = false;

        // Initialize the demo
        function initializeDemo() {
            try {
                // Initialize event system (already available globally)
                eventSystem = window.Events || window.FAPEventSystem.Events;
                
                // Initialize canvas
                const container = document.getElementById('main-canvas');
                canvas = window.FAPInfiniteCanvas.createInfiniteCanvas(container, {
                    controlStyle: 'google-maps',
                    debugMode: true,
                    enableKeyboard: true,
                    enableTouch: true
                });
                
                if (canvas.init()) {
                    updateStatus('ready', 'System Ready');
                    hideLoading();
                    setupEventListeners();
                    setupUI();
                    addSampleContent();
                    
                    console.log('✅ Demo initialized successfully');
                } else {
                    throw new Error('Canvas initialization failed');
                }
            } catch (error) {
                console.error('❌ Demo initialization failed:', error);
                updateStatus('error', 'Initialization Failed');
                showError(error.message);
            }
        }

        // Update system status
        function updateStatus(status, message) {
            const indicator = document.getElementById('system-status');
            indicator.className = `status-indicator status-${status}`;
            indicator.textContent = message;
        }

        // Show/hide loading state
        function hideLoading() {
            const loading = document.getElementById('canvas-loading');
            if (loading) loading.style.display = 'none';
        }

        function showError(message) {
            const container = document.getElementById('main-canvas');
            container.innerHTML = `
                <canvas-error>
                    <canvas-error-icon>⚠️</canvas-error-icon>
                    <canvas-error-message>Canvas initialization failed</canvas-error-message>
                    <canvas-error-details>${message}</canvas-error-details>
                </canvas-error>
            `;
        }

        // Set up event listeners
        function setupEventListeners() {
            // Canvas events
            const canvasEvents = [
                'canvas:viewport-changed',
                'canvas:zoom-changed',
                'canvas:pan-changed',
                'canvas:pointer-move',
                'canvas:control-style-changed',
                'canvas:performance-stats'
            ];

            canvasEvents.forEach(eventType => {
                eventSystem.on(eventType, (data) => {
                    if (eventLoggingEnabled) {
                        logEvent(eventType, data);
                    }
                    
                    // Handle specific events
                    if (eventType === 'canvas:viewport-changed') {
                        updateViewportDisplay(data);
                    } else if (eventType === 'canvas:pointer-move') {
                        updateCoordinateDisplay(data);
                    } else if (eventType === 'canvas:control-style-changed') {
                        updateControlStyleDisplay(data.newStyle);
                    } else if (eventType === 'canvas:performance-stats') {
                        updatePerformanceDisplay(data);
                    }
                });
            });

            // Mouse tracking for coordinate display
            const canvasElement = canvas.getCanvas();
            if (canvasElement) {
                canvasElement.addEventListener('mousemove', (e) => {
                    const rect = canvasElement.getBoundingClientRect();
                    const screenX = e.clientX - rect.left;
                    const screenY = e.clientY - rect.top;
                    const world = canvas.screenToWorld(screenX, screenY);
                    
                    updateCoordinateDisplay({
                        screen: { x: screenX, y: screenY },
                        world: world
                    });
                });
            }
        }

        // Set up UI controls
        function setupUI() {
            // Zoom controls
            document.getElementById('zoom-in').onclick = () => {
                const center = canvas.getViewportCenter();
                canvas.zoomToPoint(center.x, center.y, canvas.getState().zoom * 1.2);
            };
            
            document.getElementById('zoom-out').onclick = () => {
                const center = canvas.getViewportCenter();
                canvas.zoomToPoint(center.x, center.y, canvas.getState().zoom / 1.2);
            };
            
            // Control style switching
            document.querySelectorAll('canvas-style-option').forEach(option => {
                option.onclick = () => {
                    const style = option.getAttribute('data-style');
                    canvas.setControlStyle(style);
                };
            });
            
            // Other controls
            document.getElementById('reset-view').onclick = () => canvas.resetViewport(true);
            document.getElementById('fit-content').onclick = () => fitToSampleContent();
            document.getElementById('toggle-performance').onclick = () => togglePerformanceMonitor();
        }

        // Sample content management
        function addSampleContent() {
            sampleObjects = [];
            const ctx = canvas.getContext();
            if (!ctx) return;

            // Add various shapes and text at different world coordinates
            const samples = [
                { type: 'rect', x: -200, y: -100, width: 100, height: 60, color: '#ff6b6b', label: 'Rectangle' },
                { type: 'circle', x: 100, y: -50, radius: 40, color: '#4ecdc4', label: 'Circle' },
                { type: 'text', x: 0, y: 0, text: 'Origin (0,0)', color: '#45b7d1', size: 16 },
                { type: 'rect', x: -500, y: 200, width: 150, height: 80, color: '#96ceb4', label: 'Far Left' },
                { type: 'circle', x: 400, y: 300, radius: 30, color: '#feca57', label: 'Far Right' },
                { type: 'text', x: -100, y: 150, text: 'Sample Text', color: '#ff9ff3', size: 14 },
                { type: 'line', x1: -300, y1: -200, x2: 300, y2: 200, color: '#54a0ff', width: 3 }
            ];

            sampleObjects = samples;
            renderSampleContent();
        }

        function renderSampleContent() {
            const ctx = canvas.getContext();
            if (!ctx) return;

            // Clear canvas
            ctx.clearRect(-10000, -10000, 20000, 20000);
            
            // Apply transform
            canvas.applyTransform();
            
            // Draw grid if enabled
            if (gridVisible) {
                drawGrid(ctx);
            }
            
            // Draw sample objects
            let renderedCount = 0;
            sampleObjects.forEach(obj => {
                if (canvas.isRectVisible({ x: obj.x - 50, y: obj.y - 50, width: 100, height: 100 })) {
                    drawObject(ctx, obj);
                    renderedCount++;
                }
            });
            
            // Update rendered count
            document.getElementById('objects-rendered').textContent = renderedCount;
        }

        function drawObject(ctx, obj) {
            ctx.save();
            
            switch (obj.type) {
                case 'rect':
                    ctx.fillStyle = obj.color;
                    ctx.fillRect(obj.x, obj.y, obj.width, obj.height);
                    ctx.strokeStyle = '#333';
                    ctx.strokeRect(obj.x, obj.y, obj.width, obj.height);
                    
                    // Label
                    ctx.fillStyle = '#333';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(obj.label, obj.x + obj.width/2, obj.y + obj.height/2);
                    break;
                    
                case 'circle':
                    ctx.beginPath();
                    ctx.arc(obj.x, obj.y, obj.radius, 0, Math.PI * 2);
                    ctx.fillStyle = obj.color;
                    ctx.fill();
                    ctx.strokeStyle = '#333';
                    ctx.stroke();
                    
                    // Label
                    ctx.fillStyle = '#333';
                    ctx.font = '12px Arial';
                    ctx.textAlign = 'center';
                    ctx.fillText(obj.label, obj.x, obj.y + 4);
                    break;
                    
                case 'text':
                    ctx.fillStyle = obj.color;
                    ctx.font = `${obj.size}px Arial`;
                    ctx.textAlign = 'center';
                    ctx.fillText(obj.text, obj.x, obj.y);
                    break;
                    
                case 'line':
                    ctx.beginPath();
                    ctx.moveTo(obj.x1, obj.y1);
                    ctx.lineTo(obj.x2, obj.y2);
                    ctx.strokeStyle = obj.color;
                    ctx.lineWidth = obj.width;
                    ctx.stroke();
                    break;
            }
            
            ctx.restore();
        }

        function drawGrid(ctx) {
            const bounds = canvas.getVisibleBounds();
            const gridSize = 50;
            
            ctx.save();
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 1;
            
            // Vertical lines
            for (let x = Math.floor(bounds.left / gridSize) * gridSize; x <= bounds.right; x += gridSize) {
                ctx.beginPath();
                ctx.moveTo(x, bounds.top);
                ctx.lineTo(x, bounds.bottom);
                ctx.stroke();
            }
            
            // Horizontal lines
            for (let y = Math.floor(bounds.top / gridSize) * gridSize; y <= bounds.bottom; y += gridSize) {
                ctx.beginPath();
                ctx.moveTo(bounds.left, y);
                ctx.lineTo(bounds.right, y);
                ctx.stroke();
            }
            
            ctx.restore();
        }

        // UI update functions
        function updateViewportDisplay(data) {
            const display = document.getElementById('viewport-display');
            const bounds = canvas.getVisibleBounds();
            
            display.innerHTML = `
                Pan: (${Math.round(data.panX)}, ${Math.round(data.panY)})<br>
                Zoom: ${data.zoom.toFixed(2)}x<br>
                Visible: (${Math.round(bounds.left)}, ${Math.round(bounds.top)}) to (${Math.round(bounds.right)}, ${Math.round(bounds.bottom)})<br>
                Canvas: ${canvas.getDimensions().width}x${canvas.getDimensions().height}
            `;
            
            // Update zoom level display
            document.getElementById('zoom-level').textContent = Math.round(data.zoom * 100) + '%';
            
            // Re-render content
            renderSampleContent();
        }

        function updateCoordinateDisplay(data) {
            if (data.screen && data.world) {
                const coords = document.getElementById('coordinates');
                coords.textContent = `World: (${Math.round(data.world.x)}, ${Math.round(data.world.y)}) | Screen: (${Math.round(data.screen.x)}, ${Math.round(data.screen.y)})`;
            }
        }

        function updateControlStyleDisplay(style) {
            // Update active style indicator
            document.querySelectorAll('canvas-style-option').forEach(option => {
                option.setAttribute('data-active', option.getAttribute('data-style') === style);
            });
            
            // Update description
            const title = document.getElementById('current-style-title');
            const description = document.getElementById('current-style-description');
            const button = document.querySelector('#current-style-description + button');
            
            if (style === 'google-maps') {
                title.textContent = 'Google Maps Style (Active)';
                description.innerHTML = `
                    <p><strong>Pan:</strong> Left click + drag</p>
                    <p><strong>Zoom:</strong> Scroll wheel at cursor</p>
                    <p><strong>Context:</strong> Right click for menu</p>
                `;
                button.textContent = 'Switch to Logseq Style';
            } else {
                title.textContent = 'Logseq Style (Active)';
                description.innerHTML = `
                    <p><strong>Pan:</strong> Middle click + drag or Shift + left drag</p>
                    <p><strong>Zoom:</strong> Scroll wheel at center</p>
                    <p><strong>Context:</strong> Right click available for other actions</p>
                `;
                button.textContent = 'Switch to Google Maps Style';
            }
        }

        function updatePerformanceDisplay(data) {
            document.getElementById('current-fps').textContent = data.fps || 60;
            document.getElementById('frame-time').textContent = (1000 / (data.fps || 60)).toFixed(1) + 'ms';
            
            if (data.memoryUsage) {
                document.getElementById('used-memory').textContent = data.memoryUsage.used + ' MB';
                document.getElementById('total-memory').textContent = data.memoryUsage.total + ' MB';
                document.getElementById('memory-limit').textContent = data.memoryUsage.limit + ' MB';
            }
            
            // Update performance monitor
            const fpsDisplay = document.getElementById('fps-display');
            const memoryDisplay = document.getElementById('memory-display');
            const viewportInfo = document.getElementById('viewport-info');
            
            fpsDisplay.textContent = `FPS: ${data.fps || 60}`;
            fpsDisplay.setAttribute('data-warning', data.fps < 45);
            fpsDisplay.setAttribute('data-critical', data.fps < 30);
            
            if (data.memoryUsage) {
                memoryDisplay.textContent = `Memory: ${data.memoryUsage.used} MB`;
            }
            
            if (data.viewport) {
                viewportInfo.textContent = `Zoom: ${Math.round(data.viewport.zoom * 100)}%`;
            }
        }

        // Event logging
        function logEvent(eventType, data) {
            const log = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            const entry = `[${timestamp}] ${eventType}: ${JSON.stringify(data, null, 2)}\n`;
            
            log.textContent += entry;
            log.scrollTop = log.scrollHeight;
            
            // Limit log size
            const lines = log.textContent.split('\n');
            if (lines.length > 100) {
                log.textContent = lines.slice(-50).join('\n');
            }
        }

        // Demo functions
        function clearCanvas() {
            sampleObjects = [];
            renderSampleContent();
        }

        function toggleGrid() {
            gridVisible = !gridVisible;
            renderSampleContent();
        }

        function switchControlStyle() {
            const current = canvas.getControlStyle();
            const newStyle = current === 'google-maps' ? 'logseq' : 'google-maps';
            canvas.setControlStyle(newStyle);
        }

        function fitToSampleContent() {
            if (sampleObjects.length === 0) return;
            
            let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
            
            sampleObjects.forEach(obj => {
                const bounds = getObjectBounds(obj);
                minX = Math.min(minX, bounds.left);
                minY = Math.min(minY, bounds.top);
                maxX = Math.max(maxX, bounds.right);
                maxY = Math.max(maxY, bounds.bottom);
            });
            
            canvas.fitToContent({ left: minX, top: minY, right: maxX, bottom: maxY }, 50);
        }

        function getObjectBounds(obj) {
            switch (obj.type) {
                case 'rect':
                    return { left: obj.x, top: obj.y, right: obj.x + obj.width, bottom: obj.y + obj.height };
                case 'circle':
                    return { left: obj.x - obj.radius, top: obj.y - obj.radius, right: obj.x + obj.radius, bottom: obj.y + obj.radius };
                case 'text':
                    return { left: obj.x - 50, top: obj.y - 10, right: obj.x + 50, bottom: obj.y + 10 };
                case 'line':
                    return { left: Math.min(obj.x1, obj.x2), top: Math.min(obj.y1, obj.y2), right: Math.max(obj.x1, obj.x2), bottom: Math.max(obj.y1, obj.y2) };
                default:
                    return { left: 0, top: 0, right: 0, bottom: 0 };
            }
        }

        function panToOrigin() {
            canvas.panToPoint(0, 0, true);
        }

        function randomPan() {
            const x = (Math.random() - 0.5) * 2000;
            const y = (Math.random() - 0.5) * 2000;
            canvas.panToPoint(x, y, true);
        }

        function togglePerformanceMonitor() {
            const monitor = document.getElementById('performance-monitor');
            const visible = monitor.getAttribute('data-visible') === 'true';
            monitor.setAttribute('data-visible', !visible);
        }

        function startPerformanceTest() {
            if (performanceInterval) return;
            
            performanceInterval = canvas.startPerformanceMonitoring(1000);
            console.log('Performance monitoring started');
        }

        function stopPerformanceTest() {
            if (performanceInterval) {
                canvas.stopPerformanceMonitoring();
                performanceInterval = null;
                console.log('Performance monitoring stopped');
            }
        }

        function clearCaches() {
            canvas.clearCaches();
            console.log('Caches cleared');
        }

        function forceGC() {
            if (window.gc) {
                window.gc();
                console.log('Garbage collection forced');
            } else {
                console.log('Garbage collection not available');
            }
        }

        function saveState() {
            if (canvas.saveState()) {
                console.log('State saved successfully');
                alert('Canvas state saved to localStorage');
            } else {
                alert('Failed to save state');
            }
        }

        function loadState() {
            if (canvas.loadState(undefined, true)) {
                console.log('State loaded successfully');
            } else {
                alert('No saved state found or failed to load');
            }
        }

        function stressTest() {
            // Add many objects for stress testing
            for (let i = 0; i < 1000; i++) {
                sampleObjects.push({
                    type: 'circle',
                    x: (Math.random() - 0.5) * 4000,
                    y: (Math.random() - 0.5) * 4000,
                    radius: Math.random() * 20 + 5,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`,
                    label: `Obj ${i}`
                });
            }
            
            renderSampleContent();
            console.log('Stress test: Added 1000 objects');
        }

        function showShortcuts() {
            document.getElementById('shortcuts-modal').setAttribute('data-visible', 'true');
        }

        function hideShortcuts() {
            document.getElementById('shortcuts-modal').setAttribute('data-visible', 'false');
        }

        function clearEventLog() {
            document.getElementById('event-log').textContent = 'Event log cleared...\n';
        }

        function toggleEventLogging() {
            eventLoggingEnabled = !eventLoggingEnabled;
            console.log('Event logging:', eventLoggingEnabled ? 'enabled' : 'disabled');
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeDemo);

        // Handle page visibility for performance
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                stopPerformanceTest();
            }
        });
    </script>
</body>
</html>