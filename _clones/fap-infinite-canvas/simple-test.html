<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Canvas Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            width: 600px;
            height: 400px;
            border: 2px solid #333;
            position: relative;
            background: white;
            margin: 20px auto;
        }
        
        .instructions {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status {
            text-align: center;
            padding: 10px;
            margin: 10px 0;
            background: #e8f4fd;
            border-radius: 4px;
            font-weight: bold;
        }
        
        .error {
            background: #ffe6e6;
            color: #d00;
        }
        
        .success {
            background: #e6ffe6;
            color: #080;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h1>🎨 Infinite Canvas Zoom Test</h1>
        <p><strong>Instructions:</strong></p>
        <ol>
            <li>Move your mouse over any colored shape in the canvas below</li>
            <li>Use your mouse wheel to zoom in/out</li>
            <li>The shape should grow/shrink while staying under your cursor</li>
            <li>The grid helps visualize the zoom behavior</li>
        </ol>
        
        <div class="status" id="status">Loading...</div>
    </div>
    
    <div class="container" id="canvas-container">
        <!-- Canvas will be created here -->
    </div>

    <!-- Load Dependencies -->
    <script src="../fap-event-system/fap-event-system.js"></script>
    <script src="fap-infinite-canvas.js"></script>

    <script>
        let canvas = null;
        
        function updateStatus(message, isError = false, isSuccess = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status' + (isError ? ' error' : '') + (isSuccess ? ' success' : '');
        }
        
        function drawContent() {
            const ctx = canvas.getContext();
            if (!ctx) return;
            
            // Clear and apply transform
            ctx.clearRect(-5000, -5000, 10000, 10000);
            canvas.applyTransform(ctx);
            
            // Draw simple grid
            const bounds = canvas.getVisibleBounds();
            ctx.strokeStyle = '#eee';
            ctx.lineWidth = 1;
            
            for (let x = Math.floor(bounds.left / 50) * 50; x <= bounds.right; x += 50) {
                ctx.beginPath();
                ctx.moveTo(x, bounds.top);
                ctx.lineTo(x, bounds.bottom);
                ctx.stroke();
            }
            
            for (let y = Math.floor(bounds.top / 50) * 50; y <= bounds.bottom; y += 50) {
                ctx.beginPath();
                ctx.moveTo(bounds.left, y);
                ctx.lineTo(bounds.right, y);
                ctx.stroke();
            }
            
            // Draw axes
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(bounds.left, 0);
            ctx.lineTo(bounds.right, 0);
            ctx.moveTo(0, bounds.top);
            ctx.lineTo(0, bounds.bottom);
            ctx.stroke();
            
            // Draw test shapes
            ctx.fillStyle = 'red';
            ctx.fillRect(-10, -10, 20, 20); // Origin
            
            ctx.fillStyle = 'blue';
            ctx.fillRect(100, 50, 60, 40); // Blue rectangle
            
            ctx.fillStyle = 'green';
            ctx.beginPath();
            ctx.arc(200, 100, 30, 0, Math.PI * 2);
            ctx.fill(); // Green circle
            
            ctx.fillStyle = 'orange';
            ctx.fillRect(-150, 80, 50, 50); // Orange square
            
            // Labels
            ctx.fillStyle = 'black';
            ctx.font = '14px Arial';
            ctx.fillText('Origin', 15, -15);
            ctx.fillText('Blue Rect', 100, 40);
            ctx.fillText('Green Circle', 170, 70);
            ctx.fillText('Orange Square', -150, 70);
        }
        
        function init() {
            try {
                updateStatus('Initializing canvas...');
                
                const container = document.getElementById('canvas-container');
                canvas = window.FAPInfiniteCanvas.createInfiniteCanvas(container, {
                    controlStyle: 'google-maps',
                    debugMode: false
                });
                
                if (canvas.init()) {
                    updateStatus('✅ Canvas ready! Hover over shapes and use mouse wheel to zoom.', false, true);
                    
                    // Set up redraw on viewport changes
                    if (window.Events) {
                        window.Events.on('canvas:viewport-changed', (data) => {
                            console.log('Viewport changed:', {
                                pan: [Math.round(data.panX), Math.round(data.panY)],
                                zoom: data.zoom.toFixed(2)
                            });
                            drawContent();
                        });
                        
                        window.Events.on('canvas:wheel', (data) => {
                            console.log('Wheel at:', {
                                screen: [Math.round(data.screen.x), Math.round(data.screen.y)],
                                world: [Math.round(data.world.x), Math.round(data.world.y)],
                                delta: data.delta
                            });
                        });
                    }
                    
                    drawContent();
                } else {
                    throw new Error('Canvas initialization failed');
                }
                
            } catch (error) {
                updateStatus('❌ Error: ' + error.message, true);
                console.error('Canvas test failed:', error);
            }
        }
        
        document.addEventListener('DOMContentLoaded', init);
    </script>
</body>
</html>