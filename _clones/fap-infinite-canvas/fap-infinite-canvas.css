/**
 * FAP Infinite Canvas - CSS Styling
 * 
 * Styles for infinite canvas system including:
 * - Canvas container and responsive layout
 * - Control panels and UI elements
 * - Performance indicators and debug tools
 * - Accessibility and mobile support
 */

/* Canvas Container */
infinite-canvas {
  display: block;
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background: #fafafa;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

infinite-canvas canvas {
  display: block;
  outline: none;
  user-select: none;
  touch-action: none;
}

/* Canvas States */
infinite-canvas[data-dragging="true"] {
  cursor: grabbing !important;
}

infinite-canvas[data-control-style="google-maps"] {
  cursor: grab;
}

infinite-canvas[data-control-style="logseq"] {
  cursor: default;
}

infinite-canvas:focus-within {
  border-color: #0066cc;
  box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.1);
}

/* Control Panel */
canvas-controls {
  display: flex;
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 8px;
  gap: 8px;
  backdrop-filter: blur(10px);
  z-index: 10;
}

canvas-control-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: white;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #333;
}

canvas-control-button:hover {
  background: #f5f5f5;
  border-color: #0066cc;
  color: #0066cc;
}

canvas-control-button:active {
  background: #e8f4fd;
  transform: scale(0.95);
}

canvas-control-button[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

canvas-control-button[data-active="true"] {
  background: #0066cc;
  color: white;
  border-color: #0066cc;
}

/* Zoom Controls */
canvas-zoom-controls {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

canvas-zoom-in,
canvas-zoom-out {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: white;
  border: 1px solid #d0d0d0;
  border-radius: 3px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  transition: all 0.2s ease;
}

canvas-zoom-in:hover,
canvas-zoom-out:hover {
  background: #f0f8ff;
  border-color: #0066cc;
  color: #0066cc;
}

canvas-zoom-level {
  display: block;
  text-align: center;
  font-size: 11px;
  color: #666;
  padding: 2px 4px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 2px;
  min-width: 40px;
}

/* Control Style Selector */
canvas-control-style {
  display: flex;
  background: white;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  overflow: hidden;
}

canvas-style-option {
  display: inline-flex;
  align-items: center;
  padding: 6px 10px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-right: 1px solid #e0e0e0;
  color: #666;
}

canvas-style-option:last-child {
  border-right: none;
}

canvas-style-option:hover {
  background: #f5f5f5;
  color: #333;
}

canvas-style-option[data-active="true"] {
  background: #0066cc;
  color: white;
}

/* Performance Monitor */
canvas-performance {
  display: block;
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  z-index: 10;
  min-width: 120px;
}

canvas-performance[data-visible="false"] {
  display: none;
}

canvas-fps {
  display: block;
  color: #4ade80;
}

canvas-fps[data-warning="true"] {
  color: #fbbf24;
}

canvas-fps[data-critical="true"] {
  color: #ef4444;
}

canvas-memory {
  display: block;
  color: #60a5fa;
  margin-top: 2px;
}

canvas-viewport-info {
  display: block;
  color: #a78bfa;
  margin-top: 2px;
  font-size: 11px;
}

/* Status Bar */
canvas-status {
  display: flex;
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  color: #666;
  backdrop-filter: blur(10px);
  z-index: 10;
  justify-content: space-between;
  align-items: center;
}

canvas-coordinates {
  display: block;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

canvas-help-text {
  display: block;
  font-style: italic;
}

/* Loading State */
canvas-loading {
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  align-items: center;
  justify-content: center;
  z-index: 20;
  flex-direction: column;
  gap: 16px;
}

canvas-loading-spinner {
  display: block;
  width: 32px;
  height: 32px;
  border: 3px solid #e0e0e0;
  border-top: 3px solid #0066cc;
  border-radius: 50%;
  animation: canvas-spin 1s linear infinite;
}

@keyframes canvas-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

canvas-loading-text {
  display: block;
  color: #666;
  font-size: 14px;
}

/* Error State */
canvas-error {
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  align-items: center;
  justify-content: center;
  z-index: 20;
  flex-direction: column;
  gap: 16px;
  color: #dc3545;
}

canvas-error-icon {
  display: block;
  font-size: 48px;
}

canvas-error-message {
  display: block;
  font-size: 16px;
  text-align: center;
  max-width: 400px;
}

canvas-error-details {
  display: block;
  font-size: 12px;
  color: #666;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  max-width: 500px;
  overflow-x: auto;
}

/* Keyboard Shortcuts Help */
canvas-shortcuts {
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  z-index: 30;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

canvas-shortcuts[data-visible="false"] {
  display: none;
}

canvas-shortcuts-header {
  display: block;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
  text-align: center;
}

canvas-shortcut-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  border-bottom: 1px solid #f0f0f0;
}

canvas-shortcut-item:last-child {
  border-bottom: none;
}

canvas-shortcut-key {
  display: inline-block;
  background: #f5f5f5;
  border: 1px solid #d0d0d0;
  border-radius: 3px;
  padding: 2px 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  color: #333;
}

canvas-shortcut-description {
  display: block;
  color: #666;
  font-size: 13px;
}

/* Responsive Design */
@media (max-width: 768px) {
  canvas-controls {
    top: 5px;
    right: 5px;
    padding: 6px;
    gap: 6px;
  }
  
  canvas-control-button {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  canvas-performance {
    top: 5px;
    left: 5px;
    padding: 6px 8px;
    font-size: 11px;
  }
  
  canvas-status {
    bottom: 5px;
    left: 5px;
    right: 5px;
    padding: 4px 8px;
    font-size: 11px;
  }
  
  canvas-shortcuts {
    max-width: 90vw;
    padding: 16px;
  }
}

@media (max-width: 480px) {
  canvas-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  canvas-control-style {
    flex-direction: column;
  }
  
  canvas-style-option {
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
    text-align: center;
  }
  
  canvas-style-option:last-child {
    border-bottom: none;
  }
  
  canvas-status {
    flex-direction: column;
    gap: 4px;
    text-align: center;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  infinite-canvas {
    background: #1a1a1a;
    border-color: #404040;
  }
  
  canvas-controls {
    background: rgba(40, 40, 40, 0.95);
    border-color: #404040;
  }
  
  canvas-control-button {
    background: #2a2a2a;
    border-color: #404040;
    color: #e0e0e0;
  }
  
  canvas-control-button:hover {
    background: #3a3a3a;
    border-color: #0066cc;
    color: #4da6ff;
  }
  
  canvas-control-button:active {
    background: #1a3a5c;
  }
  
  canvas-zoom-in,
  canvas-zoom-out {
    background: #2a2a2a;
    border-color: #404040;
    color: #e0e0e0;
  }
  
  canvas-zoom-in:hover,
  canvas-zoom-out:hover {
    background: #3a3a3a;
    border-color: #0066cc;
    color: #4da6ff;
  }
  
  canvas-zoom-level {
    background: rgba(40, 40, 40, 0.8);
    color: #b0b0b0;
  }
  
  canvas-control-style {
    background: #2a2a2a;
    border-color: #404040;
  }
  
  canvas-style-option {
    color: #b0b0b0;
    border-color: #404040;
  }
  
  canvas-style-option:hover {
    background: #3a3a3a;
    color: #e0e0e0;
  }
  
  canvas-status {
    background: rgba(40, 40, 40, 0.95);
    border-color: #404040;
    color: #b0b0b0;
  }
  
  canvas-shortcuts {
    background: rgba(40, 40, 40, 0.98);
    border-color: #404040;
  }
  
  canvas-shortcuts-header {
    color: #e0e0e0;
  }
  
  canvas-shortcut-item {
    border-color: #404040;
  }
  
  canvas-shortcut-key {
    background: #3a3a3a;
    border-color: #505050;
    color: #e0e0e0;
  }
  
  canvas-shortcut-description {
    color: #b0b0b0;
  }
  
  canvas-loading {
    background: rgba(26, 26, 26, 0.9);
  }
  
  canvas-loading-text {
    color: #b0b0b0;
  }
  
  canvas-error {
    background: rgba(26, 26, 26, 0.95);
  }
  
  canvas-error-details {
    background: #2a2a2a;
    color: #b0b0b0;
  }
}

/* Print Styles */
@media print {
  canvas-controls,
  canvas-performance,
  canvas-status,
  canvas-shortcuts {
    display: none !important;
  }
  
  infinite-canvas {
    border: none;
    background: white;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  infinite-canvas {
    border-width: 2px;
  }
  
  canvas-control-button,
  canvas-zoom-in,
  canvas-zoom-out {
    border-width: 2px;
  }
  
  canvas-control-button:hover,
  canvas-zoom-in:hover,
  canvas-zoom-out:hover {
    border-width: 3px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  canvas-control-button,
  canvas-zoom-in,
  canvas-zoom-out,
  canvas-style-option {
    transition: none;
  }
  
  canvas-loading-spinner {
    animation: none;
  }
}