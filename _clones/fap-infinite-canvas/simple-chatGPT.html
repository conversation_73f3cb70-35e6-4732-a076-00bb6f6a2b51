<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>Infinite Canvas</title>
  <style>
    html, body { margin: 0; height: 100%; overflow: hidden; }
    canvas { display: block; background: #fafafa; }
  </style>
</head>
<body>
  <canvas id="canvas"></canvas>
  <script type="module">
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');

    const state = {
      panX: 0,
      panY: 0,
      zoom: 1,
      dragging: false,
      lastX: 0,
      lastY: 0,
    };

    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      draw();
    };

    const draw = () => {
      const { panX, panY, zoom } = state;
      ctx.setTransform(zoom, 0, 0, zoom, panX, panY);
      ctx.clearRect(-panX / zoom, -panY / zoom, canvas.width / zoom, canvas.height / zoom);

      // Grid
      const step = 100;
      ctx.beginPath();
      for (let x = -10000; x < 10000; x += step) {
        ctx.moveTo(x, -10000);
        ctx.lineTo(x, 10000);
      }
      for (let y = -10000; y < 10000; y += step) {
        ctx.moveTo(-10000, y);
        ctx.lineTo(10000, y);
      }
      ctx.strokeStyle = '#ccc';
      ctx.stroke();

      // Origin
      ctx.beginPath();
      ctx.arc(0, 0, 10, 0, 2 * Math.PI);
      ctx.fillStyle = 'red';
      ctx.fill();
    };

    const onMouseDown = (e) => {
      state.dragging = true;
      state.lastX = e.clientX;
      state.lastY = e.clientY;
    };

    const onMouseMove = (e) => {
      if (!state.dragging) return;
      const dx = e.clientX - state.lastX;
      const dy = e.clientY - state.lastY;
      state.panX += dx;
      state.panY += dy;
      state.lastX = e.clientX;
      state.lastY = e.clientY;
      draw();
    };

    const onMouseUp = () => {
      state.dragging = false;
    };

    const onWheel = (e) => {
      const { offsetX, offsetY, deltaY } = e;
      const zoomFactor = 1.1;
      const zoom = deltaY < 0 ? zoomFactor : 1 / zoomFactor;

      const wx = (offsetX - state.panX) / state.zoom;
      const wy = (offsetY - state.panY) / state.zoom;

      state.zoom *= zoom;
      state.panX = offsetX - wx * state.zoom;
      state.panY = offsetY - wy * state.zoom;
      draw();
    };

    window.addEventListener('resize', resizeCanvas);
    canvas.addEventListener('mousedown', onMouseDown);
    canvas.addEventListener('mousemove', onMouseMove);
    canvas.addEventListener('mouseup', onMouseUp);
    canvas.addEventListener('mouseleave', onMouseUp);
    canvas.addEventListener('wheel', onWheel, { passive: false });

    resizeCanvas();
  </script>
</body>
</html>
