<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Canvas Test</title>
    <link rel="stylesheet" href="fap-infinite-canvas.css">
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .container {
            width: 800px;
            height: 600px;
            border: 2px solid #ccc;
            position: relative;
        }
        
        .status {
            margin: 10px 0;
            padding: 10px;
            background: #f0f0f0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>FAP Infinite Canvas Test</h1>
    
    <div class="status" id="status">Initializing...</div>
    
    <div class="container" id="canvas-container">
        <!-- Canvas will be created here -->
    </div>
    
    <div style="margin-top: 20px;">
        <button onclick="testPan()">Test Pan</button>
        <button onclick="testZoom()">Test Zoom</button>
        <button onclick="testReset()">Reset View</button>
        <button onclick="showState()">Show State</button>
        <button onclick="toggleGrid()">Toggle Grid</button>
        <button onclick="addMoreContent()">Add More Content</button>
    </div>

    <!-- Load Dependencies -->
    <script src="../fap-event-system/fap-event-system.js"></script>
    <script src="fap-infinite-canvas.js"></script>

    <script>
        let canvas = null;
        
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }
        
        function initializeTest() {
            try {
                updateStatus('Initializing event system...');
                
                // Event system should be available globally
                if (!window.Events) {
                    throw new Error('Event system not available');
                }
                
                updateStatus('Creating canvas...');
                
                // Create canvas
                const container = document.getElementById('canvas-container');
                canvas = window.FAPInfiniteCanvas.createInfiniteCanvas(container, {
                    controlStyle: 'google-maps',
                    debugMode: false  // Set to true only when needed for debugging
                });
                
                updateStatus('Initializing canvas...');
                
                if (canvas.init()) {
                    updateStatus('✅ Canvas initialized successfully! Try panning and zooming.');
                    
                    // Draw some test content
                    drawTestContent();
                } else {
                    throw new Error('Canvas initialization failed');
                }
                
            } catch (error) {
                updateStatus('❌ Error: ' + error.message);
                console.error('Test failed:', error);
            }
        }
        
        function drawGrid(ctx) {
            const bounds = canvas.getVisibleBounds();
            const gridSize = 50;
            
            ctx.save();
            ctx.strokeStyle = '#e0e0e0';
            ctx.lineWidth = 1;
            
            // Vertical lines
            for (let x = Math.floor(bounds.left / gridSize) * gridSize; x <= bounds.right; x += gridSize) {
                ctx.beginPath();
                ctx.moveTo(x, bounds.top);
                ctx.lineTo(x, bounds.bottom);
                ctx.stroke();
            }
            
            // Horizontal lines
            for (let y = Math.floor(bounds.top / gridSize) * gridSize; y <= bounds.bottom; y += gridSize) {
                ctx.beginPath();
                ctx.moveTo(bounds.left, y);
                ctx.lineTo(bounds.right, y);
                ctx.stroke();
            }
            
            // Draw axis lines more prominently
            ctx.strokeStyle = '#ccc';
            ctx.lineWidth = 2;
            
            // X-axis
            ctx.beginPath();
            ctx.moveTo(bounds.left, 0);
            ctx.lineTo(bounds.right, 0);
            ctx.stroke();
            
            // Y-axis
            ctx.beginPath();
            ctx.moveTo(0, bounds.top);
            ctx.lineTo(0, bounds.bottom);
            ctx.stroke();
            
            ctx.restore();
        }
        
        function drawTestContent() {
            const ctx = canvas.getContext();
            if (!ctx) return;
            
            // Clear canvas
            ctx.clearRect(-10000, -10000, 20000, 20000);
            
            // Apply transform
            canvas.applyTransform(ctx);
            
            // Draw grid first (if enabled)
            if (showGrid) {
                drawGrid(ctx);
            }
            
            // Draw origin marker
            ctx.fillStyle = 'red';
            ctx.fillRect(-5, -5, 10, 10);
            
            // Draw some shapes at different positions
            ctx.fillStyle = 'blue';
            ctx.fillRect(100, 100, 50, 50);
            
            ctx.fillStyle = 'green';
            ctx.beginPath();
            ctx.arc(200, 200, 30, 0, Math.PI * 2);
            ctx.fill();
            
            ctx.fillStyle = 'orange';
            ctx.fillRect(-150, -100, 40, 40);
            
            ctx.fillStyle = 'purple';
            ctx.beginPath();
            ctx.arc(-200, 150, 25, 0, Math.PI * 2);
            ctx.fill();
            
            // Draw text labels
            ctx.fillStyle = 'black';
            ctx.font = '16px Arial';
            ctx.fillText('Origin (0,0)', 10, -10);
            ctx.fillText('Blue Square', 100, 90);
            ctx.fillText('Green Circle', 170, 180);
            ctx.fillText('Orange Square', -150, -110);
            ctx.fillText('Purple Circle', -230, 140);
            
            // Draw coordinate labels on grid
            ctx.fillStyle = '#666';
            ctx.font = '12px Arial';
            
            // Label some grid intersections
            for (let x = -200; x <= 300; x += 100) {
                for (let y = -200; y <= 300; y += 100) {
                    if (x !== 0 || y !== 0) {
                        ctx.fillText(`(${x},${y})`, x + 5, y - 5);
                    }
                }
            }
        }
        
        function testPan() {
            if (canvas) {
                canvas.panToPoint(100, 100, true);
                updateStatus('Panned to (100, 100)');
            }
        }
        
        function testZoom() {
            if (canvas) {
                const center = canvas.getViewportCenter();
                canvas.zoomToPoint(center.x, center.y, 2, true);
                updateStatus('Zoomed to 2x');
            }
        }
        
        function testReset() {
            if (canvas) {
                canvas.resetViewport(true);
                updateStatus('Reset to origin');
            }
        }
        
        function showState() {
            if (canvas) {
                const state = canvas.getState();
                console.log('Canvas state:', state);
                updateStatus(`Pan: (${Math.round(state.panX)}, ${Math.round(state.panY)}), Zoom: ${state.zoom.toFixed(2)}x`);
            }
        }
        
        let showGrid = true;
        
        function toggleGrid() {
            showGrid = !showGrid;
            drawTestContent();
            updateStatus(showGrid ? 'Grid enabled' : 'Grid disabled');
        }
        
        function addMoreContent() {
            // This will be called from drawTestContent to add more shapes
            updateStatus('More content added - try zooming on different objects');
            drawTestContent();
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeTest);
        
        // Redraw on viewport changes
        if (window.Events) {
            window.Events.on('canvas:viewport-changed', () => {
                if (canvas) {
                    const ctx = canvas.getContext();
                    ctx.clearRect(-10000, -10000, 20000, 20000);
                    drawTestContent();
                }
            });
        }
    </script>
</body>
</html>