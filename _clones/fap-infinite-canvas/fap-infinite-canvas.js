/**
 * FAP Infinite Canvas - Large-scale spatial canvas system
 * 
 * Functional approach to infinite canvas management including:
 * - High-performance pan and zoom navigation
 * - Large coordinate space support (-1M to +1M)
 * - Google Maps and Logseq-style controls
 * - Touch gesture support for mobile devices
 * - Event system integration for cross-package communication
 */

// Create infinite canvas system using functional closure pattern
const createInfiniteCanvas = (container, options = {}) => {
    // Default configuration options
    const defaultOptions = {
        controlStyle: 'google-maps',    // 'google-maps' | 'logseq'
        minZoom: 0.1,                   // Minimum zoom level
        maxZoom: 10,                    // Maximum zoom level
        panBounds: null,                // Optional pan boundaries
        enableKeyboard: true,           // Enable keyboard controls
        enableTouch: true,              // Enable touch gestures
        smoothing: true,                // Enable smooth pan/zoom
        debugMode: false,               // Show debug information
        coordinateLimit: 1000000        // Coordinate space limit
    };

    // Internal state managed through closure
    let state = {
        // Canvas elements
        canvas: null,
        ctx: null,
        container: container,

        // Viewport state
        panX: 0,                        // Pan offset X (screen pixels)
        panY: 0,                        // Pan offset Y (screen pixels)
        zoom: 1,                        // Current zoom level
        width: 0,                       // Canvas width (screen pixels)
        height: 0,                      // Canvas height (screen pixels)

        // Interaction state
        isDragging: false,
        lastPointer: null,
        isTouch: false,
        touchStartDistance: 0,
        touchStartZoom: 1,

        // Configuration
        options: { ...defaultOptions, ...options },

        // Performance tracking
        lastFrameTime: 0,
        frameCount: 0,
        fps: 60,

        // Event listeners (for cleanup)
        eventListeners: []
    };

    // Initialize canvas with high-DPI support
    const initCanvas = () => {
        if (!state.container) {
            throw new Error('Canvas container is required');
        }

        // Create canvas element
        state.canvas = document.createElement('canvas');
        state.ctx = state.canvas.getContext('2d');

        if (!state.ctx) {
            throw new Error('Canvas 2D context not supported');
        }

        // Set up canvas styling
        state.canvas.style.display = 'block';
        state.canvas.style.touchAction = 'none'; // Prevent default touch behaviors
        state.canvas.style.userSelect = 'none';
        state.canvas.style.outline = 'none';
        state.canvas.tabIndex = 0; // Make focusable for keyboard events

        // Add to container
        state.container.appendChild(state.canvas);

        // Initial resize
        resizeCanvas();

        // Set up resize observer for automatic resizing
        if (window.ResizeObserver) {
            const resizeObserver = new ResizeObserver(() => {
                resizeCanvas();
            });
            resizeObserver.observe(state.container);

            // Store for cleanup
            state.resizeObserver = resizeObserver;
        } else {
            // Fallback to window resize event
            const handleResize = () => resizeCanvas();
            window.addEventListener('resize', handleResize);
            state.eventListeners.push({ element: window, event: 'resize', handler: handleResize });
        }

        console.log('🎨 Infinite Canvas initialized');
        return state.canvas;
    };

    // Handle canvas resizing with high-DPI support
    const resizeCanvas = () => {
        if (!state.canvas || !state.container) return;

        const rect = state.container.getBoundingClientRect();
        const dpr = window.devicePixelRatio || 1;

        // Update state dimensions
        state.width = rect.width;
        state.height = rect.height;

        // Set canvas size accounting for device pixel ratio
        state.canvas.width = rect.width * dpr;
        state.canvas.height = rect.height * dpr;

        // Set CSS size
        state.canvas.style.width = rect.width + 'px';
        state.canvas.style.height = rect.height + 'px';

        // Scale context for high-DPI
        state.ctx.scale(dpr, dpr);

        // Emit resize event
        emitEvent('canvas:resized', {
            width: state.width,
            height: state.height,
            devicePixelRatio: dpr
        });

        // Removed excessive resize logging
    };

    // Clean up all resources
    const destroy = () => {
        // Remove event listeners
        state.eventListeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });
        state.eventListeners = [];

        // Clean up resize observer
        if (state.resizeObserver) {
            state.resizeObserver.disconnect();
        }

        // Clean up performance monitoring
        stopPerformanceMonitoring();

        // Remove canvas from DOM
        if (state.canvas && state.canvas.parentNode) {
            state.canvas.parentNode.removeChild(state.canvas);
        }

        // Clear references
        state.canvas = null;
        state.ctx = null;
        state.container = null;

        console.log('🎨 Infinite Canvas destroyed');
    };

    // Get current canvas dimensions
    const getDimensions = () => ({
        width: state.width,
        height: state.height,
        devicePixelRatio: window.devicePixelRatio || 1
    });

    // Get canvas element (for external rendering)
    const getCanvas = () => state.canvas;

    // Get canvas context (for external rendering)
    const getContext = () => state.ctx;

    // Coordinate transformation functions

    // Convert screen coordinates to world coordinates
    const screenToWorld = (screenX, screenY) => {
        const worldX = (screenX - state.panX) / state.zoom;
        const worldY = (screenY - state.panY) / state.zoom;

        // Clamp to coordinate limits to prevent precision issues
        const limit = state.options.coordinateLimit;
        return {
            x: Math.max(-limit, Math.min(limit, worldX)),
            y: Math.max(-limit, Math.min(limit, worldY))
        };
    };

    // Convert world coordinates to screen coordinates
    const worldToScreen = (worldX, worldY) => {
        return {
            x: worldX * state.zoom + state.panX,
            y: worldY * state.zoom + state.panY
        };
    };

    // Apply viewport transformation to canvas context
    const applyTransform = (ctx = state.ctx) => {
        if (!ctx) return;

        // Reset transform first
        ctx.setTransform(1, 0, 0, 1, 0, 0);

        // Apply viewport transformation
        ctx.translate(state.panX, state.panY);
        ctx.scale(state.zoom, state.zoom);
    };

    // Get transformation matrix for external use
    const getTransformMatrix = () => ({
        a: state.zoom,      // Horizontal scaling
        b: 0,               // Horizontal skewing
        c: 0,               // Vertical skewing
        d: state.zoom,      // Vertical scaling
        e: state.panX,      // Horizontal translation
        f: state.panY       // Vertical translation
    });

    // Transform a rectangle from world to screen coordinates
    const transformRect = (worldRect) => {
        const topLeft = worldToScreen(worldRect.x, worldRect.y);
        const bottomRight = worldToScreen(
            worldRect.x + worldRect.width,
            worldRect.y + worldRect.height
        );

        return {
            x: topLeft.x,
            y: topLeft.y,
            width: bottomRight.x - topLeft.x,
            height: bottomRight.y - topLeft.y
        };
    };

    // Check if a world rectangle is visible in current viewport
    const isRectVisible = (worldRect) => {
        const visibleBounds = getVisibleBounds();

        return !(
            worldRect.x + worldRect.width < visibleBounds.left ||
            worldRect.x > visibleBounds.right ||
            worldRect.y + worldRect.height < visibleBounds.top ||
            worldRect.y > visibleBounds.bottom
        );
    };

    // Get visible world bounds for viewport culling
    const getVisibleBounds = () => {
        const left = -state.panX / state.zoom;
        const top = -state.panY / state.zoom;
        const right = (-state.panX + state.width) / state.zoom;
        const bottom = (-state.panY + state.height) / state.zoom;

        return { left, top, right, bottom };
    };

    // Viewport state management functions

    // Update viewport with bounds checking
    const updateViewport = (newPanX, newPanY, newZoom, emitEvents = true) => {
        const oldState = {
            panX: state.panX,
            panY: state.panY,
            zoom: state.zoom
        };

        // Clamp zoom to limits
        newZoom = Math.max(state.options.minZoom, Math.min(state.options.maxZoom, newZoom));

        // Apply pan bounds if configured
        if (state.options.panBounds) {
            const bounds = state.options.panBounds;
            const halfWidth = state.width / 2;
            const halfHeight = state.height / 2;

            newPanX = Math.max(bounds.left - halfWidth, Math.min(bounds.right + halfWidth, newPanX));
            newPanY = Math.max(bounds.top - halfHeight, Math.min(bounds.bottom + halfHeight, newPanY));
        }

        // Update state
        state.panX = newPanX;
        state.panY = newPanY;
        state.zoom = newZoom;

        // Check if anything changed
        const hasChanged = (
            oldState.panX !== state.panX ||
            oldState.panY !== state.panY ||
            oldState.zoom !== state.zoom
        );

        // Emit events if requested and something changed
        if (emitEvents && hasChanged) {
            emitEvent('canvas:viewport-changed', {
                panX: state.panX,
                panY: state.panY,
                zoom: state.zoom,
                visibleBounds: getVisibleBounds(),
                oldState
            });

            // Emit specific zoom event if zoom changed
            if (oldState.zoom !== state.zoom) {
                emitEvent('canvas:zoom-changed', {
                    oldZoom: oldState.zoom,
                    newZoom: state.zoom,
                    zoomCenter: { x: state.width / 2, y: state.height / 2 }
                });
            }

            // Emit specific pan event if pan changed
            if (oldState.panX !== state.panX || oldState.panY !== state.panY) {
                emitEvent('canvas:pan-changed', {
                    deltaX: state.panX - oldState.panX,
                    deltaY: state.panY - oldState.panY,
                    newPan: { x: state.panX, y: state.panY }
                });
            }
        }

        return hasChanged;
    };

    // Pan to specific world coordinates
    const panToPoint = (worldX, worldY, animated = false) => {
        const screenX = state.width / 2;
        const screenY = state.height / 2;

        const newPanX = screenX - worldX * state.zoom;
        const newPanY = screenY - worldY * state.zoom;

        if (animated && state.options.smoothing) {
            animateTo(newPanX, newPanY, state.zoom);
        } else {
            updateViewport(newPanX, newPanY, state.zoom);
        }
    };

    // Zoom to specific level at point
    const zoomToPoint = (worldX, worldY, newZoom, animated = false) => {
        // We want to keep the world point at the same screen position
        // Current screen position: screenX = worldX * oldZoom + oldPanX
        // After zoom: screenX = worldX * newZoom + newPanX
        // So: worldX * oldZoom + oldPanX = worldX * newZoom + newPanX
        // Therefore: newPanX = worldX * oldZoom + oldPanX - worldX * newZoom
        // Simplifying: newPanX = worldX * (oldZoom - newZoom) + oldPanX
        
        const newPanX = worldX * (state.zoom - newZoom) + state.panX;
        const newPanY = worldY * (state.zoom - newZoom) + state.panY;

        if (animated && state.options.smoothing) {
            animateTo(newPanX, newPanY, newZoom);
        } else {
            updateViewport(newPanX, newPanY, newZoom);
        }
    };

    // Fit content to viewport
    const fitToContent = (contentBounds, padding = 50) => {
        const availableWidth = state.width - padding * 2;
        const availableHeight = state.height - padding * 2;

        const contentWidth = contentBounds.right - contentBounds.left;
        const contentHeight = contentBounds.bottom - contentBounds.top;

        // Calculate zoom to fit content
        const zoomX = availableWidth / contentWidth;
        const zoomY = availableHeight / contentHeight;
        const newZoom = Math.min(zoomX, zoomY, state.options.maxZoom);

        // Center content
        const centerX = (contentBounds.left + contentBounds.right) / 2;
        const centerY = (contentBounds.top + contentBounds.bottom) / 2;

        panToPoint(centerX, centerY);
        updateViewport(state.panX, state.panY, newZoom);
    };

    // Reset viewport to default state
    const resetViewport = (animated = false) => {
        if (animated && state.options.smoothing) {
            animateTo(0, 0, 1);
        } else {
            updateViewport(0, 0, 1);
        }
    };

    // Animation system for smooth transitions
    let animationId = null;

    const animateTo = (targetPanX, targetPanY, targetZoom, duration = 300) => {
        if (animationId) {
            cancelAnimationFrame(animationId);
        }

        const startTime = performance.now();
        const startPanX = state.panX;
        const startPanY = state.panY;
        const startZoom = state.zoom;

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Easing function (ease-out)
            const eased = 1 - Math.pow(1 - progress, 3);

            const currentPanX = startPanX + (targetPanX - startPanX) * eased;
            const currentPanY = startPanY + (targetPanY - startPanY) * eased;
            const currentZoom = startZoom + (targetZoom - startZoom) * eased;

            updateViewport(currentPanX, currentPanY, currentZoom, progress === 1);

            if (progress < 1) {
                animationId = requestAnimationFrame(animate);
            } else {
                animationId = null;
            }
        };

        animationId = requestAnimationFrame(animate);
    };

    // Get viewport center in world coordinates
    const getViewportCenter = () => {
        return screenToWorld(state.width / 2, state.height / 2);
    };

    // Check if viewport is at zoom limits
    const isAtZoomLimit = () => ({
        atMin: state.zoom <= state.options.minZoom,
        atMax: state.zoom >= state.options.maxZoom
    });

    // Control style management

    // Switch control style without restart
    const setControlStyle = (newStyle) => {
        const validStyles = ['google-maps', 'logseq'];

        if (!validStyles.includes(newStyle)) {
            console.warn(`Invalid control style: ${newStyle}. Using 'google-maps' as default.`);
            newStyle = 'google-maps';
        }

        const oldStyle = state.options.controlStyle;
        state.options.controlStyle = newStyle;

        emitEvent('canvas:control-style-changed', {
            oldStyle,
            newStyle,
            availableStyles: validStyles
        });

        if (state.options.debugMode) {
            console.log(`🎨 Control style changed: ${oldStyle} → ${newStyle}`);
        }

        return newStyle;
    };

    // Get current control style
    const getControlStyle = () => state.options.controlStyle;

    // Get available control styles
    const getAvailableControlStyles = () => ['google-maps', 'logseq'];

    // Get control style descriptions
    const getControlStyleInfo = () => ({
        'google-maps': {
            name: 'Google Maps Style',
            description: 'Left click + drag to pan, scroll wheel to zoom at cursor',
            panButton: 'Left mouse button',
            zoomBehavior: 'Zoom at cursor position',
            contextMenu: 'Right click for context menu'
        },
        'logseq': {
            name: 'Logseq Style',
            description: 'Middle click + drag to pan, scroll wheel to zoom at center',
            panButton: 'Middle mouse button or Shift + left click',
            zoomBehavior: 'Zoom at viewport center',
            contextMenu: 'Right click available for other actions'
        }
    });

    // Batch transform multiple points for efficiency
    const batchScreenToWorld = (screenPoints) => {
        return screenPoints.map(point => screenToWorld(point.x, point.y));
    };

    const batchWorldToScreen = (worldPoints) => {
        return worldPoints.map(point => worldToScreen(point.x, point.y));
    };

    // Get current state (for debugging)
    const getState = () => ({
        panX: state.panX,
        panY: state.panY,
        zoom: state.zoom,
        width: state.width,
        height: state.height,
        isDragging: state.isDragging,
        options: { ...state.options },
        fps: state.fps,
        visibleBounds: getVisibleBounds(),
        transformMatrix: getTransformMatrix()
    });

    // Event system integration

    // Emit events through global event system
    const emitEvent = (eventType, data) => {
        if (window.Events) {
            window.Events.emit(eventType, {
                canvasId: state.canvas?.id || 'default',
                timestamp: Date.now(),
                ...data
            });
        }

        // Only log important events in debug mode, not mouse moves
        if (state.options.debugMode && !eventType.includes('pointer-move')) {
            console.log(`🎨 Event: ${eventType}`, data);
        }
    };

    // Listen for external events
    const setupEventListeners = () => {
        if (!window.Events) return;

        // Listen for focus requests
        window.Events.on('canvas:focus-point', (data) => {
            if (data.canvasId && data.canvasId !== (state.canvas?.id || 'default')) return;

            panToPoint(data.worldX, data.worldY, data.animated !== false);
        });

        // Listen for zoom requests
        window.Events.on('canvas:zoom-to-point', (data) => {
            if (data.canvasId && data.canvasId !== (state.canvas?.id || 'default')) return;

            zoomToPoint(data.worldX, data.worldY, data.zoom, data.animated !== false);
        });

        // Listen for fit content requests
        window.Events.on('canvas:fit-content', (data) => {
            if (data.canvasId && data.canvasId !== (state.canvas?.id || 'default')) return;

            fitToContent(data.bounds, data.padding);
        });

        // Listen for viewport reset requests
        window.Events.on('canvas:reset-viewport', (data) => {
            if (data.canvasId && data.canvasId !== (state.canvas?.id || 'default')) return;

            resetViewport(data.animated !== false);
        });

        // Listen for control style changes
        window.Events.on('canvas:set-control-style', (data) => {
            if (data.canvasId && data.canvasId !== (state.canvas?.id || 'default')) return;

            setControlStyle(data.style);
        });

        // External event listeners set up
    };

    // Get all available events that this canvas emits
    const getEmittedEvents = () => [
        'canvas:initialized',
        'canvas:resized',
        'canvas:viewport-changed',
        'canvas:zoom-changed',
        'canvas:pan-changed',
        'canvas:pan-start',
        'canvas:pan-move',
        'canvas:pan-end',
        'canvas:pan-cancel',
        'canvas:pointer-down',
        'canvas:pointer-move',
        'canvas:pointer-up',
        'canvas:wheel',
        'canvas:touch-start',
        'canvas:touch-move',
        'canvas:touch-end',
        'canvas:touch-cancel',
        'canvas:pinch-start',
        'canvas:pinch-move',
        'canvas:pinch-end',
        'canvas:pinch-to-pan',
        'canvas:control-style-changed',
        'canvas:error'
    ];

    // Get all events that this canvas listens for
    const getListenedEvents = () => [
        'canvas:focus-point',
        'canvas:zoom-to-point',
        'canvas:fit-content',
        'canvas:reset-viewport',
        'canvas:set-control-style'
    ];

    // Performance optimization system

    // Performance monitoring
    const updatePerformanceMetrics = () => {
        const now = performance.now();

        if (state.lastFrameTime > 0) {
            const frameDelta = now - state.lastFrameTime;
            state.fps = Math.round(1000 / frameDelta);

            // Emit performance warning if FPS drops significantly
            if (state.fps < 30 && state.options.debugMode) {
                console.warn(`🎨 Performance warning: FPS dropped to ${state.fps}`);
                emitEvent('canvas:performance-warning', {
                    fps: state.fps,
                    frameDelta,
                    timestamp: now
                });
            }
        }

        state.lastFrameTime = now;
        state.frameCount++;
    };

    // Throttled update function for smooth performance
    let updateScheduled = false;

    const scheduleUpdate = (callback) => {
        if (updateScheduled) return;

        updateScheduled = true;
        requestAnimationFrame(() => {
            updatePerformanceMetrics();
            if (callback) callback();
            updateScheduled = false;
        });
    };

    // Viewport culling utilities
    const getCullingBounds = (padding = 100) => {
        const bounds = getVisibleBounds();
        return {
            left: bounds.left - padding,
            top: bounds.top - padding,
            right: bounds.right + padding,
            bottom: bounds.bottom + padding
        };
    };

    const isPointInCullingBounds = (worldX, worldY, padding = 100) => {
        const bounds = getCullingBounds(padding);
        return (
            worldX >= bounds.left &&
            worldX <= bounds.right &&
            worldY >= bounds.top &&
            worldY <= bounds.bottom
        );
    };

    const cullRectangles = (rectangles, padding = 100) => {
        const bounds = getCullingBounds(padding);

        return rectangles.filter(rect => {
            return !(
                rect.x + rect.width < bounds.left ||
                rect.x > bounds.right ||
                rect.y + rect.height < bounds.top ||
                rect.y > bounds.bottom
            );
        });
    };

    const cullPoints = (points, padding = 100) => {
        const bounds = getCullingBounds(padding);

        return points.filter(point => {
            return (
                point.x >= bounds.left &&
                point.x <= bounds.right &&
                point.y >= bounds.top &&
                point.y <= bounds.bottom
            );
        });
    };

    // Performance debugging tools
    const getPerformanceStats = () => ({
        fps: state.fps,
        frameCount: state.frameCount,
        lastFrameTime: state.lastFrameTime,
        memoryUsage: performance.memory ? {
            used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
            total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
            limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
        } : null,
        viewport: {
            zoom: state.zoom,
            visibleArea: {
                width: state.width / state.zoom,
                height: state.height / state.zoom
            }
        }
    });

    const startPerformanceMonitoring = (interval = 1000) => {
        if (state.performanceInterval) {
            clearInterval(state.performanceInterval);
        }

        state.performanceInterval = setInterval(() => {
            const stats = getPerformanceStats();

            emitEvent('canvas:performance-stats', stats);

            if (state.options.debugMode) {
                console.log('🎨 Performance stats:', stats);
            }
        }, interval);

        return state.performanceInterval;
    };

    const stopPerformanceMonitoring = () => {
        if (state.performanceInterval) {
            clearInterval(state.performanceInterval);
            state.performanceInterval = null;
        }
    };

    // Memory management
    const clearCaches = () => {
        // Clear any cached data that might be consuming memory
        if (state.options.debugMode) {
            console.log('🎨 Caches cleared');
        }

        emitEvent('canvas:caches-cleared', {
            timestamp: Date.now()
        });
    };

    // Optimize rendering for large coordinate spaces
    const optimizeForLargeSpace = () => {
        // Enable viewport culling by default for large spaces
        const bounds = getVisibleBounds();
        const visibleArea = (bounds.right - bounds.left) * (bounds.bottom - bounds.top);

        if (visibleArea > 1000000) { // Large visible area
            if (state.options.debugMode) {
                console.log('🎨 Large space detected, enabling optimizations');
            }

            emitEvent('canvas:large-space-optimization', {
                visibleArea,
                bounds,
                recommendations: [
                    'Enable viewport culling',
                    'Use level-of-detail rendering',
                    'Implement content streaming'
                ]
            });
        }
    };

    // State persistence system

    // Serialize viewport state for persistence
    const serializeState = () => ({
        panX: state.panX,
        panY: state.panY,
        zoom: state.zoom,
        controlStyle: state.options.controlStyle,
        timestamp: Date.now(),
        version: '1.0'
    });

    // Restore viewport state from serialized data
    const restoreState = (serializedState, animated = false) => {
        try {
            // Validate serialized state
            if (!serializedState || typeof serializedState !== 'object') {
                throw new Error('Invalid serialized state');
            }

            const { panX, panY, zoom, controlStyle, version } = serializedState;

            // Check version compatibility
            if (version && version !== '1.0') {
                console.warn(`🎨 State version mismatch: expected 1.0, got ${version}`);
            }

            // Validate numeric values
            if (typeof panX !== 'number' || typeof panY !== 'number' || typeof zoom !== 'number') {
                throw new Error('Invalid numeric values in state');
            }

            // Restore viewport
            if (animated && state.options.smoothing) {
                animateTo(panX, panY, zoom);
            } else {
                updateViewport(panX, panY, zoom);
            }

            // Restore control style if provided
            if (controlStyle) {
                setControlStyle(controlStyle);
            }

            emitEvent('canvas:state-restored', {
                restoredState: serializedState,
                currentState: serializeState()
            });

            if (state.options.debugMode) {
                console.log('🎨 State restored successfully', serializedState);
            }

            return true;
        } catch (error) {
            console.warn('🎨 State restoration failed:', error.message);

            // Fall back to default state
            resetViewport(animated);

            emitEvent('canvas:state-restore-failed', {
                error: error.message,
                fallbackApplied: true
            });

            return false;
        }
    };

    // Auto-save state on changes
    let autoSaveTimeout = null;

    const enableAutoSave = (key = 'fap-infinite-canvas-state', delay = 500) => {
        // Set up auto-save on viewport changes
        const handleViewportChange = () => {
            if (autoSaveTimeout) {
                clearTimeout(autoSaveTimeout);
            }

            autoSaveTimeout = setTimeout(() => {
                try {
                    const serializedState = serializeState();
                    localStorage.setItem(key, JSON.stringify(serializedState));

                    if (state.options.debugMode) {
                        console.log('🎨 State auto-saved', serializedState);
                    }

                    emitEvent('canvas:state-auto-saved', {
                        key,
                        state: serializedState
                    });
                } catch (error) {
                    console.warn('🎨 Auto-save failed:', error.message);
                }
            }, delay);
        };

        if (window.Events) {
            window.Events.on('canvas:viewport-changed', handleViewportChange);
        }

        state.autoSaveKey = key;
        state.autoSaveEnabled = true;

        if (state.options.debugMode) {
            console.log(`🎨 Auto-save enabled with key: ${key}`);
        }

        return key;
    };

    const disableAutoSave = () => {
        if (autoSaveTimeout) {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = null;
        }

        state.autoSaveEnabled = false;
        state.autoSaveKey = null;

        if (state.options.debugMode) {
            console.log('🎨 Auto-save disabled');
        }
    };

    // Load state from localStorage
    const loadState = (key = 'fap-infinite-canvas-state', animated = false) => {
        try {
            const saved = localStorage.getItem(key);
            if (!saved) {
                if (state.options.debugMode) {
                    console.log('🎨 No saved state found');
                }
                return false;
            }

            const serializedState = JSON.parse(saved);
            return restoreState(serializedState, animated);
        } catch (error) {
            console.warn('🎨 Failed to load state:', error.message);
            return false;
        }
    };

    // Save state to localStorage
    const saveState = (key = 'fap-infinite-canvas-state') => {
        try {
            const serializedState = serializeState();
            localStorage.setItem(key, JSON.stringify(serializedState));

            emitEvent('canvas:state-saved', {
                key,
                state: serializedState
            });

            if (state.options.debugMode) {
                console.log('🎨 State saved manually', serializedState);
            }

            return true;
        } catch (error) {
            console.warn('🎨 Failed to save state:', error.message);

            emitEvent('canvas:state-save-failed', {
                error: error.message,
                key
            });

            return false;
        }
    };

    // Clear saved state
    const clearSavedState = (key = 'fap-infinite-canvas-state') => {
        try {
            localStorage.removeItem(key);

            emitEvent('canvas:state-cleared', { key });

            if (state.options.debugMode) {
                console.log(`🎨 Saved state cleared: ${key}`);
            }

            return true;
        } catch (error) {
            console.warn('🎨 Failed to clear saved state:', error.message);
            return false;
        }
    };

    // Mouse interaction handling

    const setupMouseEvents = () => {
        if (!state.canvas) return;

        // Mouse event handlers
        const handleMouseDown = (e) => {
            e.preventDefault();
            state.canvas.focus();

            const rect = state.canvas.getBoundingClientRect();
            const screenX = e.clientX - rect.left;
            const screenY = e.clientY - rect.top;
            const worldPos = screenToWorld(screenX, screenY);

            // Determine if this should start panning based on control style
            const shouldPan = (
                (state.options.controlStyle === 'google-maps' && e.button === 0) || // Left click
                (state.options.controlStyle === 'logseq' && e.button === 1) ||      // Middle click
                (e.button === 0 && e.shiftKey) // Shift + left click (universal pan)
            );

            if (shouldPan) {
                state.isDragging = true;
                state.lastPointer = { x: screenX, y: screenY };
                state.canvas.style.cursor = 'grabbing';

                emitEvent('canvas:pan-start', {
                    screen: { x: screenX, y: screenY },
                    world: worldPos,
                    button: e.button
                });
            } else {
                // Emit interaction event for other systems
                emitEvent('canvas:pointer-down', {
                    screen: { x: screenX, y: screenY },
                    world: worldPos,
                    button: e.button,
                    shiftKey: e.shiftKey,
                    ctrlKey: e.ctrlKey,
                    altKey: e.altKey
                });
            }
        };

        const handleMouseMove = (e) => {
            const rect = state.canvas.getBoundingClientRect();
            const screenX = e.clientX - rect.left;
            const screenY = e.clientY - rect.top;
            const worldPos = screenToWorld(screenX, screenY);

            if (state.isDragging && state.lastPointer) {
                // Calculate pan delta
                const deltaX = screenX - state.lastPointer.x;
                const deltaY = screenY - state.lastPointer.y;

                // Update viewport
                updateViewport(
                    state.panX + deltaX,
                    state.panY + deltaY,
                    state.zoom
                );

                // Update last pointer position
                state.lastPointer = { x: screenX, y: screenY };

                emitEvent('canvas:pan-move', {
                    screen: { x: screenX, y: screenY },
                    world: worldPos,
                    delta: { x: deltaX, y: deltaY }
                });
            } else {
                // Emit hover event
                emitEvent('canvas:pointer-move', {
                    screen: { x: screenX, y: screenY },
                    world: worldPos,
                    isDragging: state.isDragging
                });
            }
        };

        const handleMouseUp = (e) => {
            const rect = state.canvas.getBoundingClientRect();
            const screenX = e.clientX - rect.left;
            const screenY = e.clientY - rect.top;
            const worldPos = screenToWorld(screenX, screenY);

            if (state.isDragging) {
                state.isDragging = false;
                state.lastPointer = null;
                state.canvas.style.cursor = 'grab';

                emitEvent('canvas:pan-end', {
                    screen: { x: screenX, y: screenY },
                    world: worldPos,
                    button: e.button
                });
            } else {
                emitEvent('canvas:pointer-up', {
                    screen: { x: screenX, y: screenY },
                    world: worldPos,
                    button: e.button,
                    shiftKey: e.shiftKey,
                    ctrlKey: e.ctrlKey,
                    altKey: e.altKey
                });
            }
        };

        const handleWheel = (e) => {
            e.preventDefault();

            const rect = state.canvas.getBoundingClientRect();
            const screenX = e.clientX - rect.left;
            const screenY = e.clientY - rect.top;
            const worldPos = screenToWorld(screenX, screenY);

            // Calculate zoom delta
            const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
            const newZoom = state.zoom * zoomFactor;

            // Zoom based on control style
            if (state.options.controlStyle === 'google-maps') {
                // Zoom at cursor position
                zoomToPoint(worldPos.x, worldPos.y, newZoom);
            } else {
                // Zoom at center (Logseq style)
                const center = getViewportCenter();
                zoomToPoint(center.x, center.y, newZoom);
            }

            emitEvent('canvas:wheel', {
                screen: { x: screenX, y: screenY },
                world: worldPos,
                delta: e.deltaY,
                zoomFactor,
                newZoom: state.zoom
            });
        };

        const handleContextMenu = (e) => {
            // Prevent context menu during panning
            if (state.isDragging) {
                e.preventDefault();
            }
        };

        const handleMouseLeave = () => {
            // Stop panning if mouse leaves canvas
            if (state.isDragging) {
                state.isDragging = false;
                state.lastPointer = null;
                state.canvas.style.cursor = 'grab';

                emitEvent('canvas:pan-cancel', {
                    reason: 'mouse-leave'
                });
            }
        };

        // Add event listeners
        const events = [
            { event: 'mousedown', handler: handleMouseDown },
            { event: 'mousemove', handler: handleMouseMove },
            { event: 'mouseup', handler: handleMouseUp },
            { event: 'wheel', handler: handleWheel },
            { event: 'contextmenu', handler: handleContextMenu },
            { event: 'mouseleave', handler: handleMouseLeave }
        ];

        events.forEach(({ event, handler }) => {
            state.canvas.addEventListener(event, handler);
            state.eventListeners.push({ element: state.canvas, event, handler });
        });

        // Set initial cursor
        state.canvas.style.cursor = 'grab';

        // Mouse events initialized
    };

    // Touch gesture handling

    const setupTouchEvents = () => {
        if (!state.canvas || !state.options.enableTouch) return;

        const getTouchCenter = (touches) => {
            if (touches.length === 1) {
                const rect = state.canvas.getBoundingClientRect();
                return {
                    x: touches[0].clientX - rect.left,
                    y: touches[0].clientY - rect.top
                };
            } else if (touches.length === 2) {
                const rect = state.canvas.getBoundingClientRect();
                return {
                    x: (touches[0].clientX + touches[1].clientX) / 2 - rect.left,
                    y: (touches[0].clientY + touches[1].clientY) / 2 - rect.top
                };
            }
            return { x: 0, y: 0 };
        };

        const getTouchDistance = (touches) => {
            if (touches.length < 2) return 0;

            const dx = touches[0].clientX - touches[1].clientX;
            const dy = touches[0].clientY - touches[1].clientY;
            return Math.sqrt(dx * dx + dy * dy);
        };

        const handleTouchStart = (e) => {
            e.preventDefault();

            const touches = Array.from(e.touches);
            const center = getTouchCenter(touches);
            const worldPos = screenToWorld(center.x, center.y);

            state.isTouch = true;

            if (touches.length === 1) {
                // Single finger - start panning
                state.isDragging = true;
                state.lastPointer = center;

                emitEvent('canvas:touch-start', {
                    screen: center,
                    world: worldPos,
                    touchCount: 1
                });
            } else if (touches.length === 2) {
                // Two fingers - start pinch zoom
                state.isDragging = false;
                state.touchStartDistance = getTouchDistance(touches);
                state.touchStartZoom = state.zoom;
                state.lastPointer = center;

                emitEvent('canvas:pinch-start', {
                    screen: center,
                    world: worldPos,
                    distance: state.touchStartDistance,
                    zoom: state.zoom
                });
            }
        };

        const handleTouchMove = (e) => {
            e.preventDefault();

            const touches = Array.from(e.touches);
            const center = getTouchCenter(touches);
            const worldPos = screenToWorld(center.x, center.y);

            if (touches.length === 1 && state.isDragging && state.lastPointer) {
                // Single finger panning
                const deltaX = center.x - state.lastPointer.x;
                const deltaY = center.y - state.lastPointer.y;

                updateViewport(
                    state.panX + deltaX,
                    state.panY + deltaY,
                    state.zoom
                );

                state.lastPointer = center;

                emitEvent('canvas:touch-move', {
                    screen: center,
                    world: worldPos,
                    delta: { x: deltaX, y: deltaY }
                });
            } else if (touches.length === 2 && state.touchStartDistance > 0) {
                // Two finger pinch zoom
                const currentDistance = getTouchDistance(touches);
                const zoomFactor = currentDistance / state.touchStartDistance;
                const newZoom = state.touchStartZoom * zoomFactor;

                // Zoom at pinch center
                const pinchCenter = screenToWorld(center.x, center.y);
                zoomToPoint(pinchCenter.x, pinchCenter.y, newZoom);

                // Also handle panning during pinch
                if (state.lastPointer) {
                    const deltaX = center.x - state.lastPointer.x;
                    const deltaY = center.y - state.lastPointer.y;

                    updateViewport(
                        state.panX + deltaX,
                        state.panY + deltaY,
                        state.zoom
                    );
                }

                state.lastPointer = center;

                emitEvent('canvas:pinch-move', {
                    screen: center,
                    world: worldPos,
                    distance: currentDistance,
                    zoomFactor,
                    newZoom: state.zoom
                });
            }
        };

        const handleTouchEnd = (e) => {
            e.preventDefault();

            const touches = Array.from(e.touches);
            const center = getTouchCenter(touches);
            const worldPos = screenToWorld(center.x, center.y);

            if (touches.length === 0) {
                // All fingers lifted
                if (state.isDragging) {
                    emitEvent('canvas:touch-end', {
                        screen: center,
                        world: worldPos
                    });
                } else if (state.touchStartDistance > 0) {
                    emitEvent('canvas:pinch-end', {
                        screen: center,
                        world: worldPos,
                        finalZoom: state.zoom
                    });
                }

                state.isDragging = false;
                state.isTouch = false;
                state.lastPointer = null;
                state.touchStartDistance = 0;
                state.touchStartZoom = 1;
            } else if (touches.length === 1 && state.touchStartDistance > 0) {
                // Went from pinch to single finger
                state.touchStartDistance = 0;
                state.touchStartZoom = 1;
                state.isDragging = true;
                state.lastPointer = center;

                emitEvent('canvas:pinch-to-pan', {
                    screen: center,
                    world: worldPos
                });
            }
        };

        const handleTouchCancel = (e) => {
            e.preventDefault();

            // Reset all touch state
            state.isDragging = false;
            state.isTouch = false;
            state.lastPointer = null;
            state.touchStartDistance = 0;
            state.touchStartZoom = 1;

            emitEvent('canvas:touch-cancel', {
                reason: 'system-cancel'
            });
        };

        // Add touch event listeners
        const touchEvents = [
            { event: 'touchstart', handler: handleTouchStart },
            { event: 'touchmove', handler: handleTouchMove },
            { event: 'touchend', handler: handleTouchEnd },
            { event: 'touchcancel', handler: handleTouchCancel }
        ];

        touchEvents.forEach(({ event, handler }) => {
            state.canvas.addEventListener(event, handler, { passive: false });
            state.eventListeners.push({ element: state.canvas, event, handler });
        });

        // Touch events initialized
    };

    // Keyboard navigation support

    const setupKeyboardEvents = () => {
        if (!state.canvas || !state.options.enableKeyboard) return;

        const handleKeyDown = (e) => {
            const panStep = 50; // Pixels to pan per key press
            const zoomStep = 0.1; // Zoom increment per key press
            let handled = false;

            // Pan with arrow keys
            if (e.key === 'ArrowLeft') {
                updateViewport(state.panX + panStep, state.panY, state.zoom);
                handled = true;
            } else if (e.key === 'ArrowRight') {
                updateViewport(state.panX - panStep, state.panY, state.zoom);
                handled = true;
            } else if (e.key === 'ArrowUp') {
                updateViewport(state.panX, state.panY + panStep, state.zoom);
                handled = true;
            } else if (e.key === 'ArrowDown') {
                updateViewport(state.panX, state.panY - panStep, state.zoom);
                handled = true;
            }

            // Zoom with + and - keys
            else if (e.key === '+' || e.key === '=') {
                const center = getViewportCenter();
                zoomToPoint(center.x, center.y, state.zoom + zoomStep);
                handled = true;
            } else if (e.key === '-') {
                const center = getViewportCenter();
                zoomToPoint(center.x, center.y, state.zoom - zoomStep);
                handled = true;
            }

            // Reset viewport with Home key
            else if (e.key === 'Home') {
                resetViewport(true);
                handled = true;
            }

            // Fit content with F key
            else if (e.key === 'f' || e.key === 'F') {
                // Emit event for external systems to provide content bounds
                emitEvent('canvas:fit-content-requested', {
                    timestamp: Date.now()
                });
                handled = true;
            }

            // Toggle control style with C key
            else if (e.key === 'c' || e.key === 'C') {
                const currentStyle = getControlStyle();
                const newStyle = currentStyle === 'google-maps' ? 'logseq' : 'google-maps';
                setControlStyle(newStyle);
                handled = true;
            }

            // Debug info with D key
            else if (e.key === 'd' || e.key === 'D') {
                console.log('🎨 Canvas Debug Info:', getState());
                console.log('🎨 Performance Stats:', getPerformanceStats());
                handled = true;
            }

            if (handled) {
                e.preventDefault();

                emitEvent('canvas:keyboard-action', {
                    key: e.key,
                    action: getKeyAction(e.key),
                    viewport: {
                        panX: state.panX,
                        panY: state.panY,
                        zoom: state.zoom
                    }
                });
            }
        };

        const handleKeyUp = (e) => {
            // Handle any key up events if needed
            emitEvent('canvas:keyboard-up', {
                key: e.key,
                timestamp: Date.now()
            });
        };

        // Space key handling for alternative pan mode
        let spacePressed = false;
        let originalCursor = '';

        const handleSpaceDown = (e) => {
            if (e.key === ' ' && !spacePressed) {
                spacePressed = true;
                originalCursor = state.canvas.style.cursor;
                state.canvas.style.cursor = 'grab';

                emitEvent('canvas:space-pan-mode', {
                    enabled: true
                });
            }
        };

        const handleSpaceUp = (e) => {
            if (e.key === ' ' && spacePressed) {
                spacePressed = false;
                state.canvas.style.cursor = originalCursor;

                emitEvent('canvas:space-pan-mode', {
                    enabled: false
                });
            }
        };

        // Modify mouse handling to support space+drag panning
        const originalMouseDown = state.canvas.onmousedown;
        const enhancedMouseDown = (e) => {
            if (spacePressed && e.button === 0) {
                // Space + left click = pan
                state.isDragging = true;
                const rect = state.canvas.getBoundingClientRect();
                state.lastPointer = {
                    x: e.clientX - rect.left,
                    y: e.clientY - rect.top
                };
                state.canvas.style.cursor = 'grabbing';
                e.preventDefault();
                return;
            }

            // Call original handler
            if (originalMouseDown) originalMouseDown(e);
        };

        // Get action description for key
        const getKeyAction = (key) => {
            const actions = {
                'ArrowLeft': 'Pan left',
                'ArrowRight': 'Pan right',
                'ArrowUp': 'Pan up',
                'ArrowDown': 'Pan down',
                '+': 'Zoom in',
                '=': 'Zoom in',
                '-': 'Zoom out',
                'Home': 'Reset viewport',
                'f': 'Fit content',
                'F': 'Fit content',
                'c': 'Toggle control style',
                'C': 'Toggle control style',
                'd': 'Show debug info',
                'D': 'Show debug info',
                ' ': 'Space pan mode'
            };

            return actions[key] || 'Unknown action';
        };

        // Add keyboard event listeners
        const keyboardEvents = [
            { event: 'keydown', handler: handleKeyDown },
            { event: 'keyup', handler: handleKeyUp }
        ];

        keyboardEvents.forEach(({ event, handler }) => {
            state.canvas.addEventListener(event, handler);
            state.eventListeners.push({ element: state.canvas, event, handler });
        });

        // Add space key handling to document (since canvas might not have focus)
        document.addEventListener('keydown', handleSpaceDown);
        document.addEventListener('keyup', handleSpaceUp);
        state.eventListeners.push({ element: document, event: 'keydown', handler: handleSpaceDown });
        state.eventListeners.push({ element: document, event: 'keyup', handler: handleSpaceUp });

        // Keyboard navigation initialized
    };

    // Initialize the canvas system
    const init = () => {
        try {
            initCanvas();
            setupMouseEvents();
            setupTouchEvents();
            setupKeyboardEvents();
            setupEventListeners();

            // Production optimizations
            if (!state.options.debugMode) {
                // Disable console logging in production
                const originalLog = console.log;
                console.log = (...args) => {
                    if (!args[0] || !args[0].includes('🎨')) {
                        originalLog.apply(console, args);
                    }
                };
            }

            // Enable auto-save by default in production
            if (typeof localStorage !== 'undefined') {
                enableAutoSave();
            }

            emitEvent('canvas:initialized', {
                width: state.width,
                height: state.height,
                options: state.options
            });
            return true;
        } catch (error) {
            console.error('❌ Canvas initialization failed:', error);
            emitEvent('canvas:error', { error: error.message, phase: 'initialization' });
            return false;
        }
    };

    // Public API
    return {
        // Initialization
        init,
        destroy,

        // Canvas access
        getCanvas,
        getContext,
        getDimensions,

        // Coordinate transformations
        screenToWorld,
        worldToScreen,
        applyTransform,
        getTransformMatrix,
        transformRect,
        batchScreenToWorld,
        batchWorldToScreen,

        // Viewport utilities
        getVisibleBounds,
        isRectVisible,

        // Viewport management
        updateViewport,
        panToPoint,
        zoomToPoint,
        fitToContent,
        resetViewport,
        getViewportCenter,
        isAtZoomLimit,

        // Control style management
        setControlStyle,
        getControlStyle,
        getAvailableControlStyles,
        getControlStyleInfo,

        // Event system integration
        getEmittedEvents,
        getListenedEvents,

        // Performance optimization
        scheduleUpdate,
        getCullingBounds,
        isPointInCullingBounds,
        cullRectangles,
        cullPoints,
        getPerformanceStats,
        startPerformanceMonitoring,
        stopPerformanceMonitoring,
        clearCaches,
        optimizeForLargeSpace,

        // State persistence
        serializeState,
        restoreState,
        enableAutoSave,
        disableAutoSave,
        loadState,
        saveState,
        clearSavedState,

        // Error handling and recovery
        emergencyReset: () => {
            console.warn('🎨 Performing emergency reset...');

            try {
                // Reset viewport to safe defaults
                state.panX = 0;
                state.panY = 0;
                state.zoom = 1;
                state.isDragging = false;
                state.isTouch = false;
                state.lastPointer = null;

                // Clear any ongoing animations
                if (animationId) {
                    cancelAnimationFrame(animationId);
                    animationId = null;
                }

                // Reset canvas if needed
                if (state.canvas && state.ctx) {
                    state.ctx.setTransform(1, 0, 0, 1, 0, 0);
                    state.ctx.clearRect(0, 0, state.width, state.height);
                }

                emitEvent('canvas:emergency-reset', {
                    timestamp: Date.now(),
                    reason: 'Manual emergency reset'
                });

                console.log('🎨 Emergency reset completed');
                return true;
            } catch (error) {
                console.error('🎨 Emergency reset failed:', error);
                return false;
            }
        },

        validateState: () => {
            const issues = [];

            if (!state.canvas) issues.push('Canvas element missing');
            if (!state.ctx) issues.push('Canvas context missing');
            if (isNaN(state.panX) || isNaN(state.panY)) issues.push('Invalid pan coordinates');
            if (isNaN(state.zoom) || state.zoom <= 0) issues.push('Invalid zoom level');
            if (state.width <= 0 || state.height <= 0) issues.push('Invalid canvas dimensions');

            if (issues.length > 0) {
                console.warn('🎨 State validation issues:', issues);

                emitEvent('canvas:state-validation-failed', {
                    issues,
                    state: getState(),
                    timestamp: Date.now()
                });

                return { valid: false, issues };
            }

            return { valid: true, issues: [] };
        },

        // State access
        getState,

        // Utilities
        resizeCanvas
    };
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { createInfiniteCanvas };
} else {
    window.FAPInfiniteCanvas = { createInfiniteCanvas };
}