;; Script that generates classes and properties for a demo of inferring properties.
;; To generate this graph:
;; bb dev:db-create inferred deps/db/create_graph/inferred.edn
;;
;; To try the demo in the UI, in any page type:
;; - Good Will Hunting #Movie #Ben-Affleck
;; or
;; - DB 3 #Meeting #Tienson
{:auto-create-ontology? true
 :classes {:Movie {:build/class-properties [:actor :comment]}
           :Meeting {:build/class-properties [:attendee :duration]}}
 :properties
 {:actor {:logseq.property/type :node
          :db/cardinality :many
          :build/property-classes [:Person]}
  :attendee {:logseq.property/type :node
             :db/cardinality :many
             :build/property-classes [:Person]}}
 :pages-and-blocks
 [{:page {:block/title "<PERSON><PERSON><PERSON>" :build/tags [:Person]}}
  {:page {:block/title "Ben-Affleck" :build/tags [:Person]}}
  {:page {:block/title "Tienson" :build/tags [:Person]}}
  {:page {:block/title "Zhi<PERSON>" :build/tags [:Person]}}]}
