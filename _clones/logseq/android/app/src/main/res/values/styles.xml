<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
    </style>

    <style name="AppTheme.NoActionBar" parent="Theme.AppCompat.DayNight.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:background">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:navigationBarColor">@color/colorPrimary</item>
        <item name="android:statusBarColor">@color/colorPrimary</item>
    </style>

    <!-- App Starting -->
    <style name="AppTheme.NoActionBarLaunch" parent="Theme.SplashScreen">
        <item name="android:windowBackground">@drawable/splash_centered</item>

        <item name="windowSplashScreenAnimationDuration">200</item>
        <item name="windowSplashScreenIconBackgroundColor">@color/logoPrimary</item>
        <item name="postSplashScreenTheme">@style/AppTheme</item>
    </style>
</resources>
