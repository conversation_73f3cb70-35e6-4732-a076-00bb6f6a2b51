[{"pkg": "@capacitor/action-sheet", "classpath": "com.capacitorjs.plugins.actionsheet.ActionSheetPlugin"}, {"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/camera", "classpath": "com.capacitorjs.plugins.camera.CameraPlugin"}, {"pkg": "@capacitor/clipboard", "classpath": "com.capacitorjs.plugins.clipboard.ClipboardPlugin"}, {"pkg": "@capacitor/filesystem", "classpath": "com.capacitorjs.plugins.filesystem.FilesystemPlugin"}, {"pkg": "@capacitor/haptics", "classpath": "com.capacitorjs.plugins.haptics.HapticsPlugin"}, {"pkg": "@capacitor/keyboard", "classpath": "com.capacitorjs.plugins.keyboard.KeyboardPlugin"}, {"pkg": "@capacitor/share", "classpath": "com.capacitorjs.plugins.share.SharePlugin"}, {"pkg": "@capacitor/splash-screen", "classpath": "com.capacitorjs.plugins.splashscreen.SplashScreenPlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}, {"pkg": "@capawesome/capacitor-background-task", "classpath": "io.capawesome.capacitorjs.plugins.backgroundtask.BackgroundTaskPlugin"}, {"pkg": "@capgo/capacitor-navigation-bar", "classpath": "ee.forgr.capacitor_navigation_bar.NavigationBarPlugin"}, {"pkg": "capacitor-voice-recorder", "classpath": "com.tchvu3.capacitorvoicerecorder.VoiceRecorder"}, {"pkg": "send-intent", "classpath": "de.mindlib.sendIntent.SendIntent"}, {"pkg": "@jcesarmobile/ssl-skip", "classpath": "com.jcesarmobile.sslskip.SslSkipPlugin"}]