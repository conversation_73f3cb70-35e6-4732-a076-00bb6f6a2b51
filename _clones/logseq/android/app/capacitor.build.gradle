// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_21
        targetCompatibility JavaVersion.VERSION_21
    }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation 'androidx.core:core-ktx:1.16.0'
    implementation project(':capacitor-action-sheet')
    implementation project(':capacitor-app')
    implementation project(':capacitor-camera')
    implementation project(':capacitor-clipboard')
    implementation project(':capacitor-filesystem')
    implementation project(':capacitor-haptics')
    implementation project(':capacitor-keyboard')
    implementation project(':capacitor-share')
    implementation project(':capacitor-splash-screen')
    implementation project(':capacitor-status-bar')
    implementation project(':capawesome-capacitor-background-task')
    implementation project(':capgo-capacitor-navigation-bar')
    implementation project(':capacitor-voice-recorder')
    implementation project(':send-intent')
    implementation project(':jcesarmobile-ssl-skip')

}


if (hasProperty('postBuildExtras')) {
    postBuildExtras()
}
