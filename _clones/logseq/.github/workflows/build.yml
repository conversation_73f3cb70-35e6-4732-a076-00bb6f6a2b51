name: CI

on:
  push:
    branches: [master]
    paths-ignore:
      - '*.md'
  pull_request:
    branches: [master]
    paths-ignore:
      - '*.md'

env:
  CLOJURE_VERSION: '1.11.1.1413'
  JAVA_VERSION: '11'
  # This is the latest node version we can run.
  NODE_VERSION: '20'
  BABASHKA_VERSION: '1.0.168'

jobs:
  typos:
    name: Spell Check with Typos
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout Actions Repository
        uses: actions/checkout@v4
      - name: Check spelling with custom config file
        uses: crate-ci/typos@v1.16.8
        with:
          config: ./typos.toml

  test:
    strategy:
      matrix:
        operating-system: [ubuntu-22.04]

    runs-on: ${{ matrix.operating-system }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Node
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'yarn'
          cache-dependency-path: yarn.lock

      - name: Set up Java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}

      - name: Set up Clojure
        uses: DeLaGuardo/setup-clojure@10.1
        with:
          cli: ${{ env.CLOJURE_VERSION }}

      - name: Clojure cache
        uses: actions/cache@v3
        id: clojure-deps
        with:
          path: |
            ~/.m2/repository
            ~/.gitlibs
          key: ${{ runner.os }}-clojure-deps-${{ hashFiles('deps.edn') }}
          restore-keys: ${{ runner.os }}-clojure-deps-

      - name: Fetch Clojure deps
        if: steps.clojure-deps.outputs.cache-hit != 'true'
        run: clojure -A:cljs -P

      - name: Fetch yarn deps
        run: yarn install --frozen-lockfile

      - name: Build test asset
        run: clojure -M:test compile test

      - name: Run some ClojureScript tests against DB version
        run: DB_GRAPH=1 node static/tests.js -r frontend.db.query-dsl-test

      - name: Run ClojureScript query tests against DB version with basic query type
        run: DB_GRAPH=1 DB_QUERY_TYPE=basic node static/tests.js -r frontend.db.query-dsl-test

      - name: Run ClojureScript tests
        run: node static/tests.js -e fix-me

  lint:
    runs-on: ubuntu-22.04

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}

      - name: Set up Clojure
        uses: DeLaGuardo/setup-clojure@10.1
        with:
          cli: ${{ env.CLOJURE_VERSION }}
          bb: ${{ env.BABASHKA_VERSION }}

      - name: Run clj-kondo lint
        run: clojure -M:clj-kondo --parallel --lint src

      - name: Carve lint for unused vars
        run: bb lint:carve

      - name: Lint for vars that are too large
        run: bb lint:large-vars

      - name: Lint for namespaces that aren't documented
        run: bb lint:ns-docstrings

      - name: Lint invalid translation entries
        run: bb lang:validate-translations

      - name: Lint to keep worker independent of frontend
        run: bb lint:worker-and-frontend-separate

      - name: Lint to keep db and file graph code separate
        run: bb lint:db-and-file-graphs-separate

  db-graph-test:
    strategy:
      matrix:
        operating-system: [ubuntu-latest]

    runs-on: ${{ matrix.operating-system }}

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Node
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'yarn'
          cache-dependency-path: |
            deps/db/yarn.lock
            scripts/yarn.lock

      - name: Set up Java
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}

      - name: Set up Clojure
        uses: DeLaGuardo/setup-clojure@10.1
        with:
          cli: ${{ env.CLOJURE_VERSION }}
          bb: ${{ env.BABASHKA_VERSION }}

      - name: Fetch scripts yarn deps
        run: cd scripts && yarn install --frozen-lockfile

      - name: Create DB graph with properties
        run: cd scripts && yarn nbb-logseq src/logseq/tasks/db_graph/create_graph_with_properties.cljs ./properties-graph

      # TODO: Use a smaller, test-focused graph to test classes
      - name: Create DB graph with classes
        run: cd scripts && yarn nbb-logseq src/logseq/tasks/db_graph/create_graph_with_schema_org.cljs ./schema-graph

      - name: Fetch deps/db yarn deps
        run: cd deps/db && yarn install --frozen-lockfile

      - name: Validate created DB graphs
        run: cd deps/db && yarn nbb-logseq script/validate_db.cljs ../../scripts/properties-graph ../../scripts/schema-graph --closed-maps --group-errors

      - name: Export a created DB graph
        run: cd deps/db && yarn nbb-logseq script/export_graph.cljs ../../scripts/properties-graph -f properties.edn -T

      - name: Create graph from the export and diff the two graphs
        run: cd deps/db && yarn nbb-logseq -cp src:../outliner/src:script script/create_graph.cljs ./properties-graph2 properties.edn -iv && yarn nbb-logseq script/diff_graphs.cljs ../../scripts/properties-graph ./properties-graph2 -T