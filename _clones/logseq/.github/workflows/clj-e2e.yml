name: Clojure E2E

on:
  push:
    branches: [master]
    paths:
      - 'clj-e2e/**'
      - '.github/workflows/clj-e2e.yml'
      - src/**
      - deps/**
      - packages/**
  pull_request:
    branches: [master]
    paths:
      - 'clj-e2e/**'
      - '.github/workflows/clj-e2e.yml'
      - src/**
      - deps/**
      - packages/**

env:
  CLOJURE_VERSION: '1.11.1.1413'
  # This is the latest node version we can run.
  NODE_VERSION: '22'
  BABASHKA_VERSION: '1.0.168'

jobs:
  e2e-test-build:
    name: Test
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Node
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'yarn'
          cache-dependency-path: |
            yarn.lock

      - name: Set up Clojure
        uses: DeLaGuardo/setup-clojure@10.1
        with:
          cli: ${{ env.CLOJURE_VERSION }}
          bb: ${{ env.BABASHKA_VERSION }}

      - name: Clojure cache
        uses: actions/cache@v3
        id: clojure-deps
        with:
          path: |
            ~/.m2/repository
            ~/.gitlibs
          key: ${{ runner.os }}-clojure-deps-${{ hashFiles('deps.edn') }}
          restore-keys: ${{ runner.os }}-clojure-deps-

      - name: Fetch Clojure deps
        if: steps.clojure-deps.outputs.cache-hit != 'true'
        run: clojure -A:cljs -P

      - name: Shadow-cljs cache
        uses: actions/cache@v3
        with:
          path: .shadow-cljs
          # ensure update cache every time
          key: ${{ runner.os }}-shadow-cljs-${{ github.sha }}
          # will match most recent upload
          restore-keys: |
            ${{ runner.os }}-shadow-cljs-

      - name: Fetch yarn deps
        run: yarn install
        env:
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: true

      # NOTE: require the app to be build with DEV-RELEASE flag
      - name: Prepare E2E test build
        run: |
          yarn gulp:build && clojure -M:cljs release app --config-merge "{:closure-defines {frontend.config/DEV-RELEASE true}}" --debug

      - name: Run e2e tests
        run: cd clj-e2e && timeout 30m bb dev
        # env:
        #   DEBUG: "pw:api"

      - name: Collect screenshots
        if: ${{ failure() }}
        uses: actions/upload-artifact@v4
        with:
          name: e2e-screenshots
          path: clj-e2e/e2e-dump/*
          retention-days: 1
