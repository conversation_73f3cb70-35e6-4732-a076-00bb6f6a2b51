# This workflow tries to build iOS app if any changes detected on the iOS source tree,
# ensuring at least it builds.

name: CI-iOS

on:
  push:
    branches: [master]
    paths:
      - 'ios/App'
      - package.json
  pull_request:
    branches: [master]
    paths:
      - 'ios/App'
      - package.json

env:
  CLOJURE_VERSION: '1.11.1.1413'
  NODE_VERSION: '20'
  JAVA_VERSION: '11'

jobs:
  build-app:
    runs-on: macos-14
    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4

      - name: Install Node.js, NPM and Yarn
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "dir=$(yarn cache dir)" >> $GITHUB_OUTPUT

      - name: Cache yarn cache directory
        uses: actions/cache@v3
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Setup Java JDK
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}

      - name: Cache clojure deps
        uses: actions/cache@v3
        with:
          path: |
            ~/.m2/repository
            ~/.gitlibs
          key: ${{ runner.os }}-clojure-lib-${{ hashFiles('**/deps.edn') }}

      - name: Setup clojure
        uses: DeLaGuardo/setup-clojure@10.1
        with:
          cli: ${{ env.CLOJURE_VERSION }}

      - name: Set Build Environment Variables
        run: |
          echo "ENABLE_FILE_SYNC_PRODUCTION=true" >> $GITHUB_ENV

      - name: Compile CLJS
        run: yarn install && yarn release-mobile

      - name: Prepare iOS build
        run: npx cap sync ios

      - name: List iOS build targets
        run: xcodebuild -list -workspace App.xcworkspace
        working-directory: ./ios/App

      - name: Build iOS App
        run: |
          xcodebuild -workspace App.xcworkspace -scheme Logseq -destination generic/platform=iOS build CODE_SIGN_IDENTITY="" CODE_SIGNING_REQUIRED=NO CODE_SIGNING_ALLOWED=NO
        working-directory: ./ios/App
