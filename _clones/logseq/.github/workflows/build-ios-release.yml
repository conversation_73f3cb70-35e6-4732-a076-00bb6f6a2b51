# This workflow build iOS App as a .ipa file and upload it to TestFlight.

name: Build-iOS

on:
  workflow_dispatch:
    inputs:
      git-ref:
        description: "Release Git Ref (Which branch or tag to build?)"
        required: true
        default: "master"

env:
  CLOJURE_VERSION: '1.11.1.1413'
  NODE_VERSION: '20'
  JAVA_VERSION: '11'

jobs:
  build-app:
    runs-on: macos-15
    steps:
      - name: Check out Git repository
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.git-ref }}
      - uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: 16.1
      - name: Install Node.js, NPM and Yarn
        uses: actions/setup-node@v3
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup Java JDK
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: ${{ env.JAVA_VERSION }}

      - name: <PERSON><PERSON> clojure deps
        uses: actions/cache@v3
        with:
          path: |
            ~/.m2/repository
            ~/.gitlibs
          key: ${{ runner.os }}-clojure-lib-${{ hashFiles('**/deps.edn') }}

      - name: Setup clojure
        uses: DeLaGuardo/setup-clojure@10.1
        with:
          cli: ${{ env.CLOJURE_VERSION }}

      - name: Setup build tools
        run: brew install fastlane

      - name: Set Build Environment Variables
        run: |
          echo "ENABLE_FILE_SYNC_PRODUCTION=true" >> $GITHUB_ENV

      - name: Compile CLJS
        run: yarn install && yarn release-mobile
        env:
          LOGSEQ_SENTRY_DSN: ${{ secrets.LOGSEQ_SENTRY_DSN }}
          LOGSEQ_POSTHOG_TOKEN: ${{ secrets.LOGSEQ_POSTHOG_TOKEN }}

      - name: Prepare iOS build
        run: npx cap sync ios

      - name: Build and upload iOS app
        run: fastlane beta
        working-directory: ./ios/App
        env:
          APP_STORE_CONNECT_API_KEY_KEY_ID: ${{ secrets.APP_STORE_CONNECT_API_KEY_KEY_ID }}
          APP_STORE_CONNECT_API_KEY_ISSUER_ID: ${{ secrets.APP_STORE_CONNECT_API_KEY_ISSUER_ID }}
          APP_STORE_CONNECT_API_KEY_KEY: ${{ secrets.APP_STORE_CONNECT_API_KEY_KEY }}
          APP_STORE_CONNECT_API_KEY_IS_KEY_CONTENT_BASE64: true
          SLACK_URL: ${{ secrets.SLACK_URL }}
          MATCH_PASSWORD: ${{ secrets.MATCH_PASSWORD }}
          MATCH_GIT_BASIC_AUTHORIZATION: ${{ secrets.MATCH_GIT_BASIC_AUTHORIZATION }}

      - name: Save Static File
        uses: actions/upload-artifact@v4
        with:
          name: static
          path: static
