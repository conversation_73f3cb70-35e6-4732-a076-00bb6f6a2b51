/**
 * FAP Event System - Simple pub/sub event system for component communication
 * 
 * Provides a lightweight event system for decoupled communication between
 * different parts of the application. No dependencies, pure vanilla JS.
 */

class EventBus {
    constructor() {
        this.listeners = new Map();
        this.onceListeners = new Map();
        this.debugMode = false;
    }

    /**
     * Subscribe to an event
     * @param {string} event - Event name
     * @param {function} callback - Callback function
     * @param {object} options - Options (priority, context)
     */
    on(event, callback, options = {}) {
        if (typeof callback !== 'function') {
            throw new Error('Callback must be a function');
        }

        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }

        const listener = {
            callback,
            priority: options.priority || 0,
            context: options.context || null,
            id: this.generateId()
        };

        const listeners = this.listeners.get(event);
        listeners.push(listener);
        
        // Sort by priority (higher priority first)
        listeners.sort((a, b) => b.priority - a.priority);

        if (this.debugMode) {
            console.log(`[EventBus] Registered listener for '${event}'`, listener);
        }

        return listener.id;
    }

    /**
     * Subscribe to an event that will only fire once
     * @param {string} event - Event name
     * @param {function} callback - Callback function
     * @param {object} options - Options
     */
    once(event, callback, options = {}) {
        if (!this.onceListeners.has(event)) {
            this.onceListeners.set(event, []);
        }

        const listener = {
            callback,
            priority: options.priority || 0,
            context: options.context || null,
            id: this.generateId()
        };

        const listeners = this.onceListeners.get(event);
        listeners.push(listener);
        listeners.sort((a, b) => b.priority - a.priority);

        return listener.id;
    }

    /**
     * Unsubscribe from an event
     * @param {string} event - Event name
     * @param {string|function} callbackOrId - Callback function or listener ID
     */
    off(event, callbackOrId) {
        if (this.listeners.has(event)) {
            const listeners = this.listeners.get(event);
            const index = typeof callbackOrId === 'string' 
                ? listeners.findIndex(l => l.id === callbackOrId)
                : listeners.findIndex(l => l.callback === callbackOrId);
            
            if (index !== -1) {
                listeners.splice(index, 1);
                if (this.debugMode) {
                    console.log(`[EventBus] Removed listener for '${event}'`);
                }
            }
        }

        if (this.onceListeners.has(event)) {
            const listeners = this.onceListeners.get(event);
            const index = typeof callbackOrId === 'string'
                ? listeners.findIndex(l => l.id === callbackOrId)
                : listeners.findIndex(l => l.callback === callbackOrId);
            
            if (index !== -1) {
                listeners.splice(index, 1);
            }
        }
    }

    /**
     * Emit an event
     * @param {string} event - Event name
     * @param {*} data - Data to pass to listeners
     * @param {object} options - Emit options
     */
    emit(event, data = null, options = {}) {
        const startTime = performance.now();
        let listenerCount = 0;

        if (this.debugMode) {
            console.log(`[EventBus] Emitting '${event}'`, data);
        }

        // Handle regular listeners
        if (this.listeners.has(event)) {
            const listeners = this.listeners.get(event);
            for (const listener of listeners) {
                try {
                    if (listener.context) {
                        listener.callback.call(listener.context, data, event);
                    } else {
                        listener.callback(data, event);
                    }
                    listenerCount++;
                } catch (error) {
                    console.error(`[EventBus] Error in listener for '${event}':`, error);
                    if (options.throwOnError) {
                        throw error;
                    }
                }
            }
        }

        // Handle once listeners
        if (this.onceListeners.has(event)) {
            const listeners = this.onceListeners.get(event);
            const listenersToRemove = [];

            for (const listener of listeners) {
                try {
                    if (listener.context) {
                        listener.callback.call(listener.context, data, event);
                    } else {
                        listener.callback(data, event);
                    }
                    listenerCount++;
                    listenersToRemove.push(listener);
                } catch (error) {
                    console.error(`[EventBus] Error in once listener for '${event}':`, error);
                    listenersToRemove.push(listener);
                    if (options.throwOnError) {
                        throw error;
                    }
                }
            }

            // Remove once listeners that have fired
            listenersToRemove.forEach(listener => {
                const index = listeners.indexOf(listener);
                if (index !== -1) {
                    listeners.splice(index, 1);
                }
            });
        }

        if (this.debugMode) {
            const duration = performance.now() - startTime;
            console.log(`[EventBus] '${event}' completed in ${duration.toFixed(2)}ms, ${listenerCount} listeners`);
        }

        return listenerCount;
    }

    /**
     * Remove all listeners for an event or all events
     * @param {string} event - Event name (optional)
     */
    clear(event = null) {
        if (event) {
            this.listeners.delete(event);
            this.onceListeners.delete(event);
            if (this.debugMode) {
                console.log(`[EventBus] Cleared all listeners for '${event}'`);
            }
        } else {
            this.listeners.clear();
            this.onceListeners.clear();
            if (this.debugMode) {
                console.log('[EventBus] Cleared all listeners');
            }
        }
    }

    /**
     * Get list of events with listeners
     */
    getEvents() {
        const events = new Set();
        this.listeners.forEach((_, event) => events.add(event));
        this.onceListeners.forEach((_, event) => events.add(event));
        return Array.from(events);
    }

    /**
     * Get listener count for an event
     * @param {string} event - Event name
     */
    getListenerCount(event) {
        const regular = this.listeners.has(event) ? this.listeners.get(event).length : 0;
        const once = this.onceListeners.has(event) ? this.onceListeners.get(event).length : 0;
        return regular + once;
    }

    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Debug mode enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        console.log(`[EventBus] Debug mode ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Generate unique ID for listeners
     */
    generateId() {
        return 'listener_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * Create a namespaced event emitter
     * @param {string} namespace - Namespace prefix
     */
    namespace(namespace) {
        return {
            on: (event, callback, options) => this.on(`${namespace}:${event}`, callback, options),
            once: (event, callback, options) => this.once(`${namespace}:${event}`, callback, options),
            off: (event, callbackOrId) => this.off(`${namespace}:${event}`, callbackOrId),
            emit: (event, data, options) => this.emit(`${namespace}:${event}`, data, options),
            clear: (event) => this.clear(event ? `${namespace}:${event}` : null)
        };
    }
}

// Global event bus instance
const globalEventBus = new EventBus();

// Convenience functions for global event bus
const Events = {
    on: (event, callback, options) => globalEventBus.on(event, callback, options),
    once: (event, callback, options) => globalEventBus.once(event, callback, options),
    off: (event, callbackOrId) => globalEventBus.off(event, callbackOrId),
    emit: (event, data, options) => globalEventBus.emit(event, data, options),
    clear: (event) => globalEventBus.clear(event),
    getEvents: () => globalEventBus.getEvents(),
    getListenerCount: (event) => globalEventBus.getListenerCount(event),
    setDebugMode: (enabled) => globalEventBus.setDebugMode(enabled),
    namespace: (namespace) => globalEventBus.namespace(namespace),
    createBus: () => new EventBus()
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { EventBus, Events };
} else {
    window.FAPEventSystem = { EventBus, Events };
    window.Events = Events; // Global convenience
}