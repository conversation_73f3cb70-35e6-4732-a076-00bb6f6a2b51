# FAP Event System

A lightweight, vanilla JavaScript pub/sub event system for decoupled component communication. No dependencies, no build step, just clean event-driven architecture.

## Features

- **Simple API**: Easy to use `on()`, `off()`, `emit()` methods
- **Once Listeners**: Listeners that automatically remove themselves after first execution
- **Priority System**: Control the order in which listeners are called
- **Namespacing**: Organize events with namespaces (e.g., `block:changed`)
- **Context Binding**: Bind listeners to specific contexts
- **Debug Mode**: Built-in debugging and performance monitoring
- **Error Handling**: Graceful error handling with optional error throwing
- **Multiple Buses**: Create isolated event buses when needed

## Basic Usage

```javascript
// Listen for events
Events.on('user:login', (userData) => {
    console.log('User logged in:', userData);
});

// Emit events
Events.emit('user:login', { id: 123, name: 'John' });

// One-time listeners
Events.once('app:ready', () => {
    console.log('App is ready!');
});

// Remove listeners
const listenerId = Events.on('test', callback);
Events.off('test', listenerId);
```

## Advanced Features

### Namespacing
```javascript
const blockEvents = Events.namespace('block');
blockEvents.on('changed', handleBlockChange);
blockEvents.emit('changed', blockData);
```

### Priority Listeners
```javascript
Events.on('render', lowPriorityHandler, { priority: 1 });
Events.on('render', highPriorityHandler, { priority: 10 });
// High priority handler runs first
```

### Context Binding
```javascript
const component = { name: 'MyComponent' };
Events.on('update', function(data) {
    console.log(this.name, 'received:', data);
}, { context: component });
```

### Debug Mode
```javascript
Events.setDebugMode(true);
// Now all events will be logged with timing information
```

## Integration with Block System

The event system integrates seamlessly with our block core:

```javascript
// In block-core.js
Events.on('block:changed', (block) => {
    // Update UI, save to database, etc.
});

Events.on('block:created', (block) => {
    // Handle new block creation
});

// Emit from blocks
block.updateContent('new content');
Events.emit('block:changed', block);
```

## Common Event Patterns

### Block Events
- `block:created` - New block created
- `block:changed` - Block content updated
- `block:deleted` - Block removed
- `block:moved` - Block hierarchy changed

### Page Events
- `page:created` - New page created
- `page:renamed` - Page name changed
- `page:deleted` - Page removed

### Editor Events
- `editor:focus` - Editor gained focus
- `editor:blur` - Editor lost focus
- `editor:selection` - Selection changed

### System Events
- `app:ready` - Application initialized
- `app:error` - Application error occurred
- `db:connected` - Database connection established

## Performance

The event system is designed for performance:
- Listeners are sorted by priority only when added
- Event emission is O(n) where n is the number of listeners
- Debug mode includes timing information
- Memory efficient with automatic cleanup of once listeners

## Error Handling

```javascript
// Listeners that throw errors won't break other listeners
Events.on('test', () => {
    throw new Error('Something went wrong');
});

Events.on('test', () => {
    console.log('This still runs');
});

// Optional: throw on first error
Events.emit('test', data, { throwOnError: true });
```

## API Reference

### Events.on(event, callback, options)
- `event`: Event name (string)
- `callback`: Function to call when event is emitted
- `options`: { priority: number, context: object }
- Returns: Listener ID (string)

### Events.once(event, callback, options)
Same as `on()` but listener is removed after first execution.

### Events.off(event, callbackOrId)
- `event`: Event name
- `callbackOrId`: Original callback function or listener ID

### Events.emit(event, data, options)
- `event`: Event name
- `data`: Data to pass to listeners
- `options`: { throwOnError: boolean }
- Returns: Number of listeners that were called

### Events.clear(event)
Remove all listeners for an event (or all events if no event specified).

### Events.namespace(namespace)
Returns a namespaced event emitter with the same API.

## Design Philosophy

1. **KISS**: Keep it simple, no unnecessary complexity
2. **Performance**: Fast event emission and listener management
3. **Flexibility**: Support various event patterns without being opinionated
4. **Debugging**: Built-in tools for understanding event flow
5. **Isolation**: Multiple event buses when needed for component isolation