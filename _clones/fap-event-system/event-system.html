<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAP Event System - Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .demo-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        button {
            margin: 5px;
            padding: 8px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f8f9fa;
            cursor: pointer;
        }
        
        button:hover {
            background: #e9ecef;
        }
        
        #log {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 0;
            border-bottom: 1px solid #eee;
        }
        
        .log-emit { color: #0066cc; }
        .log-listen { color: #059669; }
        .log-error { color: #dc2626; }
    </style>
</head>
<body>
    <h1>FAP Event System Demo</h1>
    
    <div class="demo-section">
        <h3>Basic Events</h3>
        <button onclick="emitTestEvent()">Emit Test Event</button>
        <button onclick="emitDataEvent()">Emit Event with Data</button>
        <button onclick="emitOnceEvent()">Emit Once Event</button>
        <button onclick="addListener()">Add Listener</button>
        <button onclick="removeListener()">Remove Listener</button>
    </div>

    <div class="demo-section">
        <h3>Namespaced Events</h3>
        <button onclick="emitBlockEvent()">Emit Block Event</button>
        <button onclick="emitPageEvent()">Emit Page Event</button>
        <button onclick="setupNamespaces()">Setup Namespaces</button>
    </div>

    <div class="demo-section">
        <h3>Priority & Context</h3>
        <button onclick="emitPriorityEvent()">Emit Priority Event</button>
        <button onclick="setupPriorityListeners()">Setup Priority Listeners</button>
        <button onclick="emitContextEvent()">Emit Context Event</button>
    </div>

    <div class="demo-section">
        <h3>Debug & Info</h3>
        <button onclick="toggleDebug()">Toggle Debug Mode</button>
        <button onclick="showEventInfo()">Show Event Info</button>
        <button onclick="clearAllEvents()">Clear All Events</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="demo-section">
        <h3>Event Log</h3>
        <div id="log"></div>
    </div>

    <script src="fap-event-system.js"></script>
    <script>
        // Get the global Events object
        const { Events } = window.FAPEventSystem;
        
        let testListenerId = null;
        let debugMode = false;

        // Logging function
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(message);
        }

        // Basic event functions
        function emitTestEvent() {
            Events.emit('test:basic', 'Hello from test event!');
            log('Emitted test:basic event', 'emit');
        }

        function emitDataEvent() {
            const data = {
                message: 'Event with data',
                timestamp: Date.now(),
                random: Math.random()
            };
            Events.emit('test:data', data);
            log('Emitted test:data event with data: ' + JSON.stringify(data), 'emit');
        }

        function emitOnceEvent() {
            Events.emit('test:once', 'This should only be heard once');
            log('Emitted test:once event', 'emit');
        }

        function addListener() {
            if (testListenerId) {
                log('Listener already exists', 'error');
                return;
            }
            
            testListenerId = Events.on('test:basic', (data) => {
                log('Received test:basic: ' + data, 'listen');
            });
            log('Added listener for test:basic', 'listen');
        }

        function removeListener() {
            if (!testListenerId) {
                log('No listener to remove', 'error');
                return;
            }
            
            Events.off('test:basic', testListenerId);
            testListenerId = null;
            log('Removed listener for test:basic', 'listen');
        }

        // Namespaced events
        const blockEvents = Events.namespace('block');
        const pageEvents = Events.namespace('page');

        function emitBlockEvent() {
            blockEvents.emit('changed', { id: 'block-123', content: 'Updated content' });
            log('Emitted block:changed event', 'emit');
        }

        function emitPageEvent() {
            pageEvents.emit('created', { name: 'New Page', id: 'page-456' });
            log('Emitted page:created event', 'emit');
        }

        function setupNamespaces() {
            blockEvents.on('changed', (data) => {
                log('Block changed: ' + JSON.stringify(data), 'listen');
            });

            pageEvents.on('created', (data) => {
                log('Page created: ' + JSON.stringify(data), 'listen');
            });

            log('Setup namespace listeners', 'listen');
        }

        // Priority and context
        function setupPriorityListeners() {
            Events.on('priority:test', () => log('Priority 0 (default)', 'listen'), { priority: 0 });
            Events.on('priority:test', () => log('Priority 10 (high)', 'listen'), { priority: 10 });
            Events.on('priority:test', () => log('Priority 5 (medium)', 'listen'), { priority: 5 });
            Events.on('priority:test', () => log('Priority -5 (low)', 'listen'), { priority: -5 });
            log('Setup priority listeners (should fire in order: 10, 5, 0, -5)', 'listen');
        }

        function emitPriorityEvent() {
            Events.emit('priority:test');
            log('Emitted priority:test event', 'emit');
        }

        function emitContextEvent() {
            const context = { name: 'TestContext' };
            Events.on('context:test', function(data) {
                log('Context event received by: ' + this.name, 'listen');
            }, { context });
            
            Events.emit('context:test', 'test data');
            log('Emitted context:test event', 'emit');
        }

        // Debug and info
        function toggleDebug() {
            debugMode = !debugMode;
            Events.setDebugMode(debugMode);
            log('Debug mode ' + (debugMode ? 'enabled' : 'disabled'));
        }

        function showEventInfo() {
            const events = Events.getEvents();
            log('Active events: ' + events.join(', '));
            
            events.forEach(event => {
                const count = Events.getListenerCount(event);
                log(`  ${event}: ${count} listeners`);
            });
        }

        function clearAllEvents() {
            Events.clear();
            log('Cleared all event listeners');
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        // Initialize demo
        document.addEventListener('DOMContentLoaded', function() {
            log('FAP Event System Demo initialized');
            
            // Setup some default listeners
            Events.on('test:data', (data) => {
                log('Received test:data: ' + JSON.stringify(data), 'listen');
            });

            Events.once('test:once', (data) => {
                log('Received test:once (once listener): ' + data, 'listen');
            });

            // Setup error handling
            Events.on('error', (error) => {
                log('Error event: ' + error.message, 'error');
            });

            log('Default listeners setup complete');
        });
    </script>
</body>
</html>