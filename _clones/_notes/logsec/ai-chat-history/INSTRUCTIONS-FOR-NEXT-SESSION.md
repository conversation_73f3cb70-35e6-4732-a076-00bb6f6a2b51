# CRITICAL INSTRUCTIONS FOR NEXT SESSION - Canvas Restart

## IMMEDIATE CONTEXT

**PROBLEM**: The current `fap-infinite-canvas.js` is 1,701 lines and BROKEN. The zoom functionality doesn't work - content moves toward bottom-right instead of staying under cursor during zoom.

**CAUSE**: Over-engineering. Added 15 features simultaneously instead of focusing on core zoom functionality.

**SOLUTION**: Start completely fresh with minimal implementation.

## WHAT YOU MUST DO FIRST

### 1. READ THE FAILURE ANALYSIS
- Read `_notes/logsec/ai-chat-history/ai-14-canvas-complexity-issue.md`
- Understand why the complex approach failed
- Learn from the over-engineering mistakes

### 2. RESEARCH LOGSEQ'S IMPLEMENTATION
**CRITICAL**: Before writing ANY code, research how Logseq actually implements zoom:

```bash
# Search for canvas/whiteboard zoom implementation in logseq source
find logseq/ -name "*.js" -o -name "*.ts" | xargs grep -l "zoom\|canvas\|whiteboard"
```

**Look for**:
- How they calculate zoom-to-cursor
- Their coordinate transformation math
- Their pan/zoom event handling
- Keep it SIMPLE like they do

### 3. CREATE MINIMAL TEST FIRST
Before any implementation, create a simple test:

```html
<!-- test-zoom.html -->
<canvas id="test" width="400" height="300"></canvas>
<script>
// Draw one red square at (100, 100)
// Test: hover over square, wheel zoom
// Expected: square stays under cursor
</script>
```

## IMPLEMENTATION RULES

### ABSOLUTE REQUIREMENTS:
1. **MAX 200 LINES** for core functionality
2. **ONLY pan and zoom** - nothing else
3. **Test zoom on ONE shape** until perfect
4. **Functional programming** - no classes
5. **KISS principle** - simplest possible solution

### FORBIDDEN FEATURES (until zoom works):
- ❌ Touch support
- ❌ Keyboard navigation  
- ❌ Performance monitoring
- ❌ State persistence
- ❌ Error handling
- ❌ Multiple control styles
- ❌ Event system integration
- ❌ Debug panels
- ❌ CSS styling

### ZOOM MATH TO GET RIGHT:
```javascript
// The ONLY thing that matters:
const zoomAtCursor = (mouseX, mouseY, zoomFactor) => {
    // This must keep the world point under cursor fixed
    // Study Logseq's implementation for the correct math
};
```

## STEP-BY-STEP APPROACH

### Phase 1: Research (30 minutes max)
1. Find Logseq's zoom implementation
2. Understand their coordinate math
3. Document their approach

### Phase 2: Minimal Canvas (1 hour max)
```javascript
// Target structure:
const createCanvas = (container) => {
    let panX = 0, panY = 0, zoom = 1;
    
    const draw = () => { /* render one test shape */ };
    const onWheel = (e) => { /* zoom at cursor */ };
    const onMouseDown = (e) => { /* start pan */ };
    const onMouseMove = (e) => { /* pan if dragging */ };
    
    return { /* minimal API */ };
};
```

### Phase 3: Test Until Perfect
1. Create test with ONE red square
2. Test zoom behavior
3. Fix until square stays under cursor
4. Only then consider adding features

## DEBUGGING APPROACH

If zoom still doesn't work:

### 1. Log Everything:
```javascript
console.log('Before zoom:', { panX, panY, zoom, mouseX, mouseY });
console.log('After zoom:', { panX, panY, zoom });
```

### 2. Test Math Manually:
- Pick specific values (mouseX=200, mouseY=150, zoom=1→2)
- Calculate expected pan values by hand
- Verify code produces same result

### 3. Compare to Working Examples:
- Study Google Maps zoom behavior
- Find other working canvas zoom implementations
- Copy their math exactly

## WHAT WORKED FROM PREVIOUS SESSION

### Keep These Concepts:
- ✅ Functional programming approach
- ✅ Event-driven architecture (later)
- ✅ Semantic HTML elements (later)
- ✅ User preference for functional over OOP

### Abandon These:
- ❌ Complex state management
- ❌ Multiple features at once
- ❌ Over-engineered architecture
- ❌ Premature optimization

## SUCCESS CRITERIA

**Session is successful ONLY if**:
1. ✅ One shape stays under cursor during zoom
2. ✅ Code is under 200 lines
3. ✅ Pan and zoom work smoothly
4. ✅ No feature creep

**Then and only then** consider adding:
- Touch support
- Keyboard navigation
- Event integration
- etc.

## FINAL REMINDERS

- **KISS**: Keep It Simple Stupid
- **Focus**: Zoom functionality ONLY
- **Test**: One shape, perfect behavior
- **Research**: Learn from Logseq first
- **Minimal**: 200 lines maximum

The user is frustrated because basic zoom doesn't work despite 1,701 lines of code. Prove that simple, focused code can solve this better than complex architecture.

**START WITH RESEARCH, NOT CODE.**