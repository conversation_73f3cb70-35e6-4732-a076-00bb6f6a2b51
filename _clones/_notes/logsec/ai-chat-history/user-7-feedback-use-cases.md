Great! I have never used 'logseq' or any of its competitors like 'notion' or 'obsidian' so I will not be much help in optimizing how it will fit into the bigger picture. But, it is 'open source', and you can break it down into very small parts, so I am confident that one or more uses will evolve. The first problem I am trying to solve is managing and search through the massive amounts of markdown file created, and being able to ask questions from the content in those markdown files. This may be too big a problem for now, but nice to keep in mind while you engineer test and use cases. For example, this repo has lots of markdown files. How can we manage, discover and search them? The answer may be with the next version of 'logsec' which is DB orientated? So, as you break the original app into parts, keep in mind various 'use cases' for those parts and make a note of them. Otherwise, continue on!