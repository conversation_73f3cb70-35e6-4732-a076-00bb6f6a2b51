# User Input - Project Introduction

## Date: 2025-01-25

## Original User Request:

You are currently inside a subfolder of a much larger pnpm monorepo (Home Repo), named '_clones'. The reason you are in the sub-folder is to limit your focus to just this task. There are two github cloned source folders for "logseq", one the 'app', the other the 'docs', the app has two versions, a classic file based and a newer database based. Check both folders and learn about "logsec". It is an open source project. Make a concise review of "logsec". You goal will be to build our own version, that is compatible with the offical version. In the HR, we will have both web and desktop (electron) apps. I would like to refactor the loqsec code into small functional (not OOP) plain vanilla compontents/packages/modules (we need to pick a name), that can combine to make the original app clone, or be used separately. For example, loqsec may have a "whiteboard" feature, instead of duplicating that elsewhere in HR, we have one whiteboard package. But that whiteboard may also use an "infinite canvas" and maybe we want to use an "infinte canvas" elsewhere in HR. Or maybe elsewhere in HR we have a better "infinite canvas" and want to use it in our version of the logsec app. So your goal is to reverse engineer "logseq" into the smallest "lego bricks" (packages) that can be used anywhere. Create an indepentant folder for each package, inside will be "fap-<featureName>.js", "fap-<featureName>.css", "<featureName.html> (basic example), "README-<featureName>.md".We use semantic naming for all our generic "divs" or "span" elements, but we do not create them as web components or register them. So instead of '<div id="widget"'> we will use '<widget>, then in our css, instead of 'div#widget' we can just use 'widget'. We can discuss this more later.Start by becoming an expert in all things "logsec", use the web if needed or any MCP servers that can help (ask), then come up with a plan to break it down into the smallest packages possible.The above may not be perfect, ask questions so you have a awesome understanding.To facilitate long term planning, let's keep track of these sessions chat context by copying them here: '_notes/logsec/ai-chat-history/

## Key Requirements Identified:

1. **Technology Stack**: Plain vanilla JavaScript/CSS/HTML, no frameworks, no build step
2. **Architecture**: Modular "lego brick" packages that can be used independently
3. **Naming**: fap-<featureName> for packages, semantic HTML elements
4. **Compatibility**: Should work with official Logseq where possible
5. **Platforms**: Web and desktop (Electron) versions needed
6. **Reusability**: Packages should be usable across Home Repo ecosystem
7. **Documentation**: Each package needs comprehensive README and examples

## User's Vision:

- Break Logseq into smallest possible reusable components
- Enable mixing and matching of components (e.g., better infinite canvas from elsewhere)
- Semantic HTML approach: `<widget>` instead of `<div id="widget">`
- Multi-session project with proper documentation trail