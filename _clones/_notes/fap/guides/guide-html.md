# HTML Guide – Semantic Custom Elements (FAP Style)

## ✅ Goals
- Use **semantic custom elements** for clarity and structure
- Avoid legacy `<div class="...">` patterns
- Target only the **latest Chromium** (Electron app)
- Keep **zero-dependency**, **no framework**, **no build step**

---

## ✅ Custom Element Naming Rules

- Must contain a **hyphen** (`-`)
- Must begin with a lowercase ASCII letter
- Must not clash with built-in tags
- ✅ Preferred prefix: `fap-` (e.g. `<fap-chat-message>`)

Valid:
```html
<fap-chat-message>...</fap-chat-message>
```

Invalid:
```html
<chat>        <!-- ❌ No hyphen -->
<ChatMessage> <!-- ❌ Uppercase -->
```

---

## ✅ Styling Custom Tags

Custom tags can be styled **just like native HTML**:
```css
fap-chat-message {
  display: block;
  padding: 1em;
  border: 1px solid #ccc;
}
```

Avoid inline styles (e.g. `this.style`) unless temporary.

---

## ✅ Using `customElements.define()`

Even if no logic is added, define tags to:
- Enable lifecycle hooks
- Future-proof your UI structure

Example:
```ts
customElements.define('fap-chat-message', class extends HTMLElement {});
```

Optional logic:
```ts
customElements.define('fap-chat-message', class extends HTMLElement {
  connectedCallback() {
    // Lifecycle hook (e.g. add behavior or defaults)
  }
});
```

---

## ✅ When *Not* to Use JS

If you only want structure + style, **skip JS**:
```html
<fap-chat-message>
  <strong>James:</strong> Hello!
</fap-chat-message>
```

---

## ✅ Bonus Tips

- Shadow DOM is **optional** — omit it unless you want encapsulation.
- You can use `<slot>` if you want templating features.
- Use `HTMLElement` and `customElements.define()` for full lifecycle access.

---

## ✅ Summary

| Goal                        | Recommendation                      |
|-----------------------------|--------------------------------------|
| Semantic HTML               | Use custom elements with hyphens     |
| Styling                     | Use regular CSS, avoid inline styles |
| Behavior (optional)         | Use `customElements.define()`        |
| Frameworks or tools         | Not required                         |
| Compatibility               | Full support in Chromium (Electron)  |
