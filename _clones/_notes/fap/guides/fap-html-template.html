<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Fap HTML Template</title>
    <style>
        /* --- Base Theme Colors --- */
        :root {
            --background: black;
            --text: white;
            --header-bg: darkgray;
            --main-bg: lightgray;
            --footer-bg: darkgray;
        }

        /* --- CSS Reset --- */
        *,
        *::before,
        *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        button,
        input,
        select,
        textarea {
            font: inherit;
        }

        ul,
        ol {
            list-style: none;
        }

        a {
            color: inherit;
            text-decoration: none;
        }

        img {
            display: block;
            max-width: 100%;
        }

        /* --- Full Viewport Layout --- */
        html,
        body {
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            overflow: hidden;
            background-color: black;
            color: white;
            font-family: system-ui, sans-serif;
            font-size: 16px;
            line-height: 1.5;
        }

        header,
        footer {
            position: absolute;
            left: 1px;
            right: 1px;
            height: 42px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--header-bg);
        }

        header {
            top: 1px;
        }

        footer {
            bottom: 1px;
        }

        main {
            position: absolute;
            top: 44px;
            bottom: 44px;
            left: 1px;
            right: 1px;
            background-color: var(--main-bg);
            color: black;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
            /* no scrolling */
        }
    </style>
</head>

<body>
    <header>Header</header>
    <main>Main content fills the space. Children scroll if needed.</main>
    <footer>Footer</footer>
</body>

</html>