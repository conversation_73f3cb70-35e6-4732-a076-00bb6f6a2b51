# Design Document - FAP Infinite Canvas

## Overview

The FAP Infinite Canvas provides a high-performance, reusable foundation for spatial applications. It uses modern HTML5 Canvas API with optimized pan/zoom navigation, supporting both Logseq-style and Google Maps-style interactions.

**Key Design Principles:**
- **Functional Programming**: Closure-based state management, pure functions
- **Performance First**: 60fps interactions, viewport culling, efficient transforms
- **Reusable**: Clean API for integration with whiteboards, mind maps, node editors
- **Familiar UX**: Google Maps-style navigation as default, Logseq-style as option

## Architecture

### Core Components

```
┌─────────────────────────────────────────────────────────────┐
│                    Canvas System                            │
├─────────────────────────────────────────────────────────────┤
│  Viewport Manager  │  Transform Engine  │  Event Handler   │
│  - Pan/Zoom State  │  - Coordinate Conv │  - Mouse/Touch   │
│  - Bounds Checking │  - Matrix Math     │  - Keyboard      │
│  - Culling         │  - Precision       │  - Gestures      │
├─────────────────────────────────────────────────────────────┤
│              Canvas Renderer & State                        │
│  - HTML5 Canvas    │  - Viewport State  │  - Event System  │
│  - Context 2D      │  - Transform Cache │  - Integration   │
│  - Auto Resize     │  - Performance     │  - Debugging     │
└─────────────────────────────────────────────────────────────┘
```

### State Management

**Functional Closure Pattern:**
```javascript
const createInfiniteCanvas = (container, options = {}) => {
    let state = {
        // Viewport state
        panX: 0, panY: 0,
        zoom: 1,
        
        // Canvas state
        canvas: null,
        ctx: null,
        width: 0, height: 0,
        
        // Interaction state
        isDragging: false,
        lastPointer: null,
        
        // Configuration
        options: { ...defaultOptions, ...options }
    };
    
    // Pure functions that operate on state
    const updateViewport = (newPan, newZoom) => { /* ... */ };
    const screenToWorld = (screenX, screenY) => { /* ... */ };
    
    return { updateViewport, screenToWorld, /* ... */ };
};
```

## Components and Interfaces

### 1. Viewport Manager

**Responsibilities:**
- Track pan offset and zoom level
- Enforce zoom limits (0.1x to 10x, like Google Maps)
- Calculate visible world bounds
- Provide viewport culling utilities

**Key Functions:**
```javascript
const updateViewport = (panX, panY, zoom) => {
    // Clamp zoom to limits
    zoom = Math.max(0.1, Math.min(10, zoom));
    
    // Update state and emit events
    state.panX = panX;
    state.panY = panY;
    state.zoom = zoom;
    
    emitViewportChanged();
};

const getVisibleBounds = () => ({
    left: -state.panX / state.zoom,
    top: -state.panY / state.zoom,
    right: (-state.panX + state.width) / state.zoom,
    bottom: (-state.panY + state.height) / state.zoom
});
```

### 2. Transform Engine

**Responsibilities:**
- Convert between screen and world coordinates
- Apply viewport transformations to canvas context
- Maintain precision across zoom levels
- Cache transformation matrices

**Key Functions:**
```javascript
const screenToWorld = (screenX, screenY) => ({
    x: (screenX - state.panX) / state.zoom,
    y: (screenY - state.panY) / state.zoom
});

const worldToScreen = (worldX, worldY) => ({
    x: worldX * state.zoom + state.panX,
    y: worldY * state.zoom + state.panY
});

const applyTransform = (ctx) => {
    ctx.setTransform(state.zoom, 0, 0, state.zoom, state.panX, state.panY);
};
```

### 3. Event Handler

**Responsibilities:**
- Handle mouse, touch, and keyboard events
- Support both control styles (Logseq/Google Maps)
- Prevent browser default behaviors
- Emit canvas events for integration

**Control Styles:**

**Google Maps Style (Default):**
- Left click + drag = Pan
- Scroll wheel = Zoom at cursor
- Right click = Context menu (if enabled)
- Touch drag = Pan, Pinch = Zoom

**Logseq Style:**
- Middle click + drag = Pan
- Scroll wheel = Zoom at center
- Left click = Selection/interaction
- Space + drag = Pan (alternative)

### 4. Canvas Renderer

**Responsibilities:**
- Manage HTML5 canvas element
- Handle canvas resizing
- Provide rendering context
- Clear and redraw operations

**Key Functions:**
```javascript
const initCanvas = (container) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // Set up high-DPI support
    const dpr = window.devicePixelRatio || 1;
    canvas.width = container.clientWidth * dpr;
    canvas.height = container.clientHeight * dpr;
    canvas.style.width = container.clientWidth + 'px';
    canvas.style.height = container.clientHeight + 'px';
    ctx.scale(dpr, dpr);
    
    container.appendChild(canvas);
    return { canvas, ctx };
};
```

## Data Models

### Viewport State
```javascript
const ViewportState = {
    panX: 0,           // Pan offset X (screen pixels)
    panY: 0,           // Pan offset Y (screen pixels)
    zoom: 1,           // Zoom level (0.1 to 10)
    width: 800,        // Canvas width (screen pixels)
    height: 600        // Canvas height (screen pixels)
};
```

### Canvas Configuration
```javascript
const CanvasOptions = {
    controlStyle: 'google-maps',  // 'google-maps' | 'logseq'
    minZoom: 0.1,                 // Minimum zoom level
    maxZoom: 10,                  // Maximum zoom level
    panBounds: null,              // Optional pan boundaries
    enableKeyboard: true,         // Enable keyboard controls
    enableTouch: true,            // Enable touch gestures
    smoothing: true,              // Enable smooth pan/zoom
    debugMode: false              // Show debug information
};
```

### Event Data
```javascript
const CanvasEvent = {
    type: 'viewport-changed',     // Event type
    viewport: ViewportState,      // Current viewport
    worldBounds: {                // Visible world bounds
        left: -100, top: -100,
        right: 100, bottom: 100
    },
    pointer: {                    // Pointer information
        screen: { x: 400, y: 300 },
        world: { x: 50, y: 25 }
    }
};
```

## Error Handling

### Graceful Degradation
- **Canvas Not Supported**: Fall back to div-based implementation
- **Touch Not Available**: Disable touch gestures, keep mouse
- **Performance Issues**: Reduce update frequency, disable smoothing
- **Memory Limits**: Implement viewport culling, content cleanup

### Error Recovery
```javascript
const handleCanvasError = (error) => {
    console.warn('Canvas error:', error);
    
    // Try to recover
    if (error.type === 'context-lost') {
        reinitializeCanvas();
    } else if (error.type === 'memory-limit') {
        enableViewportCulling();
    }
    
    // Emit error event for debugging
    emitEvent('canvas:error', { error, recovery: 'attempted' });
};
```

## Testing Strategy

### Unit Tests
- **Transform Functions**: Screen/world coordinate conversion accuracy
- **Viewport Logic**: Pan/zoom bounds checking and state updates
- **Event Handling**: Mouse, touch, and keyboard event processing
- **Performance**: Frame rate measurement during interactions

### Integration Tests
- **Event System**: Canvas events properly emitted and received
- **Multi-Canvas**: Multiple instances work independently
- **State Persistence**: Viewport state save/restore functionality
- **Browser Compatibility**: Cross-browser canvas behavior

### Performance Tests
- **Frame Rate**: Maintain 60fps during pan/zoom operations
- **Memory Usage**: No memory leaks during extended use
- **Large Coordinates**: Performance with extreme coordinate values
- **Viewport Culling**: Efficiency with large amounts of off-screen content

### Manual Testing
- **User Experience**: Natural feeling pan/zoom interactions
- **Touch Gestures**: Smooth pinch-to-zoom on mobile devices
- **Control Styles**: Both Google Maps and Logseq styles work correctly
- **Edge Cases**: Behavior at zoom limits and coordinate extremes

## Performance Optimizations

### Rendering Optimizations
- **Viewport Culling**: Only render visible content
- **Transform Caching**: Cache transformation matrices
- **RequestAnimationFrame**: Smooth 60fps updates
- **High-DPI Support**: Efficient pixel ratio handling

### Memory Management
- **Event Cleanup**: Remove listeners on destroy
- **Canvas Cleanup**: Properly dispose of canvas contexts
- **State Cleanup**: Clear cached data when not needed
- **Garbage Collection**: Minimize object creation in hot paths

### Interaction Optimizations
- **Event Throttling**: Limit update frequency during rapid interactions
- **Smooth Interpolation**: Use easing for natural feeling movement
- **Touch Optimization**: Efficient touch event handling
- **Keyboard Shortcuts**: Fast keyboard-based navigation

## Integration Points

### Event System Integration
```javascript
// Emit canvas events
emitEvent('canvas:viewport-changed', {
    panX: state.panX,
    panY: state.panY,
    zoom: state.zoom,
    bounds: getVisibleBounds()
});

// Listen for external events
onEvent('canvas:focus-point', (data) => {
    panToPoint(data.worldX, data.worldY);
});
```

### Package Integration
- **Whiteboard Package**: Provides spatial drawing surface
- **Mind Map Package**: Enables node positioning and connections
- **Block System**: Can render blocks at world coordinates
- **Search System**: Can highlight search results spatially

### External Integration
- **Persistence**: Save/restore viewport state
- **Analytics**: Track user navigation patterns
- **Accessibility**: Keyboard navigation support
- **Mobile**: Touch gesture optimization

This design provides a solid foundation for spatial applications while maintaining the project's functional programming principles and performance requirements.