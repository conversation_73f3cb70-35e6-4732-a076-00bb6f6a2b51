# Requirements Document - Logseq Modular Packages

## Introduction

This project aims to reverse-engineer Logseq into the smallest possible modular "lego brick" packages using vanilla JavaScript, CSS, and HTML. Each package should be independent, reusable, and follow the KISS (Keep It Simple Stupid) principle. The packages will be used across the Home Repo (HR) ecosystem and should maintain compatibility with future Logseq features where possible.

## Requirements

### Requirement 1: Modular Architecture

**User Story:** As a developer, I want each feature to be a separate package, so that I can use individual components in different parts of the HR ecosystem without duplicating code.

#### Acceptance Criteria

1. WHEN a package is created THEN it SHALL be completely independent with no external dependencies
2. WHEN a package is used THEN it SHALL work without requiring other packages (except explicitly declared dependencies)
3. WHEN packages communicate THEN they SHALL use the event system for loose coupling
4. IF a package needs another package THEN it SHALL declare this dependency clearly in its README

### Requirement 2: Vanilla Technology Stack

**User Story:** As a developer, I want to use only vanilla JavaScript/CSS/HTML, so that the codebase remains simple, maintainable, and free from framework dependencies.

#### Acceptance Criteria

1. WHEN code is written THEN it SHALL use only vanilla JavaScript (ES6+)
2. WHEN styling is applied THEN it SHALL use only plain CSS (no preprocessors)
3. WHEN markup is created THEN it SHALL use semantic HTML elements
4. WHEN the project is built THEN it SHALL require no build step or compilation
5. WHEN dependencies are considered THEN external dependencies SHALL be avoided

### Requirement 3: Semantic HTML Elements

**User Story:** As a developer, I want to use semantic HTML element names like `<block-item>` instead of `<div class="block-item">`, so that the code is self-documenting and easier to understand.

#### Acceptance Criteria

1. WHEN creating HTML elements THEN semantic names SHALL be used (e.g., `<block-item>`, `<button-icon>`)
2. WHEN styling elements THEN CSS selectors SHALL target semantic names directly
3. WHEN elements are complex THEN web components MAY be used if there is significant advantage
4. WHEN naming elements THEN names SHALL be descriptive and self-documenting

### Requirement 4: Block-Based Core System

**User Story:** As a user, I want all content to be organized as blocks with hierarchical relationships, so that I can create nested structures like in Logseq.

#### Acceptance Criteria

1. WHEN content is created THEN it SHALL be stored as a Block with unique ID
2. WHEN blocks are related THEN they SHALL support parent-child relationships
3. WHEN block content changes THEN references SHALL be automatically extracted ([[page]], ((block)), #tag)
4. WHEN blocks are manipulated THEN the system SHALL maintain referential integrity
5. WHEN blocks are serialized THEN they SHALL support JSON export/import

### Requirement 5: Interactive Outliner Interface

**User Story:** As a user, I want to edit blocks in a hierarchical outliner with keyboard shortcuts, so that I can efficiently create and organize content.

#### Acceptance Criteria

1. WHEN I press Enter THEN a new block SHALL be created at the same level
2. WHEN I press Tab THEN the current block SHALL be indented (become child of previous block)
3. WHEN I press Shift+Tab THEN the current block SHALL be outdented (move up hierarchy)
4. WHEN I use arrow keys THEN I SHALL be able to navigate between blocks
5. WHEN I click a bullet THEN child blocks SHALL expand/collapse
6. WHEN I edit content THEN changes SHALL be auto-saved with configurable delay

### Requirement 6: Event-Driven Communication

**User Story:** As a developer, I want packages to communicate via events, so that components remain loosely coupled and can be easily swapped or extended.

#### Acceptance Criteria

1. WHEN packages need to communicate THEN they SHALL use the event system
2. WHEN events are emitted THEN they SHALL follow consistent naming conventions
3. WHEN events are processed THEN they SHALL support priority ordering
4. WHEN debugging is needed THEN the event system SHALL provide debug logging
5. WHEN events are namespaced THEN they SHALL use consistent namespace patterns

### Requirement 7: Reference System

**User Story:** As a user, I want to create links between content using [[page references]], ((block references)), and #tags, so that I can build a connected knowledge graph.

#### Acceptance Criteria

1. WHEN I type [[Page Name]] THEN it SHALL create a page reference
2. WHEN I type ((block-id)) THEN it SHALL create a block reference  
3. WHEN I type #hashtag THEN it SHALL create a tag reference
4. WHEN references are created THEN they SHALL be automatically extracted and stored
5. WHEN references are displayed THEN they SHALL be visually distinct and clickable

### Requirement 8: Multi-Platform Support

**User Story:** As a user, I want the system to work across web, desktop (Electron), and P2P (Pear) platforms, so that I can access my content anywhere.

#### Acceptance Criteria

1. WHEN deployed to web THEN it SHALL work in modern browsers
2. WHEN packaged for desktop THEN it SHALL work with Electron
3. WHEN used with P2P THEN it SHALL work with Pear runtime
4. WHEN switching platforms THEN data SHALL be compatible across all platforms
5. WHEN platform-specific features are needed THEN they SHALL be abstracted behind common interfaces

### Requirement 9: Database Flexibility

**User Story:** As a developer, I want to support multiple database backends, so that the system can work with different storage requirements (in-memory, SQLite, TerminusDB).

#### Acceptance Criteria

1. WHEN starting development THEN an in-memory store SHALL be used for MVP
2. WHEN persistence is needed THEN SQLite WASM SHALL be supported
3. WHEN HR integration is required THEN TerminusDB SHALL be supported
4. WHEN database backends are swapped THEN the API SHALL remain consistent
5. WHEN data is migrated THEN it SHALL maintain integrity across backends

### Requirement 10: Whiteboard/Canvas Integration

**User Story:** As a user, I want to arrange blocks and content on a spatial canvas, so that I can think visually and create mind maps or diagrams.

#### Acceptance Criteria

1. WHEN using the whiteboard THEN I SHALL be able to drag blocks from the outliner
2. WHEN on the canvas THEN I SHALL be able to pan and zoom smoothly
3. WHEN creating shapes THEN I SHALL have basic drawing tools (rectangle, circle, line)
4. WHEN connecting items THEN I SHALL be able to draw connectors between elements
5. WHEN the canvas is large THEN performance SHALL remain smooth with many elements

### Requirement 11: Search and Discovery

**User Story:** As a user, I want to search through all my content and find relevant blocks quickly, so that I can locate information efficiently.

#### Acceptance Criteria

1. WHEN I search for text THEN it SHALL find matches in block content
2. WHEN search results are shown THEN matching text SHALL be highlighted
3. WHEN searching references THEN it SHALL find blocks that link to specific pages/blocks
4. WHEN using fuzzy search THEN it SHALL handle typos and partial matches
5. WHEN search is performed THEN results SHALL be ranked by relevance

### Requirement 12: Future Logseq Compatibility

**User Story:** As a developer, I want to maintain compatibility with Logseq's evolution, so that we can benefit from their future improvements without major rewrites.

#### Acceptance Criteria

1. WHEN Logseq adds new features THEN our architecture SHALL accommodate similar functionality
2. WHEN APIs are designed THEN they SHALL be similar to Logseq's patterns where appropriate
3. WHEN data formats are chosen THEN they SHALL be compatible or easily convertible
4. WHEN plugin systems are built THEN they SHALL follow similar patterns to Logseq's plugin API
5. WHEN breaking changes are needed THEN migration paths SHALL be provided