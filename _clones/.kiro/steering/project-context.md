# Project Context - Logseq Modular Packages

## Project Overview

This project is part of the Home Repo (HR) ecosystem, located in the `_clones` subfolder to focus development scope. We are reverse-engineering Logseq into modular "lego brick" packages that can be used independently or combined to recreate the full Logseq experience.

## Key Principles

### KISS (Keep It Simple Stupid)
- No frameworks, no build step, no dependencies
- Pure vanilla JavaScript, CSS, and HTML
- Self-contained packages with clear interfaces

### Semantic HTML Approach
- Use `<block-item>` instead of `<div class="block-item">`
- Self-documenting code through element names
- Future-ready for web components if needed
- Examples: `<button-icon>`, `<block-content>`, `<block-bullet>`

### Event-Driven Architecture
- Packages communicate via pub/sub events
- Loose coupling enables easy swapping of components
- Debug mode for development and troubleshooting
- Consistent naming conventions (e.g., `block:changed`, `outliner:save`)

### Modular Design
- Each package is independent and reusable
- Clear dependencies declared in README files
- Can be used separately in other HR projects
- "Lego brick" philosophy - small, focused, combinable

## Technology Stack

### Core Technologies
- **JavaScript**: ES6+ vanilla JavaScript only
- **CSS**: Plain CSS with semantic selectors
- **HTML**: Semantic custom elements
- **Database**: Start with in-memory, migrate to SQLite WASM, support TerminusDB

### Platform Targets
- **Web**: Modern browsers
- **Desktop**: Electron wrapper
- **P2P**: Pear runtime support

## Package Architecture

### Naming Convention
- Package names: `fap-<feature-name>` (e.g., `fap-block-core`)
- Files within package:
  - `fap-<feature-name>.js` - Main implementation
  - `fap-<feature-name>.css` - Styling
  - `<feature-name>.html` - Demo/example
  - `README-<feature-name>.md` - Documentation

### Package Structure
```
fap-<feature-name>/
├── fap-<feature-name>.js     # Main implementation
├── fap-<feature-name>.css    # Styling
├── <feature-name>.html       # Interactive demo
└── README-<feature-name>.md  # Documentation
```

## Development Guidelines

### Code Style
- Use semantic variable and function names
- Include comprehensive comments
- Follow consistent indentation (2 spaces)
- Use modern JavaScript features (const/let, arrow functions, classes)

### CSS Approach
- Target semantic HTML elements directly
- Use CSS Grid and Flexbox for layouts
- Support responsive design and dark mode
- Include print-friendly styles

### Event Naming
- Format: `<namespace>:<action>` (e.g., `block:changed`)
- Common namespaces: `block`, `outliner`, `page`, `search`, `canvas`
- Common actions: `changed`, `created`, `deleted`, `focused`, `saved`

### Documentation
- Each package must have comprehensive README
- Include usage examples and API documentation
- Provide interactive HTML demos
- Document integration points with other packages

## Integration with Home Repo

### Reusability Goals
- Whiteboard package can be used in other HR applications
- Infinite canvas can power various spatial interfaces
- Event system can coordinate between HR components
- Block system can structure content anywhere in HR

### Data Compatibility
- JSON serialization for data exchange
- Compatible with HR's TerminusDB when ready
- Migration paths between storage backends
- Export/import functionality for data portability

## Current Status

### ✅ Completed Packages (3/12+) - All Fully Functional
1. **fap-event-system** - Pub/sub communication backbone with priority, namespacing, debug mode
2. **fap-block-core** - Block data structure with hierarchy, references, search, JSON serialization
3. **fap-outliner** - Complete interactive editing with keyboard nav, focus management, auto-save

### 🎯 Next Priority Packages
1. **fap-page-references** - [[Page]] linking and navigation system
2. **fap-infinite-canvas** - Base canvas for whiteboard functionality
3. **fap-search-engine** - Full-text search with highlighting
4. **fap-whiteboard** - Spatial thinking canvas (Logseq's signature feature)

### 🧪 Testing & Demos
- **Standards Demo**: `demo-fap-standard.html` with visual indicators and comprehensive logging
- **Original Demo**: `demo-combined.html` with full feature showcase
- **Playwright Integration**: MCP server setup for automated testing
- **Individual Demos**: Each package has interactive HTML demo

## Development Notes

### Session Management
- Use `_notes/logsec/ai-chat-history/` for session tracking
- **Naming convention established**:
  - User files: `user-<number>-<topic>.md`
  - AI files: `ai-<number>-<topic>.md`
  - Session summaries: `session-<number>-<topic>.md` (when needed)
- **Current session files**: user-1 through user-5, ai-1 through ai-6
- **README.md** in chat history folder provides navigation and overview

### Testing Strategy
- Interactive HTML demos for manual testing
- Event logging for debugging
- Real-world usage examples
- Performance monitoring in debug mode

### Performance Considerations
- Efficient DOM manipulation with element caching
- Debounced operations (auto-save, search)
- Event delegation for better performance
- Memory management with proper cleanup

## Future Considerations

### Plugin System
- Design packages to be extensible
- Consider plugin architecture similar to Logseq
- Maintain compatibility with Logseq's plugin patterns

### Collaborative Features
- Event system ready for real-time collaboration
- Conflict resolution strategies
- Multi-user editing indicators

### Advanced Features
- Undo/redo system
- Version control integration
- Advanced search with AI
- Custom themes and styling